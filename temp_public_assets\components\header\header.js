/**
 * FuseHeader JavaScript
 * 
 * Implementa la funcionalidad interactiva para el componente FuseHeader
 */

document.addEventListener('DOMContentLoaded', function() {
    initHeader();
});

/**
 * Inicializa todas las funcionalidades del header
 */
function initHeader() {
    initLanguageDropdown();
    initFullscreenToggle();
    initThemeToggle();
}

/**
 * Inicializa el menú desplegable de idiomas
 */
function initLanguageDropdown() {
    // Selector del botón de idioma
    const languageButton = document.querySelector('.language-dropdown button');
    // Selector del menú desplegable
    const dropdown = document.querySelector('.language-dropdown-content');
    // Selectores de todos los elementos del menú
    const languageItems = document.querySelectorAll('.language-item');
    
    if (!languageButton || !dropdown) return;

    // Mostrar/ocultar el menú al hacer clic en el botón
    languageButton.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdown.classList.toggle('hidden');
    });

    // Cerrar el menú al hacer clic fuera de él
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target) && !languageButton.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });

    // Manejar la selección de un idioma
    languageItems.forEach(item => {
        item.addEventListener('click', function() {
            const langCode = this.dataset.langCode;
            const langFlag = this.querySelector('span').textContent.trim();
            
            // Actualizar el botón con el idioma seleccionado
            languageButton.innerHTML = `${langFlag} ${langCode}`;
            
            // Cerrar el menú desplegable
            dropdown.classList.add('hidden');

            // Aquí se podría enviar una solicitud AJAX para cambiar el idioma en el servidor
            console.log(`Idioma cambiado a: ${langCode}`);
        });
    });
}

/**
 * Inicializa el botón de pantalla completa
 */
function initFullscreenToggle() {
    const fullscreenButton = document.querySelector('button svg[d*="M3.75 3.75v4.5"]').parentElement;
    
    if (!fullscreenButton) return;

    fullscreenButton.addEventListener('click', function() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.error(`Error al intentar entrar en modo pantalla completa: ${err.message}`);
            });
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
    });
}

/**
 * Inicializa el botón de cambio de tema (claro/oscuro)
 */
function initThemeToggle() {
    const themeButton = document.querySelector('button svg[d*="M12 3v2.25m6.364.386"]').parentElement;
    let darkMode = localStorage.getItem('darkMode') === 'true';
    
    if (!themeButton) return;

    // Aplicar tema guardado al cargar la página
    if (darkMode) {
        document.documentElement.classList.add('dark-theme');
    }

    themeButton.addEventListener('click', function() {
        darkMode = !darkMode;
        
        if (darkMode) {
            document.documentElement.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
        }
        
        // Guardar preferencia
        localStorage.setItem('darkMode', darkMode);
    });
}
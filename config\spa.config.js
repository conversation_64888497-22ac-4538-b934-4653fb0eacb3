/**
 * SPA Configuration
 * 
 * Central configuration for the Single Page Application
 */

export default {
    // Application Settings
    app: {
        name: 'ProyectDash',
        version: '2.0.0',
        environment: process.env.NODE_ENV || 'development',
        debug: process.env.NODE_ENV !== 'production'
    },

    // API Configuration
    api: {
        baseURL: process.env.API_URL || '/api/v1',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        cache: {
            enabled: true,
            ttl: 5 * 60 * 1000 // 5 minutes
        }
    },

    // Router Configuration
    router: {
        mode: 'history', // 'history' or 'hash'
        base: '/',
        linkActiveClass: 'active',
        linkExactActiveClass: 'exact-active',
        scrollBehavior: 'smooth',
        transitions: {
            default: {
                enter: 'fade-in',
                leave: 'fade-out',
                duration: 300
            }
        }
    },

    // Store Configuration
    store: {
        strict: process.env.NODE_ENV !== 'production',
        devtools: process.env.NODE_ENV !== 'production',
        plugins: [],
        modules: {
            // Module configuration
            app: {
                namespaced: true,
                persist: ['theme', 'language']
            },
            user: {
                namespaced: true,
                persist: ['token']
            },
            navigation: {
                namespaced: true,
                persist: false
            },
            ui: {
                namespaced: true,
                persist: ['sidebarOpen', 'theme']
            }
        },
        persistence: {
            key: 'proyectdash-state',
            storage: window.localStorage,
            reducer: (state) => ({
                // Only persist specific parts
                user: {
                    token: state.user.token
                },
                ui: {
                    theme: state.ui.theme,
                    sidebarOpen: state.ui.sidebarOpen
                }
            })
        }
    },

    // Authentication Configuration
    auth: {
        tokenKey: 'auth_token',
        tokenType: 'Bearer',
        loginRoute: '/login',
        logoutRoute: '/login',
        defaultRoute: '/dashboard',
        refreshInterval: 30 * 60 * 1000, // 30 minutes
        guards: {
            auth: (to, from, store) => {
                if (!store.state.user.isAuthenticated) {
                    return '/login';
                }
            },
            guest: (to, from, store) => {
                if (store.state.user.isAuthenticated) {
                    return '/dashboard';
                }
            }
        }
    },

    // UI Configuration
    ui: {
        theme: {
            default: 'light',
            available: ['light', 'dark'],
            autoDetect: true
        },
        sidebar: {
            defaultOpen: true,
            collapsible: true,
            width: '260px',
            collapsedWidth: '64px'
        },
        notifications: {
            position: 'top-right',
            duration: 5000,
            maxVisible: 3
        },
        transitions: {
            duration: 200,
            easing: 'ease-in-out'
        }
    },

    // Feature Flags
    features: {
        // Enable/disable features for gradual rollout
        spa: true,
        newApi: true,
        stateManagement: true,
        darkMode: true,
        offlineMode: true,
        analytics: true,
        debugMode: process.env.NODE_ENV !== 'production'
    },

    // Performance Configuration
    performance: {
        enablePrefetch: true,
        enableLazyLoading: true,
        chunkSize: 244 * 1024, // 244 KB
        imageLazyLoading: true,
        bundleAnalyzer: process.env.NODE_ENV !== 'production'
    },

    // Development Tools
    dev: {
        enableVueDevtools: true,
        enableReduxDevtools: true,
        logLevel: 'debug', // 'debug', 'info', 'warn', 'error'
        mockApi: false,
        hotReload: true
    },

    // Analytics Configuration
    analytics: {
        enabled: process.env.NODE_ENV === 'production',
        trackingId: process.env.GA_TRACKING_ID,
        trackPageViews: true,
        trackEvents: true,
        trackErrors: true
    },

    // Error Handling
    errorHandling: {
        showErrorDetails: process.env.NODE_ENV !== 'production',
        logToConsole: true,
        logToServer: process.env.NODE_ENV === 'production',
        errorEndpoint: '/api/v1/errors',
        ignoredErrors: [
            'ResizeObserver loop limit exceeded',
            'Non-Error promise rejection captured'
        ]
    },

    // Localization
    i18n: {
        defaultLocale: 'en',
        fallbackLocale: 'en',
        availableLocales: ['en', 'es', 'fr', 'de'],
        loadPath: '/locales/{locale}.json',
        cacheBusting: true
    },

    // Security Configuration
    security: {
        enableCSP: true,
        cspDirectives: {
            'default-src': ["'self'"],
            'script-src': ["'self'", "'unsafe-inline'", 'cdn.jsdelivr.net'],
            'style-src': ["'self'", "'unsafe-inline'", 'cdn.jsdelivr.net'],
            'img-src': ["'self'", 'data:', 'https:'],
            'connect-src': ["'self'", 'https://api.example.com']
        },
        enableXSSProtection: true,
        enableFrameOptions: true,
        frameOptions: 'SAMEORIGIN'
    }
};

// Helper function to get nested config value
export function getConfig(path, defaultValue = null) {
    const keys = path.split('.');
    let value = config;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
}

// Helper function to check if feature is enabled
export function isFeatureEnabled(feature) {
    return config.features[feature] === true;
}

// Helper function to get API endpoint
export function getApiEndpoint(endpoint) {
    return config.api.baseURL + (endpoint.startsWith('/') ? endpoint : '/' + endpoint);
}
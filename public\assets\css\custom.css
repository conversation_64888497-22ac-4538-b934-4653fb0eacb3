/**
 * Estilos personalizados para el SPA
 */

/* Estilos para el sidebar con nueva paleta de colores */
:root {
    --app-primary: #0ea5e9;       /* Sky-500 */
    --app-primary-hover: #0284c7; /* Sky-600 */
    --app-primary-light: #e0f2fe; /* Sky-100 */
    --app-secondary: #64748b;     /* Slate-500 */
    --app-secondary-light: #f1f5f9; /* Slate-100 */
    --app-background: #ffffff;
    --app-background-alt: #f8fafc; /* Slate-50 */
    --app-border: #e2e8f0;        /* Slate-200 */
    --app-text-primary: #1e293b;  /* Slate-800 */
    --app-text-secondary: #64748b; /* Slate-500 */
    --app-text-muted: #94a3b8;     /* Slate-400 */
}

/* Redefinir colores de elementos específicos */
.card {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transform: translateY(-2px);
}

.badge-success {
    background-color: #dcfce7;
    color: #15803d;
}

.badge-warning {
    background-color: #fef9c3;
    color: #854d0e;
}

.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

.badge-info {
    background-color: #e0f2fe;
    color: #0369a1;
}

/* Botones con colores actualizados */
.btn-primary {
    background-color: var(--app-primary);
    color: white;
    border: 1px solid var(--app-primary-hover);
}

.btn-primary:hover {
    background-color: var(--app-primary-hover);
}

.btn-secondary {
    background-color: var(--app-secondary-light);
    color: var(--app-text-secondary);
    border: 1px solid var(--app-border);
}

.btn-secondary:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
}

.btn-secondary.active {
    background-color: var(--app-primary-light);
    color: var(--app-primary);
    border-color: #bae6fd;
}

/* Asegurar que el sidebar no se duplique */
#sidebar-container {
    z-index: 50;
    position: relative;
}

/* Eliminar sidebars anidados (para evitar duplicados) */
.page-content .fuse-sidebar,
div[id^="fuse-sidebar-"],
#main-content div.fuse-sidebar,
div.main-content-inner div.fuse-sidebar,
script[src*="sidebar.js"] + script[src*="sidebar.js"] {
    display: none !important;
}

/* Prevenir duplicación de contenido */
.page-content .fuse-sidebar-content,
.page-content .fuse-sidebar-header,
.page-content .fuse-sidebar-footer,
#main-content div.fuse-sidebar-content,
#main-content div.fuse-sidebar-header,
#main-content div.fuse-sidebar-footer {
    display: none !important;
}

/* Evitar doble carga del sidebar cuando estamos en /project/ */
.project-page #sidebar-container:not(:first-of-type) {
    display: none !important;
}

/* Estilos para tablas */
.data-table th {
    background-color: var(--app-secondary-light);
    color: var(--app-text-secondary);
    font-weight: 600;
}

.data-table tbody tr:hover {
    background-color: var(--app-secondary-light);
}

/* Estilos para gráficos */
.chart-container {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
}

/* Estilos para el calendario */
.calendar-day {
    background-color: white;
}

.calendar-day.bg-blue-50 {
    background-color: var(--app-primary-light) !important;
}

.calendar-day-header.font-bold.text-blue-600 {
    color: var(--app-primary) !important;
}

.calendar-event.bg-blue-100 {
    background-color: #bae6fd !important;
    border-left-color: var(--app-primary) !important;
}

/* Indicador de carga */
.loader {
    border-top-color: var(--app-primary);
}
/**
 * Enhanced Sidebar JavaScript for SPA Navigation
 * 
 * Provides sidebar functionality with SPA navigation support
 * 
 * <AUTHOR> with Claude Code
 * @version 2.0
 */

(function() {
    'use strict';
    
    // Sidebar instance
    let sidebarInstance = null;
    
    /**
     * Sidebar class
     */
    class FuseSidebar {
        constructor(config = {}) {
            this.config = {
                activeItem: config.activeItem || 'project',
                navigationMode: config.navigationMode || 'spa',
                baseUrl: config.baseUrl || '/',
                onItemClick: config.onItemClick || null,
                debug: config.debug || false,
                transitionDuration: 200
            };
            
            this.elements = {
                sidebar: null,
                items: [],
                activeItem: null
            };
            
            this.state = {
                activeItem: this.config.activeItem,
                isTransitioning: false
            };
            
            this.init();
        }
        
        /**
         * Initialize sidebar
         */
        init() {
            this.log('Initializing sidebar', this.config);
            
            // Find sidebar element
            this.elements.sidebar = document.querySelector('.fuse-sidebar');
            if (!this.elements.sidebar) {
                console.error('Sidebar element not found');
                return;
            }
            
            // Find all sidebar items
            this.elements.items = Array.from(
                this.elements.sidebar.querySelectorAll('.fuse-sidebar-item')
            );
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Set initial active item
            this.setActiveItem(this.state.activeItem);
            
            // Setup keyboard navigation
            this.setupKeyboardNavigation();
            
            this.log('Sidebar initialized');
        }
        
        /**
         * Setup event listeners
         */
        setupEventListeners() {
            // Item click handlers
            this.elements.items.forEach(item => {
                item.addEventListener('click', (e) => this.handleItemClick(e));
                
                // Add hover effects
                item.addEventListener('mouseenter', () => this.handleItemHover(item, true));
                item.addEventListener('mouseleave', () => this.handleItemHover(item, false));
            });
            
            // View documentation button
            const docsBtn = this.elements.sidebar.querySelector('.view-docs-btn');
            if (docsBtn) {
                docsBtn.addEventListener('click', () => {
                    window.open('https://docs.example.com', '_blank');
                });
            }
            
            // Listen for external navigation events
            window.addEventListener('navigation-complete', (e) => {
                if (e.detail && e.detail.page) {
                    this.setActiveItem(e.detail.page);
                }
            });
        }
        
        /**
         * Handle item click
         * 
         * @param {Event} event Click event
         */
        handleItemClick(event) {
            event.preventDefault();
            
            const item = event.currentTarget;
            const itemId = item.dataset.itemId;
            
            if (!itemId || this.state.isTransitioning) {
                return;
            }
            
            this.log('Item clicked:', itemId);
            
            // Update active state immediately for better UX
            this.setActiveItem(itemId);
            
            // Handle navigation based on mode
            if (this.config.navigationMode === 'spa') {
                this.navigateSPA(itemId);
            } else {
                this.navigateTraditional(itemId);
            }
        }
        
        /**
         * Handle item hover
         * 
         * @param {HTMLElement} item Item element
         * @param {boolean} isHovering Whether hovering
         */
        handleItemHover(item, isHovering) {
            const hoverElement = item.querySelector('.fuse-sidebar-hover');
            if (hoverElement) {
                hoverElement.style.opacity = isHovering ? '1' : '0';
            }
        }
        
        /**
         * Navigate using SPA mode
         * 
         * @param {string} itemId Item ID
         */
        navigateSPA(itemId) {
            // Use custom callback if provided
            if (this.config.onItemClick) {
                this.config.onItemClick(itemId);
                return;
            }
            
            // Emit custom event for navigation system
            const event = new CustomEvent('fuse-sidebar-navigation', {
                detail: { itemId },
                bubbles: true
            });
            window.dispatchEvent(event);
            
            // Fallback to navigation handler
            if (typeof window.handleNavigation === 'function') {
                window.handleNavigation(itemId);
            }
        }
        
        /**
         * Navigate using traditional mode
         * 
         * @param {string} itemId Item ID
         */
        navigateTraditional(itemId) {
            const url = new URL(window.location);
            url.searchParams.set('page', itemId);
            window.location.href = url.toString();
        }
        
        /**
         * Set active item
         * 
         * @param {string} itemId Item ID
         */
        setActiveItem(itemId) {
            if (this.state.activeItem === itemId) {
                return;
            }
            
            this.log('Setting active item:', itemId);
            
            // Start transition
            this.state.isTransitioning = true;
            
            // Find the item
            const newActiveItem = this.elements.items.find(
                item => item.dataset.itemId === itemId
            );
            
            if (!newActiveItem) {
                this.log('Item not found:', itemId);
                this.state.isTransitioning = false;
                return;
            }
            
            // Remove active state from current item
            if (this.elements.activeItem) {
                this.removeActiveState(this.elements.activeItem);
            }
            
            // Add active state to new item
            this.addActiveState(newActiveItem);
            
            // Update state
            this.state.activeItem = itemId;
            this.elements.activeItem = newActiveItem;
            
            // End transition after animation
            setTimeout(() => {
                this.state.isTransitioning = false;
            }, this.config.transitionDuration);
        }
        
        /**
         * Add active state to item
         * 
         * @param {HTMLElement} item Item element
         */
        addActiveState(item) {
            // Add transitioning class
            item.classList.add('transitioning');
            
            // Add active classes
            setTimeout(() => {
                item.classList.add('bg-sky-50', 'text-sky-600', 'shadow-sm');
                item.classList.remove('text-gray-600', 'hover:bg-gray-50');
                
                // Update icon color
                const icon = item.querySelector('.w-5.h-5');
                if (icon) {
                    icon.classList.add('text-sky-500');
                    icon.classList.remove('text-gray-400');
                }
                
                // Show chevron
                const chevron = item.querySelector('.transition-all.duration-200.flex-shrink-0');
                if (chevron) {
                    chevron.classList.add('opacity-100', 'translate-x-0');
                    chevron.classList.remove('opacity-0', '-translate-x-2');
                }
                
                // Remove transitioning class
                setTimeout(() => {
                    item.classList.remove('transitioning');
                }, this.config.transitionDuration);
            }, 10);
        }
        
        /**
         * Remove active state from item
         * 
         * @param {HTMLElement} item Item element
         */
        removeActiveState(item) {
            item.classList.remove('bg-sky-50', 'text-sky-600', 'shadow-sm');
            item.classList.add('text-gray-600', 'hover:bg-gray-50');
            
            // Update icon color
            const icon = item.querySelector('.w-5.h-5');
            if (icon) {
                icon.classList.remove('text-sky-500');
                icon.classList.add('text-gray-400');
            }
            
            // Hide chevron
            const chevron = item.querySelector('.transition-all.duration-200.flex-shrink-0');
            if (chevron) {
                chevron.classList.remove('opacity-100', 'translate-x-0');
                chevron.classList.add('opacity-0', '-translate-x-2');
            }
        }
        
        /**
         * Setup keyboard navigation
         */
        setupKeyboardNavigation() {
            document.addEventListener('keydown', (e) => {
                // Only handle if sidebar is focused or no input is focused
                const activeElement = document.activeElement;
                if (activeElement.tagName === 'INPUT' || 
                    activeElement.tagName === 'TEXTAREA') {
                    return;
                }
                
                switch (e.key) {
                    case 'ArrowUp':
                        e.preventDefault();
                        this.navigateToPreviousItem();
                        break;
                    case 'ArrowDown':
                        e.preventDefault();
                        this.navigateToNextItem();
                        break;
                    case 'Enter':
                        if (this.elements.activeItem) {
                            this.elements.activeItem.click();
                        }
                        break;
                }
            });
        }
        
        /**
         * Navigate to previous item
         */
        navigateToPreviousItem() {
            const currentIndex = this.elements.items.indexOf(this.elements.activeItem);
            if (currentIndex > 0) {
                const previousItem = this.elements.items[currentIndex - 1];
                this.setActiveItem(previousItem.dataset.itemId);
            }
        }
        
        /**
         * Navigate to next item
         */
        navigateToNextItem() {
            const currentIndex = this.elements.items.indexOf(this.elements.activeItem);
            if (currentIndex < this.elements.items.length - 1) {
                const nextItem = this.elements.items[currentIndex + 1];
                this.setActiveItem(nextItem.dataset.itemId);
            }
        }
        
        /**
         * Get active item
         * 
         * @returns {string} Active item ID
         */
        getActiveItem() {
            return this.state.activeItem;
        }
        
        /**
         * Destroy sidebar instance
         */
        destroy() {
            // Remove event listeners
            this.elements.items.forEach(item => {
                item.replaceWith(item.cloneNode(true));
            });
            
            // Clear references
            this.elements = {
                sidebar: null,
                items: [],
                activeItem: null
            };
            
            this.log('Sidebar destroyed');
        }
        
        /**
         * Log debug message
         * 
         * @param {...any} args
         */
        log(...args) {
            if (this.config.debug) {
                console.log('[FuseSidebar]', ...args);
            }
        }
    }
    
    /**
     * Initialize sidebar
     * 
     * @param {Object} config Configuration
     * @returns {FuseSidebar} Sidebar instance
     */
    function initFuseSidebar(config) {
        // Destroy existing instance if any
        if (sidebarInstance) {
            sidebarInstance.destroy();
        }
        
        // Create new instance
        sidebarInstance = new FuseSidebar(config);
        
        return sidebarInstance;
    }
    
    // Export to global scope
    window.initFuseSidebar = initFuseSidebar;
    window.fuseSidebarInstance = sidebarInstance;
    
})();
<?php
/**
 * Navigation API Endpoint
 * 
 * Handles all SPA navigation requests via AJAX
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

// Start session
session_start();

// Set headers for AJAX
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Include navigation manager
require_once __DIR__ . '/../../core/NavigationManager.php';

use Core\NavigationManager;

try {
    // Get request data
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $page = $_GET['page'] ?? $_POST['page'] ?? null;
    $action = $_GET['action'] ?? $_POST['action'] ?? 'navigate';
    
    // Initialize navigation manager
    $currentPage = $_SESSION['current_page'] ?? 'project';
    $navigation = new NavigationManager($currentPage);
    
    // Handle different actions
    switch ($action) {
        case 'navigate':
            if (!$page) {
                throw new Exception('Page parameter is required');
            }
            
            // Validate page
            if (!in_array($page, $navigation->getValidPages())) {
                throw new Exception('Invalid page requested');
            }
            
            // Perform navigation
            $response = $navigation->navigate($page);
            
            // Add additional data
            $response['menu'] = $navigation->getMenuItems();
            $response['preloadPages'] = $navigation->getPreloadPages();
            
            echo json_encode($response);
            break;
            
        case 'preload':
            // Get pages to preload
            $preloadPages = $navigation->getPreloadPages();
            $preloadData = [];
            
            foreach ($preloadPages as $page) {
                $navigation = new NavigationManager($page);
                $preloadData[$page] = [
                    'title' => $navigation->getPageTitle(),
                    'scripts' => $navigation->getPageScripts(),
                    'styles' => $navigation->getPageStyles(),
                    'cache' => $navigation->shouldCachePage($page)
                ];
            }
            
            echo json_encode([
                'success' => true,
                'pages' => $preloadData
            ]);
            break;
            
        case 'state':
            // Get current navigation state
            echo json_encode([
                'success' => true,
                'state' => $navigation->getClientState(),
                'menu' => $navigation->getMenuItems()
            ]);
            break;
            
        case 'validate':
            // Validate a page
            if (!$page) {
                throw new Exception('Page parameter is required');
            }
            
            $isValid = in_array($page, $navigation->getValidPages());
            
            echo json_encode([
                'success' => true,
                'valid' => $isValid,
                'page' => $page
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    // Handle errors
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'page' => $page ?? null
    ]);
}
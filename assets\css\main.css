/**
 * Archivo principal CSS que importa todos los módulos CSS
 */

/* Base */
@import url('base/base.css');

/* Layout */
@import url('layout/layout.css');

/* Theme */
@import url('theme/theme.css');

/* Utilities */
@import url('utilities/utilities.css');

/* Componentes */
@import url('../components/header/header.css');
@import url('../components/sidebar/sidebar.css');

/* Modules - Añadir cuando se creen */
/* @import url('modules/forms.css'); */
/* @import url('modules/tables.css'); */
/* @import url('modules/buttons.css'); */

/* Responsive - Clases específicas para distintos breakpoints */
@media (max-width: 768px) {
    /* Estilos móviles */
}

@media (min-width: 769px) and (max-width: 1024px) {
    /* Estilos para tablets */
}

@media (min-width: 1025px) {
    /* Estilos desktop */
}
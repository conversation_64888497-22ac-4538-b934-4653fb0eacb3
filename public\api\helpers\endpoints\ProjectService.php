<?php
/**
 * Servicio para la gestión de proyectos
 * 
 * Proporciona métodos para interactuar con los endpoints de proyectos de la API
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

// Requerir el modelo de proyecto
require_once __DIR__ . '/../models/Project.php';

class ProjectService {
    /**
     * Cliente de API
     * @var ApiClient
     */
    private $apiClient;
    
    /**
     * Base del endpoint de proyectos
     * @var string
     */
    private $endpoint = 'projects';
    
    /**
     * Constructor
     * 
     * @param ApiClient $apiClient Cliente de API para realizar peticiones
     */
    public function __construct(ApiClient $apiClient) {
        $this->apiClient = $apiClient;
    }
    
    /**
     * Obtiene la lista de todos los proyectos
     * 
     * @param array $filters Filtros para la consulta (estado, etiquetas, etc)
     * @param int $page Número de página para paginación
     * @param int $limit Número de elementos por página
     * @return array Lista de objetos Project
     * @throws Exception Si hay un error en la petición
     */
    public function getProjects($filters = [], $page = 1, $limit = 10) {
        $params = array_merge($filters, [
            'page' => $page,
            'limit' => $limit
        ]);
        
        $response = $this->apiClient->get($this->endpoint, $params);
        
        $projects = [];
        if (isset($response['data']) && is_array($response['data'])) {
            foreach ($response['data'] as $projectData) {
                $projects[] = Project::fromApiResponse($projectData);
            }
        }
        
        return [
            'data' => $projects,
            'total' => $response['total'] ?? count($projects),
            'page' => $response['page'] ?? $page,
            'limit' => $response['limit'] ?? $limit,
            'pages' => $response['pages'] ?? ceil(($response['total'] ?? count($projects)) / $limit)
        ];
    }
    
    /**
     * Obtiene un proyecto por su ID
     * 
     * @param int $id ID del proyecto
     * @return Project Objeto Project
     * @throws Exception Si hay un error en la petición o el proyecto no existe
     */
    public function getProject($id) {
        $response = $this->apiClient->get("{$this->endpoint}/{$id}");
        
        if (!isset($response['data'])) {
            throw new Exception("El proyecto con ID {$id} no existe");
        }
        
        return Project::fromApiResponse($response['data']);
    }
    
    /**
     * Crea un nuevo proyecto
     * 
     * @param Project $project Datos del proyecto a crear
     * @return Project Proyecto creado con ID asignado
     * @throws Exception Si hay un error en la petición
     */
    public function createProject(Project $project) {
        $response = $this->apiClient->post($this->endpoint, $project->toApiRequest());
        
        if (!isset($response['data'])) {
            throw new Exception("Error al crear el proyecto");
        }
        
        return Project::fromApiResponse($response['data']);
    }
    
    /**
     * Actualiza un proyecto existente
     * 
     * @param Project $project Proyecto con datos actualizados
     * @return Project Proyecto actualizado
     * @throws Exception Si hay un error en la petición o el proyecto no existe
     */
    public function updateProject(Project $project) {
        if (!$project->id) {
            throw new Exception("El proyecto debe tener un ID para actualizarlo");
        }
        
        $response = $this->apiClient->put("{$this->endpoint}/{$project->id}", $project->toApiRequest());
        
        if (!isset($response['data'])) {
            throw new Exception("Error al actualizar el proyecto con ID {$project->id}");
        }
        
        return Project::fromApiResponse($response['data']);
    }
    
    /**
     * Elimina un proyecto
     * 
     * @param int $id ID del proyecto a eliminar
     * @return bool true si se eliminó correctamente
     * @throws Exception Si hay un error en la petición o el proyecto no existe
     */
    public function deleteProject($id) {
        $response = $this->apiClient->delete("{$this->endpoint}/{$id}");
        
        if (!isset($response['success']) || $response['success'] !== true) {
            throw new Exception("Error al eliminar el proyecto con ID {$id}");
        }
        
        return true;
    }
    
    /**
     * Obtiene las etiquetas disponibles para proyectos
     * 
     * @return array Lista de etiquetas
     * @throws Exception Si hay un error en la petición
     */
    public function getProjectTags() {
        $response = $this->apiClient->get("{$this->endpoint}/tags");
        
        if (!isset($response['data'])) {
            throw new Exception("Error al obtener las etiquetas de proyectos");
        }
        
        return $response['data'];
    }
    
    /**
     * Obtiene estadísticas de proyectos
     * 
     * @param array $filters Filtros para las estadísticas
     * @return array Estadísticas de proyectos
     * @throws Exception Si hay un error en la petición
     */
    public function getProjectStats($filters = []) {
        $response = $this->apiClient->get("{$this->endpoint}/stats", $filters);
        
        if (!isset($response['data'])) {
            throw new Exception("Error al obtener estadísticas de proyectos");
        }
        
        return $response['data'];
    }
}
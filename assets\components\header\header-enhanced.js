/**
 * Header Enhanced Functionality
 * 
 * Funcionalidad mejorada para el componente FuseHeader
 * - Canvas lateral de notificaciones
 * - Funcionalidad de pantalla completa
 * - Interacciones mejoradas
 */

class HeaderEnhanced {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    init() {
        if (this.isInitialized) {
            console.warn('HeaderEnhanced already initialized');
            return;
        }
        
        // Esperar a que el DOM esté completamente cargado
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
        } else {
            this.initializeComponents();
        }
    }
    
    initializeComponents() {
        try {
            console.log('🔧 Starting HeaderEnhanced initialization...');
            
            // Verificar que los elementos básicos existan
            this.validateRequiredElements();
            
            this.initNotifications();
            this.initFullscreen();
            this.initLanguageDropdown();
            this.initFullscreenListeners();
            this.initCanvasLateral();
            this.initEventListeners();
            this.isInitialized = true;
            console.log('✅ HeaderEnhanced initialized successfully');
            
            // Debug info
            console.log('📊 Header Debug Info:', {
                notificationToggle: !!this.notificationToggle,
                fullscreenToggle: !!this.fullscreenToggle,
                canvasLateralToggle: !!this.canvasLateralToggle,
                notificationsCanvas: !!this.notificationsCanvas,
                canvasLateral: !!this.canvasLateral
            });
            
        } catch (error) {
            console.error('❌ Error initializing HeaderEnhanced:', error);
            console.error('Stack trace:', error.stack);
        }
    }
    
    /**
     * Validar que los elementos requeridos existan
     */
    validateRequiredElements() {
        const requiredElements = [
            { selector: '.fullscreen-toggle', name: 'Fullscreen Toggle' },
            { selector: '.notification-toggle', name: 'Notification Toggle' },
            { selector: '.canvas-lateral-toggle', name: 'Canvas Lateral Toggle' },
            { selector: '#notificationsCanvas', name: 'Notifications Canvas' },
            { selector: '#notificationsOverlay', name: 'Notifications Overlay' },
            { selector: '#canvasLateral', name: 'Canvas Lateral' },
            { selector: '#canvasLateralOverlay', name: 'Canvas Lateral Overlay' }
        ];
        
        const missing = [];
        requiredElements.forEach(({ selector, name }) => {
            const element = document.querySelector(selector);
            if (!element) {
                missing.push(name + ' (' + selector + ')');
            }
        });
        
        if (missing.length > 0) {
            console.warn('⚠️ Missing elements:', missing);
            console.warn('🔍 Available buttons in DOM:', {
                fullscreenButtons: document.querySelectorAll('[class*="fullscreen"]').length,
                notificationButtons: document.querySelectorAll('[class*="notification"]').length,
                canvasButtons: document.querySelectorAll('[class*="canvas"]').length
            });
        } else {
            console.log('✅ All required elements found');
        }
    }

    /**
     * Inicializar funcionalidad de notificaciones
     */
    initNotifications() {
        this.notificationsCanvas = document.getElementById('notificationsCanvas');
        this.notificationsOverlay = document.getElementById('notificationsOverlay');
        this.notificationToggle = document.querySelector('.notification-toggle');
        this.closeNotifications = document.querySelector('.close-notifications');
        this.clearAllNotifications = document.querySelector('.clear-all-notifications');

        // Verificar si los elementos existen
        if (!this.notificationsCanvas || !this.notificationsOverlay || !this.notificationToggle) {
            console.warn('Elements for notifications not found');
            return;
        }
    }

    /**
     * Inicializar funcionalidad de pantalla completa
     */
    initFullscreen() {
        this.fullscreenToggle = document.querySelector('.fullscreen-toggle');
        this.fullscreenIcon = document.querySelector('.fullscreen-icon');
        this.fullscreenExitIcon = document.querySelector('.fullscreen-exit-icon');

        if (!this.fullscreenToggle) {
            console.warn('Fullscreen toggle button not found');
            return;
        }
        
        if (!this.fullscreenIcon || !this.fullscreenExitIcon) {
            console.warn('Fullscreen icons not found, creating fallback');
            this.createFullscreenIcons();
        }
        
        // Verificar estado inicial de pantalla completa
        this.updateFullscreenIcons();
    }
    
    /**
     * Crear iconos de pantalla completa si no existen
     */
    createFullscreenIcons() {
        if (!this.fullscreenToggle) return;
        
        // Limpiar contenido del botón
        this.fullscreenToggle.innerHTML = '';
        
        // Crear icono de pantalla completa
        const enterIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        enterIcon.classList.add('w-4', 'h-4', 'fullscreen-icon');
        enterIcon.setAttribute('fill', 'none');
        enterIcon.setAttribute('stroke', 'currentColor');
        enterIcon.setAttribute('viewBox', '0 0 24 24');
        enterIcon.setAttribute('stroke-width', '1.5');
        enterIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5M3.75 20.25v-4.5m0 4.5h4.5M20.25 3.75h-4.5m4.5 0v4.5M20.25 20.25h-4.5m4.5 0v-4.5" />';
        
        // Crear icono de salir de pantalla completa
        const exitIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        exitIcon.classList.add('w-4', 'h-4', 'fullscreen-exit-icon', 'hidden');
        exitIcon.setAttribute('fill', 'none');
        exitIcon.setAttribute('stroke', 'currentColor');
        exitIcon.setAttribute('viewBox', '0 0 24 24');
        exitIcon.setAttribute('stroke-width', '1.5');
        exitIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" d="M9 9V4.5M9 9H4.5M9 9L3.75 3.75M15 9h4.5M15 9V4.5M15 9l5.25-5.25M9 15v4.5M9 15H4.5M9 15l-5.25 5.25M15 15h4.5M15 15v4.5m0-4.5l5.25 5.25" />';
        
        this.fullscreenToggle.appendChild(enterIcon);
        this.fullscreenToggle.appendChild(exitIcon);
        
        // Actualizar referencias
        this.fullscreenIcon = enterIcon;
        this.fullscreenExitIcon = exitIcon;
    }

    /**
     * Inicializar dropdown de idioma
     */
    initLanguageDropdown() {
        this.languageDropdown = document.querySelector('.language-dropdown');
        this.languageButton = this.languageDropdown?.querySelector('button');
        this.languageContent = this.languageDropdown?.querySelector('.language-dropdown-content');

        if (this.languageButton && this.languageContent) {
            this.languageButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleLanguageDropdown();
            });
        }
    }

    /**
     * Inicializar todos los event listeners
     */
    initEventListeners() {
        console.log('🔗 Setting up event listeners...');
        
        // Event listeners para notificaciones
        if (this.notificationToggle) {
            console.log('✅ Adding notification toggle listener');
            this.notificationToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔔 Notification toggle clicked');
                this.toggleNotifications();
            });
        } else {
            console.warn('⚠️ Notification toggle not found');
        }

        if (this.closeNotifications) {
            console.log('✅ Adding close notifications listener');
            this.closeNotifications.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('❌ Close notifications clicked');
                this.closeNotificationsPanel();
            });
        }

        if (this.clearAllNotifications) {
            console.log('✅ Adding clear all notifications listener');
            this.clearAllNotifications.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🗑️ Clear all notifications clicked');
                this.clearAllNotificationsHandler();
            });
        }

        if (this.notificationsOverlay) {
            console.log('✅ Adding notifications overlay listener');
            this.notificationsOverlay.addEventListener('click', () => {
                console.log('📱 Notifications overlay clicked');
                this.closeNotificationsPanel();
            });
        }

        // Event listeners para pantalla completa
        if (this.fullscreenToggle) {
            console.log('✅ Adding fullscreen toggle listener');
            this.fullscreenToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔍 Fullscreen toggle clicked');
                this.toggleFullscreen();
            });
        } else {
            console.warn('⚠️ Fullscreen toggle not found');
        }

        // Event listeners para canvas lateral
        if (this.canvasLateralToggle) {
            console.log('✅ Adding canvas lateral toggle listener');
            this.canvasLateralToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('≡ Canvas lateral toggle clicked');
                this.toggleCanvasLateral();
            });
        } else {
            console.warn('⚠️ Canvas lateral toggle not found');
        }

        if (this.closeCanvasLateral) {
            console.log('✅ Adding close canvas lateral listener');
            this.closeCanvasLateral.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('❌ Close canvas lateral clicked');
                this.closeCanvasLateralPanel();
            });
        }

        if (this.canvasLateralOverlay) {
            console.log('✅ Adding canvas lateral overlay listener');
            this.canvasLateralOverlay.addEventListener('click', () => {
                console.log('📱 Canvas lateral overlay clicked');
                this.closeCanvasLateralPanel();
            });
        }

        // Cerrar paneles al hacer clic fuera
        document.addEventListener('click', (e) => {
            // Cerrar notificaciones si están abiertas
            if (this.notificationsCanvas && !this.notificationsCanvas.classList.contains('translate-x-full')) {
                if (!this.notificationsCanvas.contains(e.target) &&
                    !this.notificationToggle?.contains(e.target)) {
                    this.closeNotificationsPanel();
                }
            }

            // Cerrar canvas lateral si está abierto
            if (this.canvasLateral && !this.canvasLateral.classList.contains('translate-x-full')) {
                if (!this.canvasLateral.contains(e.target) &&
                    !this.canvasLateralToggle?.contains(e.target)) {
                    this.closeCanvasLateralPanel();
                }
            }

            // Cerrar dropdown de idioma si está abierto
            if (this.languageContent && !this.languageContent.classList.contains('hidden')) {
                if (!this.languageDropdown?.contains(e.target)) {
                    this.closeLanguageDropdown();
                }
            }
        });

        // Event listeners para acciones de notificaciones
        this.initNotificationActions();
    }

    /**
     * Inicializar acciones de notificaciones
     */
    initNotificationActions() {
        // Marcar como leído
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('mark-as-read')) {
                e.preventDefault();
                const notificationId = e.target.getAttribute('data-notification-id');
                this.markAsRead(notificationId);
            }
        });

        // Eliminar notificación
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-notification')) {
                e.preventDefault();
                const notificationId = e.target.getAttribute('data-notification-id');
                this.deleteNotification(notificationId);
            }
        });
    }

    /**
     * Toggle panel de notificaciones
     */
    toggleNotifications() {
        console.log('🔔 Toggling notifications panel...');
        
        if (!this.notificationsCanvas) {
            console.error('❌ Notifications canvas not found');
            return;
        }
        
        const isHidden = this.notificationsCanvas.classList.contains('translate-x-full');
        console.log('📱 Current state: notifications panel is', isHidden ? 'hidden' : 'visible');
        
        if (isHidden) {
            console.log('📂 Opening notifications panel...');
            this.openNotificationsPanel();
        } else {
            console.log('📁 Closing notifications panel...');
            this.closeNotificationsPanel();
        }
    }

    /**
     * Abrir panel de notificaciones
     */
    openNotificationsPanel() {
        console.log('📂 Opening notifications panel...');
        
        if (!this.notificationsCanvas || !this.notificationsOverlay) {
            console.error('❌ Required elements not found for notifications panel');
            return;
        }
        
        // Cerrar canvas lateral si está abierto
        if (this.canvasLateral && !this.canvasLateral.classList.contains('translate-x-full')) {
            console.log('📁 Closing canvas lateral first...');
            this.closeCanvasLateralPanel();
        }
        
        this.notificationsCanvas.classList.remove('translate-x-full');
        this.notificationsOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        console.log('✅ Notifications panel opened');
    }

    /**
     * Cerrar panel de notificaciones
     */
    closeNotificationsPanel() {
        console.log('📁 Closing notifications panel...');
        
        if (!this.notificationsCanvas || !this.notificationsOverlay) {
            console.error('❌ Required elements not found for notifications panel');
            return;
        }
        
        this.notificationsCanvas.classList.add('translate-x-full');
        this.notificationsOverlay.classList.add('hidden');
        document.body.style.overflow = '';
        
        console.log('✅ Notifications panel closed');
    }

    /**
     * Limpiar todas las notificaciones
     */
    clearAllNotificationsHandler() {
        const notificationItems = document.querySelectorAll('.notification-item');
        notificationItems.forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';
            setTimeout(() => item.remove(), 300);
        });

        // Actualizar contador de notificaciones
        this.updateNotificationCount(0);
    }

    /**
     * Marcar notificación como leída
     */
    markAsRead(notificationId) {
        const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`)?.closest('.notification-item');
        if (notificationItem) {
            notificationItem.classList.remove('bg-blue-50', 'border-l-4', 'border-blue-500');
            notificationItem.classList.add('bg-gray-50');
            
            const icon = notificationItem.querySelector('.text-xl');
            if (icon) {
                icon.classList.remove('text-blue-500');
                icon.classList.add('text-gray-400');
            }

            // Actualizar botón
            const markAsReadBtn = notificationItem.querySelector('.mark-as-read');
            if (markAsReadBtn) {
                markAsReadBtn.textContent = 'Leído';
                markAsReadBtn.disabled = true;
                markAsReadBtn.classList.add('text-gray-400', 'cursor-not-allowed');
                markAsReadBtn.classList.remove('text-blue-600', 'hover:text-blue-800');
            }
        }
    }

    /**
     * Eliminar notificación
     */
    deleteNotification(notificationId) {
        const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`)?.closest('.notification-item');
        if (notificationItem) {
            notificationItem.style.opacity = '0';
            notificationItem.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notificationItem.remove();
                this.updateNotificationCount();
            }, 300);
        }
    }

    /**
     * Actualizar contador de notificaciones
     */
    updateNotificationCount(count = null) {
        const badge = document.querySelector('.notification-toggle .absolute');
        if (badge) {
            const currentCount = parseInt(badge.textContent) || 0;
            const newCount = count !== null ? count : Math.max(0, currentCount - 1);
            
            if (newCount > 0) {
                badge.textContent = newCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    /**
     * Toggle pantalla completa
     */
    toggleFullscreen() {
        console.log('🔍 Toggling fullscreen...');
        
        try {
            const currentlyFullscreen = this.isFullscreen();
            console.log('📱 Current state: fullscreen is', currentlyFullscreen ? 'active' : 'inactive');
            
            if (currentlyFullscreen) {
                console.log('↩️ Exiting fullscreen...');
                this.exitFullscreen();
            } else {
                console.log('↗️ Entering fullscreen...');
                this.enterFullscreen();
            }
        } catch (error) {
            console.error('❌ Error toggling fullscreen:', error);
            this.showFullscreenError();
        }
    }
    
    /**
     * Verificar si está en pantalla completa
     */
    isFullscreen() {
        return document.fullscreenElement ||
               document.webkitFullscreenElement ||
               document.mozFullScreenElement ||
               document.msFullscreenElement;
    }

    /**
     * Entrar a pantalla completa
     */
    enterFullscreen() {
        const element = document.documentElement;
        
        try {
            if (element.requestFullscreen) {
                element.requestFullscreen().catch(err => {
                    console.error(`Error al intentar entrar en modo pantalla completa: ${err.message}`);
                    this.showFullscreenError();
                });
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            } else {
                console.warn('Fullscreen API not supported');
                this.showFullscreenError();
                return;
            }
            
            // Actualizar iconos inmediatamente (se corregirá con los listeners si es necesario)
            this.updateFullscreenIcons(true);
        } catch (error) {
            console.error('Error entering fullscreen:', error);
            this.showFullscreenError();
        }
    }

    /**
     * Salir de pantalla completa
     */
    exitFullscreen() {
        try {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
            
            // Actualizar iconos inmediatamente
            this.updateFullscreenIcons(false);
        } catch (error) {
            console.error('Error exiting fullscreen:', error);
        }
    }
    
    /**
     * Actualizar iconos de pantalla completa
     */
    updateFullscreenIcons(isFullscreen = null) {
        if (!this.fullscreenIcon || !this.fullscreenExitIcon) return;
        
        const currentlyFullscreen = isFullscreen !== null ? isFullscreen : this.isFullscreen();
        
        if (currentlyFullscreen) {
            this.fullscreenIcon.classList.add('hidden');
            this.fullscreenExitIcon.classList.remove('hidden');
        } else {
            this.fullscreenIcon.classList.remove('hidden');
            this.fullscreenExitIcon.classList.add('hidden');
        }
    }
    
    /**
     * Mostrar error de pantalla completa
     */
    showFullscreenError() {
        if (typeof showNotification === 'function') {
            showNotification('Pantalla completa no disponible o bloqueada por el navegador', 'warning', 3000);
        } else {
            console.warn('Pantalla completa no disponible');
        }
    }

    /**
     * Escuchar cambios en el estado de pantalla completa
     */
    initFullscreenListeners() {
        // Listener unificado para todos los eventos de cambio de pantalla completa
        const handleFullscreenChange = () => {
            this.updateFullscreenIcons();
        };
        
        // Agregar listeners para todos los navegadores
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);
        
        // Listener para errores de pantalla completa
        document.addEventListener('fullscreenerror', (e) => {
            console.error('Fullscreen error:', e);
            this.showFullscreenError();
            this.updateFullscreenIcons(false);
        });
        
        document.addEventListener('webkitfullscreenerror', (e) => {
            console.error('Webkit fullscreen error:', e);
            this.showFullscreenError();
            this.updateFullscreenIcons(false);
        });
    }

    /**
     * Toggle dropdown de idioma
     */
    toggleLanguageDropdown() {
        this.languageContent.classList.toggle('hidden');
    }

    /**
     * Cerrar dropdown de idioma
     */
    closeLanguageDropdown() {
        this.languageContent.classList.add('hidden');
    }

    /**
     * Inicializar canvas lateral
     */
    initCanvasLateral() {
        this.canvasLateral = document.getElementById('canvasLateral');
        this.canvasLateralOverlay = document.getElementById('canvasLateralOverlay');
        this.canvasLateralToggle = document.querySelector('.canvas-lateral-toggle');
        this.closeCanvasLateral = document.querySelector('.close-canvas-lateral');

        if (!this.canvasLateral || !this.canvasLateralOverlay || !this.canvasLateralToggle) {
            console.warn('Elements for canvas lateral not found');
            return;
        }
    }

    /**
     * Toggle canvas lateral
     */
    toggleCanvasLateral() {
        console.log('≡ Toggling canvas lateral...');
        
        if (!this.canvasLateral) {
            console.error('❌ Canvas lateral not found');
            return;
        }
        
        const isHidden = this.canvasLateral.classList.contains('translate-x-full');
        console.log('📱 Current state: canvas lateral is', isHidden ? 'hidden' : 'visible');
        
        if (isHidden) {
            console.log('📂 Opening canvas lateral...');
            this.openCanvasLateral();
        } else {
            console.log('📁 Closing canvas lateral...');
            this.closeCanvasLateralPanel();
        }
    }

    /**
     * Abrir canvas lateral
     */
    openCanvasLateral() {
        console.log('📂 Opening canvas lateral...');
        
        if (!this.canvasLateral || !this.canvasLateralOverlay) {
            console.error('❌ Required elements not found for canvas lateral');
            return;
        }
        
        // Cerrar notificaciones si están abiertas
        if (this.notificationsCanvas && !this.notificationsCanvas.classList.contains('translate-x-full')) {
            console.log('📁 Closing notifications first...');
            this.closeNotificationsPanel();
        }
        
        this.canvasLateral.classList.remove('translate-x-full');
        this.canvasLateralOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        console.log('✅ Canvas lateral opened');
    }

    /**
     * Cerrar canvas lateral
     */
    closeCanvasLateralPanel() {
        console.log('📁 Closing canvas lateral...');
        
        if (!this.canvasLateral || !this.canvasLateralOverlay) {
            console.error('❌ Required elements not found for canvas lateral');
            return;
        }
        
        this.canvasLateral.classList.add('translate-x-full');
        this.canvasLateralOverlay.classList.add('hidden');
        document.body.style.overflow = '';
        
        console.log('✅ Canvas lateral closed');
    }
    
    /**
     * Método para destruir la instancia y limpiar event listeners
     */
    destroy() {
        // Remover event listeners
        if (this.notificationToggle) {
            this.notificationToggle.removeEventListener('click', this.toggleNotifications);
        }
        
        if (this.fullscreenToggle) {
            this.fullscreenToggle.removeEventListener('click', this.toggleFullscreen);
        }
        
        if (this.canvasLateralToggle) {
            this.canvasLateralToggle.removeEventListener('click', this.toggleCanvasLateral);
        }
        
        // Cerrar paneles abiertos
        this.closeNotificationsPanel();
        this.closeCanvasLateralPanel();
        
        // Resetear flags
        this.isInitialized = false;
        
        console.log('HeaderEnhanced destroyed');
    }
    
    /**
     * Método para reinicializar si es necesario
     */
    reinitialize() {
        if (this.isInitialized) {
            this.destroy();
        }
        this.init();
    }
}

// Variable global para la instancia
let headerEnhancedInstance = null;

/**
 * Función de inicialización compatible con main.js
 */
window.initHeader = function() {
    if (headerEnhancedInstance) {
        console.warn('Header already initialized');
        return headerEnhancedInstance;
    }
    
    headerEnhancedInstance = new HeaderEnhanced();
    return headerEnhancedInstance;
};

/**
 * Función para obtener la instancia actual
 */
window.getHeaderInstance = function() {
    return headerEnhancedInstance;
};

/**
 * Función para destruir la instancia
 */
window.destroyHeader = function() {
    if (headerEnhancedInstance && typeof headerEnhancedInstance.destroy === 'function') {
        headerEnhancedInstance.destroy();
        headerEnhancedInstance = null;
    }
};

// Inicializar automáticamente cuando el DOM esté listo (fallback)
document.addEventListener('DOMContentLoaded', () => {
    // Solo inicializar automáticamente si no se ha inicializado ya
    if (!headerEnhancedInstance) {
        setTimeout(() => {
            if (!headerEnhancedInstance) {
                console.log('Auto-initializing HeaderEnhanced...');
                window.initHeader();
            }
        }, 100);
    }
});

// Exportar para uso con módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeaderEnhanced;
}
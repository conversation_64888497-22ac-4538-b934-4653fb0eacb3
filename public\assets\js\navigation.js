/**
 * Funciones de navegación para el SPA
 * Este archivo contiene las funciones necesarias para manejar la navegación entre páginas.
 */

// Variables globales
let currentPage = '';
let baseUrl = '';
let sidebarInstance = null;

/**
 * Inicializa la navegación del SPA
 * @param {Object} options - Opciones de inicialización
 */
function initNavigation(options = {}) {
    currentPage = options.initialPage || 'project';
    baseUrl = options.baseUrl || window.location.pathname;
    
    // Configurar el manejo de eventos del navegador (botones atrás/adelante)
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.page) {
            handleNavigation(event.state.page);
        } else {
            handleNavigation('project');
        }
    });
    
    // Inicializar los componentes de la página actual
    initPageComponents();
}

/**
 * Maneja la navegación entre páginas
 * @param {string} itemId - ID de la página a cargar
 */
function handleNavigation(itemId) {
    if (itemId === currentPage) return;
    
    // Verificar si el itemId es 'project' - necesita tratamiento especial
    if (itemId === 'project' && window.location.pathname.includes('fuse-sidebar-php')) {
        // Si estamos en /fuse-sidebar-php/ y navegando a project, redireccionar a la raíz del proyecto
        window.location.href = window.location.pathname.split('fuse-sidebar-php')[0];
        return;
    }
    
    // Actualizar el sidebar si está disponible
    if (typeof setActiveItem === 'function') {
        setActiveItem(itemId);
    }
    
    // Mostrar indicador de carga
    document.getElementById('main-content').innerHTML = '<div class="flex justify-center items-center h-full"><div class="loader"></div></div>';
    
    // Hacer solicitud AJAX para cargar el contenido de la página
    fetch(`${baseUrl}?page=${itemId}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        // Actualizar el contenido principal
        document.getElementById('main-content').innerHTML = '<div class="main-content-inner">' + html + '</div>';
        
        // Actualizar la URL
        window.history.pushState({ page: itemId }, '', `${baseUrl}?page=${itemId}`);
        
        // Actualizar la variable de página actual
        currentPage = itemId;
        
        // Actualizar el título de la página
        document.title = getPageTitle(itemId) + ' - FUSE React PHP SPA';
        
        // Inicializar componentes de la nueva página
        initPageComponents();
    })
    .catch(error => {
        console.error('Error loading page:', error);
        document.getElementById('main-content').innerHTML = `
            <div class="flex justify-center items-center h-full">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline">Failed to load the page. Please try again.</span>
                </div>
            </div>
        `;
    });
}

/**
 * Obtiene el título de la página
 * @param {string} page - ID de la página
 * @returns {string} Título de la página
 */
function getPageTitle(page) {
    const titles = {
        'project': 'Project Dashboard',
        'analytics': 'Analytics Dashboard',
        'finance': 'Finance Dashboard',
        'crypto': 'Crypto Dashboard',
        'ai-image': 'AI Image Generator',
        'academy': 'Academy',
        'calendar': 'Calendar',
        'messenger': 'Messenger',
        'contacts': 'Contacts',
        'ecommerce': 'E-Commerce',
        'file-manager': 'File Manager',
        'help-center': 'Help Center'
    };
    
    return titles[page] || 'Dashboard';
}

/**
 * Inicializa los componentes específicos de la página
 */
function initPageComponents() {
    // Inicializar gráficos si existen
    const chartElements = document.querySelectorAll('.chart-container canvas');
    if (chartElements.length > 0) {
        initCharts();
    }
    
    // Inicializar calendarios si existen
    const calendarElements = document.querySelectorAll('.calendar-container');
    if (calendarElements.length > 0) {
        initCalendars();
    }
    
    // Inicializar tablas de datos si existen
    const dataTableElements = document.querySelectorAll('.data-table');
    if (dataTableElements.length > 0) {
        initDataTables();
    }
}

/**
 * Inicializa los gráficos de la página
 */
function initCharts() {
    const charts = document.querySelectorAll('.chart-container canvas');
    charts.forEach(canvas => {
        const chartType = canvas.dataset.chartType || 'line';
        const chartId = canvas.id;
        
        if (chartId === 'salesChart') {
            initSalesChart(canvas);
        } else if (chartId === 'visitsChart') {
            initVisitsChart(canvas);
        } else if (chartId === 'conversionChart') {
            initConversionChart(canvas);
        }
    });
}

/**
 * Inicializa el gráfico de ventas
 * @param {HTMLCanvasElement} canvas - Elemento canvas del gráfico
 */
function initSalesChart(canvas) {
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Sales',
                data: [12, 19, 3, 5, 2, 3, 20, 33, 23, 12, 33, 55],
                borderColor: '#0ea5e9',
                backgroundColor: 'rgba(14, 165, 233, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de visitas
 * @param {HTMLCanvasElement} canvas - Elemento canvas del gráfico
 */
function initVisitsChart(canvas) {
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Visits',
                data: [1200, 1900, 3000, 5000, 2000, 3000, 4000],
                backgroundColor: '#0ea5e9',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de conversión
 * @param {HTMLCanvasElement} canvas - Elemento canvas del gráfico
 */
function initConversionChart(canvas) {
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Direct', 'Social', 'Email', 'Other'],
            datasets: [{
                data: [30, 40, 20, 10],
                backgroundColor: ['#0ea5e9', '#ef4444', '#10b981', '#f59e0b'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '70%'
        }
    });
}

/**
 * Inicializa los calendarios
 */
function initCalendars() {
    // Implementación del calendario si es necesario
    console.log('Calendars initialized');
}

/**
 * Inicializa las tablas de datos
 */
function initDataTables() {
    // Implementación de tablas de datos si es necesario
    console.log('Data tables initialized');
}
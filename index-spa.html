<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProyectDash - Modern SPA</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Application Styles -->
    <link rel="stylesheet" href="assets/css/main.css">
    
    <style>
        /* Loading Screen */
        .app-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .app-loading .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e2e8f0;
            border-top-color: #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Route Transitions */
        .fade-enter {
            opacity: 0;
            transform: translateY(10px);
        }
        
        .fade-enter-active {
            transition: all 0.3s ease;
        }
        
        .fade-leave {
            opacity: 1;
        }
        
        .fade-leave-active {
            transition: all 0.2s ease;
            opacity: 0;
        }
        
        /* Error Page */
        .init-error {
            text-align: center;
            padding: 2rem;
        }
        
        .init-error h1 {
            font-size: 2rem;
            color: #ef4444;
            margin-bottom: 1rem;
        }
        
        .init-error .error-message {
            color: #6b7280;
            margin: 1rem 0;
        }
        
        .init-error button {
            background: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
        }
        
        .init-error button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Loading Screen -->
    <div class="app-loading" id="app-loading">
        <div class="spinner"></div>
    </div>
    
    <!-- Application Root -->
    <div id="app" class="flex flex-col h-screen" style="display: none;">
        <!-- Header Container -->
        <div id="header-container"></div>
        
        <!-- Main Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar Container -->
            <div id="sidebar-container"></div>
            
            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto" id="main-content">
                <!-- Dynamic content will be loaded here -->
            </main>
        </div>
    </div>
    
    <!-- Core Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>
    
    <!-- Core Application Files -->
    <script src="assets/js/core/Store.js"></script>
    <script src="assets/js/core/SPARouter.js"></script>
    <script src="assets/js/core/ApiClient.js"></script>
    
    <!-- Application Bootstrap -->
    <script type="module">
        // Import main application
        import app from './assets/js/app.js';
        
        // Hide loading screen when app is ready
        window.addEventListener('app:ready', () => {
            document.getElementById('app-loading').style.display = 'none';
            document.getElementById('app').style.display = 'flex';
        });
        
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            
            // Show user-friendly error message
            if (window.$store) {
                window.$store.dispatch('app/showNotification', {
                    type: 'error',
                    message: 'An unexpected error occurred. Please refresh the page.'
                });
            }
        });
        
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            
            // Show user-friendly error message
            if (window.$store) {
                window.$store.dispatch('app/showNotification', {
                    type: 'error',
                    message: 'An unexpected error occurred. Please try again.'
                });
            }
        });
    </script>
    
    <!-- Development Mode Helper -->
    <script>
        if (window.location.hostname === 'localhost') {
            console.log('%c ProyectDash SPA ', 'background: #3b82f6; color: white; padding: 2px 10px; border-radius: 3px;');
            console.log('Development mode enabled');
            console.log('Available globals: $app, $router, $store, $api');
        }
    </script>
</body>
</html>
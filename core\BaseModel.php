<?php
/**
 * BaseModel - Modelo Base para ORM Simple
 * 
 * Clase base que proporciona funcionalidades comunes para todos los modelos:
 * - CRUD operations básicas
 * - Query Builder integrado
 * - Validación de datos
 * - Timestamps automáticos
 * - Soft deletes
 * - Relaciones básicas
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

require_once __DIR__ . '/DatabaseManager.php';
require_once __DIR__ . '/QueryBuilder.php';

abstract class BaseModel {
    /**
     * Nombre de la tabla
     * @var string
     */
    protected $table;
    
    /**
     * Clave primaria
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * Campos que se pueden asignar masivamente
     * @var array
     */
    protected $fillable = [];
    
    /**
     * Campos protegidos que no se pueden asignar masivamente
     * @var array
     */
    protected $guarded = ['id', 'created_at', 'updated_at'];
    
    /**
     * Timestamps automáticos
     * @var bool
     */
    protected $timestamps = true;
    
    /**
     * Soft deletes
     * @var bool
     */
    protected $softDeletes = false;
    
    /**
     * Atributos del modelo
     * @var array
     */
    protected $attributes = [];
    
    /**
     * Atributos originales (para detectar cambios)
     * @var array
     */
    protected $original = [];
    
    /**
     * Indica si el modelo existe en la base de datos
     * @var bool
     */
    protected $exists = false;
    
    /**
     * DatabaseManager instance
     * @var DatabaseManager
     */
    protected static $db;
    
    /**
     * QueryBuilder instance
     * @var QueryBuilder
     */
    protected $queryBuilder;
    
    /**
     * Reglas de validación
     * @var array
     */
    protected $rules = [];
    
    /**
     * Mensajes de validación personalizados
     * @var array
     */
    protected $messages = [];
    
    /**
     * Constructor
     * 
     * @param array $attributes
     */
    public function __construct(array $attributes = []) {
        if (static::$db === null) {
            static::$db = DatabaseManager::getInstance();
        }
        
        $this->queryBuilder = new QueryBuilder(static::$db);
        $this->fill($attributes);
    }
    
    /**
     * Llenar el modelo con atributos
     * 
     * @param array $attributes
     * @return $this
     */
    public function fill(array $attributes): self {
        foreach ($attributes as $key => $value) {
            if ($this->isFillable($key)) {
                $this->setAttribute($key, $value);
            }
        }
        
        return $this;
    }
    
    /**
     * Verificar si un atributo es asignable masivamente
     * 
     * @param string $key
     * @return bool
     */
    protected function isFillable(string $key): bool {
        if (in_array($key, $this->guarded)) {
            return false;
        }
        
        if (empty($this->fillable)) {
            return true;
        }
        
        return in_array($key, $this->fillable);
    }
    
    /**
     * Establecer un atributo
     * 
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setAttribute(string $key, $value): self {
        $this->attributes[$key] = $value;
        return $this;
    }
    
    /**
     * Obtener un atributo
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getAttribute(string $key, $default = null) {
        return $this->attributes[$key] ?? $default;
    }
    
    /**
     * Magic getter
     * 
     * @param string $key
     * @return mixed
     */
    public function __get(string $key) {
        return $this->getAttribute($key);
    }
    
    /**
     * Magic setter
     * 
     * @param string $key
     * @param mixed $value
     */
    public function __set(string $key, $value): void {
        $this->setAttribute($key, $value);
    }
    
    /**
     * Magic isset
     * 
     * @param string $key
     * @return bool
     */
    public function __isset(string $key): bool {
        return isset($this->attributes[$key]);
    }
    
    /**
     * Guardar el modelo
     * 
     * @return bool
     * @throws Exception
     */
    public function save(): bool {
        if (!$this->validate()) {
            return false;
        }
        
        if ($this->timestamps) {
            $now = date('Y-m-d H:i:s');
            
            if (!$this->exists) {
                $this->setAttribute('created_at', $now);
            }
            
            $this->setAttribute('updated_at', $now);
        }
        
        if ($this->exists) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }
    
    /**
     * Realizar inserción
     * 
     * @return bool
     * @throws Exception
     */
    protected function performInsert(): bool {
        $data = $this->attributes;
        
        $result = $this->queryBuilder
            ->table($this->table)
            ->insert($data);
        
        if ($result) {
            $this->setAttribute($this->primaryKey, static::$db->lastInsertId());
            $this->exists = true;
            $this->syncOriginal();
            return true;
        }
        
        return false;
    }
    
    /**
     * Realizar actualización
     * 
     * @return bool
     * @throws Exception
     */
    protected function performUpdate(): bool {
        $dirtyAttributes = $this->getDirty();
        
        if (empty($dirtyAttributes)) {
            return true; // No hay cambios
        }
        
        $result = $this->queryBuilder
            ->table($this->table)
            ->where($this->primaryKey, $this->getAttribute($this->primaryKey))
            ->update($dirtyAttributes);
        
        if ($result) {
            $this->syncOriginal();
            return true;
        }
        
        return false;
    }
    
    /**
     * Obtener atributos que han cambiado
     * 
     * @return array
     */
    public function getDirty(): array {
        $dirty = [];
        
        foreach ($this->attributes as $key => $value) {
            if (!array_key_exists($key, $this->original) || $this->original[$key] !== $value) {
                $dirty[$key] = $value;
            }
        }
        
        return $dirty;
    }
    
    /**
     * Sincronizar atributos originales
     */
    protected function syncOriginal(): void {
        $this->original = $this->attributes;
    }
    
    /**
     * Eliminar el modelo
     * 
     * @return bool
     * @throws Exception
     */
    public function delete(): bool {
        if (!$this->exists) {
            return false;
        }
        
        if ($this->softDeletes) {
            return $this->performSoftDelete();
        } else {
            return $this->performDelete();
        }
    }
    
    /**
     * Realizar eliminación física
     * 
     * @return bool
     * @throws Exception
     */
    protected function performDelete(): bool {
        $result = $this->queryBuilder
            ->table($this->table)
            ->where($this->primaryKey, $this->getAttribute($this->primaryKey))
            ->delete();
        
        if ($result) {
            $this->exists = false;
            return true;
        }
        
        return false;
    }
    
    /**
     * Realizar eliminación lógica (soft delete)
     * 
     * @return bool
     * @throws Exception
     */
    protected function performSoftDelete(): bool {
        $this->setAttribute('deleted_at', date('Y-m-d H:i:s'));
        return $this->save();
    }
    
    /**
     * Restaurar modelo eliminado lógicamente
     * 
     * @return bool
     * @throws Exception
     */
    public function restore(): bool {
        if (!$this->softDeletes) {
            return false;
        }
        
        $this->setAttribute('deleted_at', null);
        return $this->save();
    }
    
    /**
     * Validar el modelo
     * 
     * @return bool
     */
    protected function validate(): bool {
        if (empty($this->rules)) {
            return true;
        }
        
        // Implementación básica de validación
        foreach ($this->rules as $field => $rules) {
            $value = $this->getAttribute($field);
            $fieldRules = is_array($rules) ? $rules : explode('|', $rules);
            
            foreach ($fieldRules as $rule) {
                if (!$this->validateRule($field, $value, $rule)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Validar una regla específica
     * 
     * @param string $field
     * @param mixed $value
     * @param string $rule
     * @return bool
     */
    protected function validateRule(string $field, $value, string $rule): bool {
        switch ($rule) {
            case 'required':
                return !empty($value);
            
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            
            case 'numeric':
                return is_numeric($value);
            
            default:
                if (strpos($rule, 'max:') === 0) {
                    $max = (int) substr($rule, 4);
                    return strlen($value) <= $max;
                }
                
                if (strpos($rule, 'min:') === 0) {
                    $min = (int) substr($rule, 4);
                    return strlen($value) >= $min;
                }
                
                return true;
        }
    }
    
    /**
     * Crear nuevo modelo
     * 
     * @param array $attributes
     * @return static
     * @throws Exception
     */
    public static function create(array $attributes): self {
        $model = new static($attributes);
        $model->save();
        return $model;
    }
    
    /**
     * Encontrar modelo por ID
     * 
     * @param mixed $id
     * @return static|null
     * @throws Exception
     */
    public static function find($id): ?self {
        $instance = new static();
        
        $result = $instance->queryBuilder
            ->table($instance->table)
            ->where($instance->primaryKey, $id)
            ->first();
        
        if ($result) {
            return $instance->newFromBuilder($result);
        }
        
        return null;
    }
    
    /**
     * Encontrar modelo por ID o fallar
     * 
     * @param mixed $id
     * @return static
     * @throws Exception
     */
    public static function findOrFail($id): self {
        $model = static::find($id);
        
        if ($model === null) {
            throw new Exception("Registro no encontrado con ID: $id");
        }
        
        return $model;
    }
    
    /**
     * Obtener todos los modelos
     * 
     * @return array
     * @throws Exception
     */
    public static function all(): array {
        $instance = new static();
        
        $results = $instance->queryBuilder
            ->table($instance->table)
            ->get();
        
        return array_map(function($result) use ($instance) {
            return $instance->newFromBuilder($result);
        }, $results);
    }
    
    /**
     * Crear nuevo modelo desde resultado de query
     * 
     * @param array $attributes
     * @return static
     */
    protected function newFromBuilder(array $attributes): self {
        $model = new static();
        $model->fill($attributes);
        $model->exists = true;
        $model->syncOriginal();
        return $model;
    }
    
    /**
     * Iniciar query builder
     * 
     * @return QueryBuilder
     */
    public static function query(): QueryBuilder {
        $instance = new static();
        return $instance->queryBuilder->table($instance->table);
    }
    
    /**
     * Scope para registros no eliminados (soft deletes)
     * 
     * @param QueryBuilder $query
     * @return QueryBuilder
     */
    public function scopeActive(QueryBuilder $query): QueryBuilder {
        if ($this->softDeletes) {
            return $query->whereNull('deleted_at');
        }
        
        return $query;
    }
    
    /**
     * Convertir modelo a array
     * 
     * @return array
     */
    public function toArray(): array {
        return $this->attributes;
    }
    
    /**
     * Convertir modelo a JSON
     * 
     * @return string
     */
    public function toJson(): string {
        return json_encode($this->toArray());
    }
    
    /**
     * Refresh modelo desde base de datos
     * 
     * @return $this
     * @throws Exception
     */
    public function refresh(): self {
        if (!$this->exists) {
            return $this;
        }
        
        $result = $this->queryBuilder
            ->table($this->table)
            ->where($this->primaryKey, $this->getAttribute($this->primaryKey))
            ->first();
        
        if ($result) {
            $this->fill($result);
            $this->syncOriginal();
        }
        
        return $this;
    }
}
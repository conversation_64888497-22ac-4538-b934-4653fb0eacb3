<?php
/**
 * Ejemplo de Integración API
 * 
 * Demuestra cómo integrar el nuevo sistema de base de datos
 * con la API existente del proyecto
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

require_once __DIR__ . '/../public/api/config/config.php';
require_once __DIR__ . '/ProjectModel.php';
require_once __DIR__ . '/UserModel.php';

/**
 * Controlador de Proyectos mejorado
 */
class ProjectsApiController {
    
    /**
     * Obtener lista de proyectos con filtros y paginación
     * 
     * GET /api/v1/projects
     */
    public static function getProjects() {
        try {
            // Parámetros de query
            $page = (int) ($_GET['page'] ?? 1);
            $perPage = min((int) ($_GET['per_page'] ?? 10), 50); // Máximo 50
            $status = $_GET['status'] ?? '';
            $priority = $_GET['priority'] ?? '';
            $clientId = (int) ($_GET['client_id'] ?? 0);
            $managerId = (int) ($_GET['manager_id'] ?? 0);
            $search = trim($_GET['search'] ?? '');
            $minBudget = (float) ($_GET['min_budget'] ?? 0);
            $maxBudget = (float) ($_GET['max_budget'] ?? 0);
            
            // Construir query base
            $query = ProjectModel::query()
                               ->select('id', 'name', 'description', 'status', 'priority', 
                                       'start_date', 'end_date', 'budget', 'client_id', 
                                       'manager_id', 'created_at', 'updated_at');
            
            // Aplicar filtros
            if (!empty($status)) {
                $query->byStatus($status);
            }
            
            if (!empty($priority)) {
                $query->byPriority($priority);
            }
            
            if ($clientId > 0) {
                $query->byClient($clientId);
            }
            
            if ($managerId > 0) {
                $query->byManager($managerId);
            }
            
            if (!empty($search)) {
                $query->search($search);
            }
            
            if ($minBudget > 0 && $maxBudget > $minBudget) {
                $query->byBudgetRange($minBudget, $maxBudget);
            }
            
            // Ejecutar query con paginación
            $result = $query->orderBy('created_at', 'desc')
                           ->paginate($page, $perPage);
            
            // Formatear datos
            $projects = array_map(function($projectData) {
                $project = new ProjectModel($projectData);
                $project->exists = true; // Marcar como existente para cálculos
                return $project->toArray();
            }, $result['data']);
            
            return jsonResponse([
                'success' => true,
                'data' => $projects,
                'pagination' => [
                    'current_page' => $result['current_page'],
                    'per_page' => $result['per_page'],
                    'total' => $result['total'],
                    'last_page' => $result['last_page'],
                    'from' => $result['from'],
                    'to' => $result['to']
                ],
                'filters_applied' => [
                    'status' => $status,
                    'priority' => $priority,
                    'client_id' => $clientId,
                    'manager_id' => $managerId,
                    'search' => $search
                ]
            ]);
            
        } catch (Exception $e) {
            return jsonResponse([
                'success' => false,
                'error' => 'Error al obtener proyectos: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Obtener proyecto específico con detalles completos
     * 
     * GET /api/v1/projects/{id}
     */
    public static function getProject($id) {
        try {
            $project = ProjectModel::find($id);
            
            if (!$project) {
                return jsonResponse([
                    'success' => false,
                    'error' => 'Proyecto no encontrado'
                ], 404);
            }
            
            // Obtener datos completos
            $projectData = $project->toArray();
            $projectData['statistics'] = $project->getStatistics();
            $projectData['tasks'] = $project->getTasks();
            $projectData['team_members'] = $project->getTeamMembers();
            
            return jsonResponse([
                'success' => true,
                'data' => $projectData
            ]);
            
        } catch (Exception $e) {
            return jsonResponse([
                'success' => false,
                'error' => 'Error al obtener proyecto: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Crear nuevo proyecto
     * 
     * POST /api/v1/projects
     */
    public static function createProject() {
        try {
            // Obtener datos JSON
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                return jsonResponse([
                    'success' => false,
                    'error' => 'Datos JSON inválidos'
                ], 400);
            }
            
            // Validar datos requeridos
            $required = ['name', 'description', 'status', 'priority', 'start_date'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    return jsonResponse([
                        'success' => false,
                        'error' => "Campo requerido faltante: $field"
                    ], 400);
                }
            }
            
            // Crear proyecto usando transacción
            $db = getDatabaseManager();
            $projectId = $db->transaction(function($db) use ($input) {
                
                // Crear proyecto
                $project = ProjectModel::create([
                    'name' => $input['name'],
                    'description' => $input['description'],
                    'status' => $input['status'],
                    'priority' => $input['priority'],
                    'start_date' => $input['start_date'],
                    'end_date' => $input['end_date'] ?? null,
                    'budget' => $input['budget'] ?? 0,
                    'client_id' => $input['client_id'] ?? null,
                    'manager_id' => $input['manager_id'] ?? null
                ]);
                
                // Asignar miembros del equipo si se proporcionan
                if (!empty($input['team_members'])) {
                    foreach ($input['team_members'] as $member) {
                        $db->query(
                            "INSERT INTO project_members (project_id, user_id, role, joined_at) VALUES (?, ?, ?, ?)",
                            [$project->id, $member['user_id'], $member['role'] ?? 'member', date('Y-m-d H:i:s')]
                        );
                    }
                }
                
                // Crear tareas iniciales si se proporcionan
                if (!empty($input['initial_tasks'])) {
                    foreach ($input['initial_tasks'] as $task) {
                        $db->query(
                            "INSERT INTO project_tasks (project_id, name, description, status, priority, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                            [$project->id, $task['name'], $task['description'] ?? '', 'pending', $task['priority'] ?? 'medium', date('Y-m-d H:i:s')]
                        );
                    }
                }
                
                return $project->id;
            });
            
            // Obtener proyecto creado con datos completos
            $project = ProjectModel::find($projectId);
            
            return jsonResponse([
                'success' => true,
                'message' => 'Proyecto creado exitosamente',
                'data' => $project->toArray()
            ], 201);
            
        } catch (Exception $e) {
            return jsonResponse([
                'success' => false,
                'error' => 'Error al crear proyecto: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Actualizar proyecto
     * 
     * PUT /api/v1/projects/{id}
     */
    public static function updateProject($id) {
        try {
            $project = ProjectModel::find($id);
            
            if (!$project) {
                return jsonResponse([
                    'success' => false,
                    'error' => 'Proyecto no encontrado'
                ], 404);
            }
            
            // Obtener datos JSON
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                return jsonResponse([
                    'success' => false,
                    'error' => 'Datos JSON inválidos'
                ], 400);
            }
            
            // Actualizar campos permitidos
            $allowedFields = ['name', 'description', 'priority', 'start_date', 'end_date', 'budget', 'client_id', 'manager_id'];
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $project->setAttribute($field, $input[$field]);
                }
            }
            
            // Cambio de estado especial (con logging)
            if (isset($input['status']) && $input['status'] !== $project->status) {
                $reason = $input['status_reason'] ?? 'Actualización vía API';
                $project->changeStatus($input['status'], $reason);
            } else {
                $project->save();
            }
            
            return jsonResponse([
                'success' => true,
                'message' => 'Proyecto actualizado exitosamente',
                'data' => $project->toArray()
            ]);
            
        } catch (Exception $e) {
            return jsonResponse([
                'success' => false,
                'error' => 'Error al actualizar proyecto: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Eliminar proyecto (soft delete)
     * 
     * DELETE /api/v1/projects/{id}
     */
    public static function deleteProject($id) {
        try {
            $project = ProjectModel::find($id);
            
            if (!$project) {
                return jsonResponse([
                    'success' => false,
                    'error' => 'Proyecto no encontrado'
                ], 404);
            }
            
            // Verificar si se puede eliminar
            if ($project->status === ProjectModel::STATUS_ACTIVE) {
                return jsonResponse([
                    'success' => false,
                    'error' => 'No se puede eliminar un proyecto activo. Primero cambie su estado.'
                ], 400);
            }
            
            $project->delete();
            
            return jsonResponse([
                'success' => true,
                'message' => 'Proyecto eliminado exitosamente'
            ]);
            
        } catch (Exception $e) {
            return jsonResponse([
                'success' => false,
                'error' => 'Error al eliminar proyecto: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Obtener estadísticas generales de proyectos
     * 
     * GET /api/v1/projects/statistics
     */
    public static function getProjectStatistics() {
        try {
            $db = getDatabaseManager();
            
            // Estadísticas por estado
            $statusStats = $db->query(
                "SELECT status, COUNT(*) as count, COALESCE(SUM(budget), 0) as total_budget
                 FROM projects 
                 WHERE deleted_at IS NULL 
                 GROUP BY status"
            )->fetchAll();
            
            // Estadísticas por prioridad
            $priorityStats = $db->query(
                "SELECT priority, COUNT(*) as count 
                 FROM projects 
                 WHERE deleted_at IS NULL 
                 GROUP BY priority"
            )->fetchAll();
            
            // Proyectos vencidos
            $overdueCount = $db->query(
                "SELECT COUNT(*) as count 
                 FROM projects 
                 WHERE deleted_at IS NULL 
                 AND end_date < CURDATE() 
                 AND status NOT IN ('completed', 'cancelled')"
            )->fetch()['count'];
            
            // Presupuesto total
            $budgetStats = $db->query(
                "SELECT 
                    COALESCE(SUM(p.budget), 0) as total_budget,
                    COALESCE(SUM(expenses.total), 0) as total_expenses
                 FROM projects p
                 LEFT JOIN (
                    SELECT project_id, SUM(amount) as total 
                    FROM project_expenses 
                    GROUP BY project_id
                 ) expenses ON p.id = expenses.project_id
                 WHERE p.deleted_at IS NULL"
            )->fetch();
            
            return jsonResponse([
                'success' => true,
                'data' => [
                    'by_status' => $statusStats,
                    'by_priority' => $priorityStats,
                    'overdue_count' => (int) $overdueCount,
                    'budget' => [
                        'total_allocated' => (float) $budgetStats['total_budget'],
                        'total_spent' => (float) $budgetStats['total_expenses'],
                        'remaining' => (float) $budgetStats['total_budget'] - (float) $budgetStats['total_expenses']
                    ]
                ]
            ]);
            
        } catch (Exception $e) {
            return jsonResponse([
                'success' => false,
                'error' => 'Error al obtener estadísticas: ' . $e->getMessage()
            ], 500);
        }
    }
}

// ============================================================================
// ROUTING SIMPLE PARA DEMOSTRACIÓN
// ============================================================================

// Determinar método y ruta
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Obtener ID si existe
$id = null;
if (isset($pathParts[3]) && is_numeric($pathParts[3])) {
    $id = (int) $pathParts[3];
}

// Routing básico
if ($pathParts[0] === 'api' && $pathParts[1] === 'v1' && $pathParts[2] === 'projects') {
    
    switch ($method) {
        case 'GET':
            if ($id) {
                ProjectsApiController::getProject($id);
            } elseif (isset($pathParts[3]) && $pathParts[3] === 'statistics') {
                ProjectsApiController::getProjectStatistics();
            } else {
                ProjectsApiController::getProjects();
            }
            break;
            
        case 'POST':
            ProjectsApiController::createProject();
            break;
            
        case 'PUT':
            if ($id) {
                ProjectsApiController::updateProject($id);
            } else {
                jsonResponse(['success' => false, 'error' => 'ID requerido para actualización'], 400);
            }
            break;
            
        case 'DELETE':
            if ($id) {
                ProjectsApiController::deleteProject($id);
            } else {
                jsonResponse(['success' => false, 'error' => 'ID requerido para eliminación'], 400);
            }
            break;
            
        default:
            jsonResponse(['success' => false, 'error' => 'Método no permitido'], 405);
    }
    
} else {
    // Ejemplo de uso para testing (si se ejecuta directamente)
    echo "=== EJEMPLO DE API INTEGRATION ===\n\n";
    
    // Simular request GET
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET['status'] = 'active';
    $_GET['page'] = 1;
    $_GET['per_page'] = 5;
    
    echo "Simulando GET /api/v1/projects?status=active&page=1&per_page=5\n";
    
    ob_start();
    ProjectsApiController::getProjects();
    $output = ob_get_clean();
    
    echo "Respuesta:\n";
    echo $output . "\n\n";
    
    echo "=== INTEGRACIÓN COMPLETADA ===\n";
}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Botones Header - ProyectDash</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .test-button {
            margin: 8px 4px;
            padding: 8px 16px;
            border: 2px solid #3b82f6;
            background: #eff6ff;
            color: #1e40af;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .test-button:hover {
            background: #dbeafe;
            border-color: #2563eb;
        }
        
        .test-results {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1f2937;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            max-width: 300px;
            z-index: 9999;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .log-item {
            margin: 2px 0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .log-success { background: rgba(34, 197, 94, 0.2); color: #86efac; }
        .log-error { background: rgba(239, 68, 68, 0.2); color: #fca5a5; }
        .log-warning { background: rgba(245, 158, 11, 0.2); color: #fde047; }
        .log-info { background: rgba(59, 130, 246, 0.2); color: #93c5fd; }
    </style>
</head>
<body class="bg-gray-100">
    
    <!-- Header simulado con los elementos necesarios -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">ProyectDash - Test</h1>
                    </div>
                </div>

                <!-- Botones del header -->
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6 space-x-4">
                        
                        <!-- Botón de pantalla completa -->
                        <button type="button" class="fullscreen-toggle bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white" data-header-tooltip="Pantalla completa">
                            <span class="sr-only">Ver en pantalla completa</span>
                            <svg class="w-4 h-4 fullscreen-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5M3.75 20.25v-4.5m0 4.5h4.5M20.25 3.75h-4.5m4.5 0v4.5M20.25 20.25h-4.5m4.5 0v-4.5" />
                            </svg>
                            <svg class="w-4 h-4 fullscreen-exit-icon hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 9V4.5M9 9H4.5M9 9L3.75 3.75M15 9h4.5M15 9V4.5M15 9l5.25-5.25M9 15v4.5M9 15H4.5M9 15l-5.25 5.25M15 15h4.5M15 15v4.5m0-4.5l5.25 5.25" />
                            </svg>
                        </button>

                        <!-- Botón de notificaciones -->
                        <button type="button" class="notification-toggle bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white relative" data-header-tooltip="Notificaciones">
                            <span class="sr-only">Ver notificaciones</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <!-- Badge de notificación -->
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white rounded-full text-xs flex items-center justify-center">3</span>
                        </button>

                        <!-- Botón de menú lateral -->
                        <button type="button" class="canvas-lateral-toggle bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white" data-header-tooltip="Menú lateral">
                            <span class="sr-only">Abrir menú lateral</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Canvas de notificaciones -->
    <div id="notificationsOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
    <div id="notificationsCanvas" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 border-l border-gray-200">
        <!-- Header del canvas -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center">
                <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <h2 class="text-lg font-semibold text-gray-900">Notificaciones</h2>
            </div>
            <div class="flex items-center space-x-2">
                <button class="clear-all-notifications text-gray-400 hover:text-gray-600 text-sm" title="Limpiar todas">
                    Limpiar
                </button>
                <button class="close-notifications text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Contenido del canvas -->
        <div class="overflow-y-auto h-full pb-16">
            <div class="p-4 space-y-4">
                <!-- Notificación de test -->
                <div class="notification-item bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                    <div class="flex items-start">
                        <div class="text-xl mr-3">🧪</div>
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-blue-900">Test de Notificaciones</h3>
                            <p class="text-sm text-blue-700 mt-1">Esta es una notificación de prueba para verificar la funcionalidad.</p>
                            <div class="mt-2 flex space-x-2">
                                <button class="mark-as-read text-xs text-blue-600 hover:text-blue-800" data-notification-id="test">Marcar como leído</button>
                                <button class="delete-notification text-xs text-red-600 hover:text-red-800" data-notification-id="test">Eliminar</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer del canvas -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white">
            <button class="w-full bg-sky-500 text-white py-2 px-4 rounded-lg hover:bg-sky-600 transition-colors">
                Ver todas las notificaciones
            </button>
        </div>
    </div>

    <!-- Canvas lateral -->
    <div id="canvasLateralOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
    <div id="canvasLateral" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 border-l border-gray-200">
        <!-- Header del canvas -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center">
                <svg class="w-6 h-6 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
                <h2 class="text-lg font-semibold text-gray-900">Menú Lateral</h2>
            </div>
            <button class="close-canvas-lateral text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Contenido del canvas -->
        <div class="overflow-y-auto h-full pb-16">
            <div class="p-4 space-y-4">
                <!-- Test del panel -->
                <div class="bg-green-50 border-l-4 border-green-500 p-4 rounded-r-lg">
                    <h3 class="text-sm font-medium text-green-900">🧪 Test del Panel</h3>
                    <p class="text-sm text-green-700 mt-1">Este panel se está mostrando correctamente. ¡Funcionalidad OK!</p>
                </div>
            </div>
        </div>

        <!-- Footer del canvas -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white">
            <button class="w-full bg-sky-500 text-white py-2 px-4 rounded-lg hover:bg-sky-600 transition-colors">
                Configuración avanzada
            </button>
        </div>
    </div>
    
    <!-- Panel de control de tests -->
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">🧪 Test de Funcionalidad - Botones del Header</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Test Pantalla Completa -->
                <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <h3 class="font-semibold text-blue-900 mb-3">🔍 Pantalla Completa</h3>
                    <p class="text-blue-700 text-sm mb-4">Icono: ⛶ | Clase: .fullscreen-toggle</p>
                    <button class="test-button" onclick="testFullscreenButton()">
                        Test Manual
                    </button>
                    <button class="test-button" onclick="testFullscreenAPI()">
                        Test API
                    </button>
                    <div id="fullscreen-status" class="mt-2 text-sm text-blue-600"></div>
                </div>
                
                <!-- Test Notificaciones -->
                <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                    <h3 class="font-semibold text-green-900 mb-3">🔔 Notificaciones</h3>
                    <p class="text-green-700 text-sm mb-4">Badge: 3 | Clase: .notification-toggle</p>
                    <button class="test-button" onclick="testNotificationButton()">
                        Test Manual
                    </button>
                    <button class="test-button" onclick="testNotificationCanvas()">
                        Test Canvas
                    </button>
                    <div id="notifications-status" class="mt-2 text-sm text-green-600"></div>
                </div>
                
                <!-- Test Menú Lateral -->
                <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                    <h3 class="font-semibold text-yellow-900 mb-3">≡ Menú Lateral</h3>
                    <p class="text-yellow-700 text-sm mb-4">Icono: ≡ | Clase: .canvas-lateral-toggle</p>
                    <button class="test-button" onclick="testCanvasLateralButton()">
                        Test Manual
                    </button>
                    <button class="test-button" onclick="testCanvasLateralPanel()">
                        Test Panel
                    </button>
                    <div id="canvas-status" class="mt-2 text-sm text-yellow-600"></div>
                </div>
            </div>
            
            <!-- Información de diagnóstico -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-900 mb-3">🔧 Diagnóstico del Sistema</h3>
                <div id="diagnostic-info" class="text-sm text-gray-600 space-y-1">
                    <div>Cargando información de diagnóstico...</div>
                </div>
                <button class="test-button mt-3" onclick="runFullDiagnostic()">
                    Ejecutar Diagnóstico Completo
                </button>
            </div>
            
            <!-- Test automático -->
            <div class="mt-6">
                <button class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors" onclick="runAllTests()">
                    🚀 Ejecutar Todos los Tests
                </button>
                <button class="bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors ml-3" onclick="clearTestResults()">
                    🗑️ Limpiar Resultados
                </button>
            </div>
        </div>
    </div>
    
    <!-- Panel de resultados -->
    <div class="test-results">
        <h3 style="margin-bottom: 8px; font-weight: bold; color: #fbbf24;">📊 Resultados de Tests</h3>
        <div id="test-log">
            <div class="log-item log-info">Sistema iniciado - Esperando tests...</div>
        </div>
        <button onclick="clearTestResults()" style="background: #ef4444; color: white; padding: 4px 8px; border: none; border-radius: 4px; margin-top: 8px; cursor: pointer; font-size: 11px;">
            Limpiar
        </button>
    </div>

    <!-- Cargar scripts necesarios -->
    <link rel="stylesheet" href="assets/components/header/header-enhanced.css">
    <script src="assets/components/header/header-enhanced.js"></script>
    
    <script>
        // Sistema de logging
        function log(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const logItem = document.createElement('div');
            logItem.className = `log-item log-${type}`;
            logItem.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[TEST] ${message}`);
        }
        
        function clearTestResults() {
            document.getElementById('test-log').innerHTML = '<div class="log-item log-info">Log limpiado - Listo para nuevos tests...</div>';
        }
        
        // Tests de funcionalidad específicos
        function testFullscreenButton() {
            log('🔍 Iniciando test del botón de pantalla completa...', 'info');
            
            const button = document.querySelector('.fullscreen-toggle');
            if (!button) {
                log('❌ Botón de pantalla completa no encontrado', 'error');
                return false;
            }
            
            log('✅ Botón encontrado, simulando clic...', 'success');
            
            // Simular clic
            try {
                button.click();
                log('✅ Clic ejecutado correctamente', 'success');
                
                // Verificar si hay instancia del header
                const headerInstance = window.headerEnhancedInstance || window.getHeaderInstance?.();
                if (headerInstance) {
                    log('✅ Instancia del header encontrada', 'success');
                } else {
                    log('⚠️ Instancia del header no encontrada', 'warning');
                }
                
                return true;
            } catch (error) {
                log(`❌ Error al hacer clic: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testFullscreenAPI() {
            log('🔍 Testando API de pantalla completa...', 'info');
            
            if (!document.fullscreenEnabled) {
                log('⚠️ Fullscreen API no disponible en este navegador', 'warning');
                return false;
            }
            
            const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement);
            log(`📱 Estado actual: ${isFullscreen ? 'pantalla completa activa' : 'pantalla normal'}`, 'info');
            
            return true;
        }
        
        function testNotificationButton() {
            log('🔔 Iniciando test del botón de notificaciones...', 'info');
            
            const button = document.querySelector('.notification-toggle');
            if (!button) {
                log('❌ Botón de notificaciones no encontrado', 'error');
                return false;
            }
            
            log('✅ Botón encontrado, simulando clic...', 'success');
            
            try {
                button.click();
                log('✅ Clic ejecutado correctamente', 'success');
                
                // Verificar canvas
                setTimeout(() => {
                    const canvas = document.getElementById('notificationsCanvas');
                    if (canvas) {
                        const isVisible = !canvas.classList.contains('translate-x-full');
                        log(`📱 Canvas de notificaciones: ${isVisible ? 'visible' : 'oculto'}`, isVisible ? 'success' : 'warning');
                    }
                }, 100);
                
                return true;
            } catch (error) {
                log(`❌ Error al hacer clic: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testNotificationCanvas() {
            log('🔔 Testando canvas de notificaciones...', 'info');
            
            const canvas = document.getElementById('notificationsCanvas');
            const overlay = document.getElementById('notificationsOverlay');
            
            if (!canvas) {
                log('❌ Canvas de notificaciones no encontrado', 'error');
                return false;
            }
            
            if (!overlay) {
                log('❌ Overlay de notificaciones no encontrado', 'error');
                return false;
            }
            
            log('✅ Elementos del canvas encontrados', 'success');
            
            const isHidden = canvas.classList.contains('translate-x-full');
            log(`📱 Estado del canvas: ${isHidden ? 'oculto' : 'visible'}`, 'info');
            
            return true;
        }
        
        function testCanvasLateralButton() {
            log('≡ Iniciando test del botón de menú lateral...', 'info');
            
            const button = document.querySelector('.canvas-lateral-toggle');
            if (!button) {
                log('❌ Botón de menú lateral no encontrado', 'error');
                return false;
            }
            
            log('✅ Botón encontrado, simulando clic...', 'success');
            
            try {
                button.click();
                log('✅ Clic ejecutado correctamente', 'success');
                
                // Verificar canvas
                setTimeout(() => {
                    const canvas = document.getElementById('canvasLateral');
                    if (canvas) {
                        const isVisible = !canvas.classList.contains('translate-x-full');
                        log(`📱 Canvas lateral: ${isVisible ? 'visible' : 'oculto'}`, isVisible ? 'success' : 'warning');
                    }
                }, 100);
                
                return true;
            } catch (error) {
                log(`❌ Error al hacer clic: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testCanvasLateralPanel() {
            log('≡ Testando panel lateral...', 'info');
            
            const canvas = document.getElementById('canvasLateral');
            const overlay = document.getElementById('canvasLateralOverlay');
            
            if (!canvas) {
                log('❌ Canvas lateral no encontrado', 'error');
                return false;
            }
            
            if (!overlay) {
                log('❌ Overlay lateral no encontrado', 'error');
                return false;
            }
            
            log('✅ Elementos del panel encontrados', 'success');
            
            const isHidden = canvas.classList.contains('translate-x-full');
            log(`📱 Estado del panel: ${isHidden ? 'oculto' : 'visible'}`, 'info');
            
            return true;
        }
        
        function runFullDiagnostic() {
            log('🔧 Ejecutando diagnóstico completo...', 'info');
            
            const diagnostic = {
                elements: {},
                functions: {},
                instances: {},
                styles: {}
            };
            
            // Verificar elementos
            const elements = [
                '.fullscreen-toggle',
                '.notification-toggle', 
                '.canvas-lateral-toggle',
                '#notificationsCanvas',
                '#notificationsOverlay',
                '#canvasLateral',
                '#canvasLateralOverlay'
            ];
            
            elements.forEach(selector => {
                const element = document.querySelector(selector);
                diagnostic.elements[selector] = !!element;
                if (element) {
                    log(`✅ Elemento encontrado: ${selector}`, 'success');
                } else {
                    log(`❌ Elemento faltante: ${selector}`, 'error');
                }
            });
            
            // Verificar funciones
            const functions = [
                'initHeader',
                'getHeaderInstance',
                'destroyHeader'
            ];
            
            functions.forEach(funcName => {
                const func = window[funcName];
                diagnostic.functions[funcName] = typeof func === 'function';
                if (typeof func === 'function') {
                    log(`✅ Función disponible: ${funcName}()`, 'success');
                } else {
                    log(`❌ Función faltante: ${funcName}()`, 'error');
                }
            });
            
            // Verificar instancia
            const headerInstance = window.headerEnhancedInstance || window.getHeaderInstance?.();
            diagnostic.instances.headerEnhanced = !!headerInstance;
            
            if (headerInstance) {
                log('✅ Instancia HeaderEnhanced encontrada', 'success');
                diagnostic.instances.isInitialized = headerInstance.isInitialized;
                if (headerInstance.isInitialized) {
                    log('✅ HeaderEnhanced está inicializado', 'success');
                } else {
                    log('⚠️ HeaderEnhanced NO está inicializado', 'warning');
                }
            } else {
                log('❌ Instancia HeaderEnhanced NO encontrada', 'error');
            }
            
            // Actualizar info de diagnóstico
            const diagnosticInfo = document.getElementById('diagnostic-info');
            diagnosticInfo.innerHTML = `
                <div><strong>Elementos DOM:</strong> ${Object.values(diagnostic.elements).filter(Boolean).length}/${Object.keys(diagnostic.elements).length}</div>
                <div><strong>Funciones JS:</strong> ${Object.values(diagnostic.functions).filter(Boolean).length}/${Object.keys(diagnostic.functions).length}</div>
                <div><strong>Instancia Header:</strong> ${diagnostic.instances.headerEnhanced ? 'Encontrada' : 'No encontrada'}</div>
                <div><strong>Estado Inicialización:</strong> ${diagnostic.instances.isInitialized ? 'Inicializado' : 'No inicializado'}</div>
            `;
            
            log('🔧 Diagnóstico completo finalizado', 'info');
            return diagnostic;
        }
        
        function runAllTests() {
            log('🚀 Iniciando batería completa de tests...', 'info');
            
            // Ejecutar diagnóstico primero
            runFullDiagnostic();
            
            // Esperar un momento para el diagnóstico
            setTimeout(() => {
                // Tests individuales
                const results = {
                    fullscreen: testFullscreenButton(),
                    notifications: testNotificationButton(), 
                    canvasLateral: testCanvasLateralButton()
                };
                
                // Resultados finales
                const passed = Object.values(results).filter(Boolean).length;
                const total = Object.keys(results).length;
                
                if (passed === total) {
                    log(`🎉 Todos los tests pasaron correctamente (${passed}/${total})`, 'success');
                } else {
                    log(`⚠️ Tests completados con errores (${passed}/${total})`, 'warning');
                }
            }, 500);
        }
        
        // Inicializar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 Sistema de tests cargado correctamente', 'success');
            
            // Ejecutar diagnóstico inicial
            setTimeout(() => {
                runFullDiagnostic();
            }, 1000);
        });
        
        // Funciones globales para la consola
        window.testFullscreenButton = testFullscreenButton;
        window.testNotificationButton = testNotificationButton;
        window.testCanvasLateralButton = testCanvasLateralButton;
        window.runFullDiagnostic = runFullDiagnostic;
        window.runAllTests = runAllTests;
        
        console.log('%c🧪 Sistema de Tests del Header Cargado', 'color: green; font-weight: bold; font-size: 14px;');
        console.log('Funciones disponibles:');
        console.log('- testFullscreenButton()');
        console.log('- testNotificationButton()');
        console.log('- testCanvasLateralButton()');
        console.log('- runFullDiagnostic()');
        console.log('- runAllTests()');
    </script>
</body>
</html>
<?php
/**
 * API - Punto de entrada principal
 * 
 * Este archivo maneja todas las solicitudes a la API, enrutándolas
 * a los controladores correspondientes.
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

// Configurar encabezados CORS
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Responder a las solicitudes OPTIONS (preflight CORS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Incluir el autoloader y configuración
require_once __DIR__ . '/config/config.php';

// Analizar la solicitud para determinar qué ruta usar
$request_uri = $_SERVER['REQUEST_URI'];
$request_method = $_SERVER['REQUEST_METHOD'];

// Extraer la ruta de la API de la URL (por ejemplo, /api/v1/projects)
$api_pattern = '/\/api\/([^?]+)/';
preg_match($api_pattern, $request_uri, $matches);

// Si no hay coincidencia, devolver error 404
if (empty($matches[1])) {
    http_response_code(404);
    echo json_encode(['error' => 'Endpoint no encontrado']);
    exit;
}

// Obtener la ruta de la API sin parámetros de consulta
$api_path = trim($matches[1], '/');
$api_path_parts = explode('/', $api_path);

// Validar versión de la API
if (empty($api_path_parts[0]) || $api_path_parts[0] !== 'v1') {
    http_response_code(400);
    echo json_encode(['error' => 'Versión de API no soportada']);
    exit;
}

// Obtener el recurso solicitado (ej. 'projects')
$resource = $api_path_parts[1] ?? '';

// Obtener el ID si está presente (ej. 'projects/1')
$id = $api_path_parts[2] ?? null;

// Obtener parámetros de consulta
$query_params = [];
parse_str($_SERVER['QUERY_STRING'] ?? '', $query_params);

// Cargar controlador basado en el recurso
$controller_file = __DIR__ . "/v1/controllers/" . ucfirst($resource) . "Controller.php";

if (!file_exists($controller_file)) {
    http_response_code(404);
    echo json_encode(['error' => 'Recurso no encontrado']);
    exit;
}

require_once $controller_file;
$controller_class = ucfirst($resource) . "Controller";

if (!class_exists($controller_class)) {
    http_response_code(500);
    echo json_encode(['error' => 'Controlador no implementado']);
    exit;
}

// Instanciar el controlador
$controller = new $controller_class();

// Determinar el método a llamar basado en la solicitud HTTP
try {
    switch ($request_method) {
        case 'GET':
            if ($id) {
                // GET /api/v1/resource/:id
                $result = $controller->get($id, $query_params);
            } else {
                // GET /api/v1/resource
                $result = $controller->getAll($query_params);
            }
            break;
            
        case 'POST':
            // POST /api/v1/resource
            $data = json_decode(file_get_contents('php://input'), true);
            $result = $controller->create($data);
            break;
            
        case 'PUT':
            // PUT /api/v1/resource/:id
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'ID de recurso requerido para actualizar']);
                exit;
            }
            $data = json_decode(file_get_contents('php://input'), true);
            $result = $controller->update($id, $data);
            break;
            
        case 'DELETE':
            // DELETE /api/v1/resource/:id
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'ID de recurso requerido para eliminar']);
                exit;
            }
            $result = $controller->delete($id);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Método no permitido']);
            exit;
    }
    
    // Devolver la respuesta como JSON
    echo json_encode($result);
    
} catch (Exception $e) {
    // Manejar errores
    $status_code = $e->getCode() >= 400 && $e->getCode() < 600 ? $e->getCode() : 500;
    http_response_code($status_code);
    echo json_encode(['error' => $e->getMessage()]);
}
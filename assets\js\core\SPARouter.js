/**
 * Modern SPA Router for ProyectDash
 * 
 * Provides client-side routing with History API, route guards,
 * lazy loading, and transition effects.
 * 
 * <AUTHOR> with Claude Code
 * @version 2.0
 */

class SPARouter {
    constructor(options = {}) {
        this.routes = new Map();
        this.currentRoute = null;
        this.previousRoute = null;
        this.baseUrl = options.baseUrl || '';
        this.notFoundHandler = options.notFound || this.defaultNotFound;
        this.beforeEach = options.beforeEach || null;
        this.afterEach = options.afterEach || null;
        this.transitionDuration = options.transitionDuration || 300;
        this.defaultTransition = options.defaultTransition || 'fade';
        this.routeParams = {};
        this.queryParams = {};
        this.meta = {};
        this.lazyLoadCache = new Map();
        
        // Initialize
        this.init();
    }
    
    /**
     * Initialize the router
     */
    init() {
        // Listen to popstate events
        window.addEventListener('popstate', (e) => {
            const path = this.getCurrentPath();
            this.navigate(path, { skipPush: true, state: e.state });
        });
        
        // Intercept link clicks
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.shouldInterceptLink(link)) {
                e.preventDefault();
                const path = link.getAttribute('href');
                this.navigate(path);
            }
        });
        
        // Navigate to initial route
        const initialPath = this.getCurrentPath();
        this.navigate(initialPath, { skipPush: true, initial: true });
    }
    
    /**
     * Register a route
     * 
     * @param {string} path - Route path with parameters
     * @param {Object} options - Route options
     * @returns {SPARouter}
     */
    route(path, options) {
        const route = {
            path,
            pattern: this.pathToRegex(path),
            component: options.component,
            lazy: options.lazy || null,
            guard: options.guard || null,
            meta: options.meta || {},
            transition: options.transition || this.defaultTransition,
            title: options.title || null,
            name: options.name || null,
            children: options.children || []
        };
        
        this.routes.set(path, route);
        
        // Register named route
        if (route.name) {
            this.routes.set(`name:${route.name}`, route);
        }
        
        // Register child routes
        if (route.children.length > 0) {
            this.registerChildRoutes(route);
        }
        
        return this;
    }
    
    /**
     * Register child routes
     * 
     * @param {Object} parentRoute
     */
    registerChildRoutes(parentRoute) {
        parentRoute.children.forEach(child => {
            const childPath = parentRoute.path + child.path;
            const childRoute = {
                ...child,
                path: childPath,
                pattern: this.pathToRegex(childPath),
                parent: parentRoute
            };
            
            this.routes.set(childPath, childRoute);
            
            if (childRoute.name) {
                this.routes.set(`name:${childRoute.name}`, childRoute);
            }
            
            if (childRoute.children && childRoute.children.length > 0) {
                this.registerChildRoutes(childRoute);
            }
        });
    }
    
    /**
     * Navigate to a route
     * 
     * @param {string} path - Route path
     * @param {Object} options - Navigation options
     * @returns {Promise}
     */
    async navigate(path, options = {}) {
        // Normalize path
        path = this.normalizePath(path);
        
        // Find matching route
        const matchResult = this.matchRoute(path);
        
        if (!matchResult) {
            return this.handleNotFound(path);
        }
        
        const { route, params } = matchResult;
        
        // Extract query parameters
        const queryParams = this.extractQueryParams(path);
        
        // Create route context
        const context = {
            path,
            route,
            params,
            query: queryParams,
            meta: route.meta,
            from: this.currentRoute
        };
        
        // Run global before guard
        if (this.beforeEach) {
            const guardResult = await this.beforeEach(context);
            if (guardResult === false) {
                return false;
            }
            if (typeof guardResult === 'string') {
                return this.navigate(guardResult);
            }
        }
        
        // Run route guard
        if (route.guard) {
            const guardResult = await route.guard(context);
            if (guardResult === false) {
                return false;
            }
            if (typeof guardResult === 'string') {
                return this.navigate(guardResult);
            }
        }
        
        // Update state
        this.previousRoute = this.currentRoute;
        this.currentRoute = context;
        this.routeParams = params;
        this.queryParams = queryParams;
        this.meta = route.meta;
        
        // Update browser history
        if (!options.skipPush) {
            const state = {
                path,
                params,
                query: queryParams,
                meta: route.meta,
                ...options.state
            };
            
            if (options.replace) {
                window.history.replaceState(state, '', this.baseUrl + path);
            } else {
                window.history.pushState(state, '', this.baseUrl + path);
            }
        }
        
        // Update document title
        if (route.title) {
            document.title = typeof route.title === 'function' 
                ? route.title(context) 
                : route.title;
        }
        
        // Load and render component
        try {
            await this.loadAndRenderComponent(route, context, options);
            
            // Run global after guard
            if (this.afterEach) {
                await this.afterEach(context);
            }
            
            // Emit navigation event
            this.emit('navigation', context);
            
            return true;
        } catch (error) {
            console.error('Navigation error:', error);
            this.emit('navigation-error', { error, context });
            return false;
        }
    }
    
    /**
     * Load and render component
     * 
     * @param {Object} route
     * @param {Object} context
     * @param {Object} options
     */
    async loadAndRenderComponent(route, context, options = {}) {
        let component = route.component;
        
        // Handle lazy loading
        if (route.lazy && !component) {
            component = await this.lazyLoad(route.lazy);
            route.component = component; // Cache for next time
        }
        
        // Get container element
        const container = document.getElementById('app') || document.body;
        
        // Apply transition
        if (!options.initial && route.transition) {
            await this.applyTransition(container, route.transition, 'out');
        }
        
        // Render component
        if (typeof component === 'function') {
            const rendered = await component(context);
            if (typeof rendered === 'string') {
                container.innerHTML = rendered;
            } else if (rendered instanceof HTMLElement) {
                container.innerHTML = '';
                container.appendChild(rendered);
            } else if (rendered && typeof rendered.render === 'function') {
                // Support for component objects with render method
                await rendered.render(container, context);
            }
        } else if (typeof component === 'string') {
            container.innerHTML = component;
        } else if (component instanceof HTMLElement) {
            container.innerHTML = '';
            container.appendChild(component.cloneNode(true));
        }
        
        // Apply transition
        if (!options.initial && route.transition) {
            await this.applyTransition(container, route.transition, 'in');
        }
        
        // Initialize any JavaScript for the new content
        this.initializeContent(container, context);
    }
    
    /**
     * Lazy load a component
     * 
     * @param {Function|string} loader
     * @returns {Promise}
     */
    async lazyLoad(loader) {
        // Check cache
        if (this.lazyLoadCache.has(loader)) {
            return this.lazyLoadCache.get(loader);
        }
        
        let component;
        
        if (typeof loader === 'function') {
            // Dynamic import function
            component = await loader();
            
            // Handle ES modules
            if (component.default) {
                component = component.default;
            }
        } else if (typeof loader === 'string') {
            // URL to fetch
            const response = await fetch(loader);
            const text = await response.text();
            
            // Create component from HTML
            component = () => text;
        }
        
        // Cache the component
        this.lazyLoadCache.set(loader, component);
        
        return component;
    }
    
    /**
     * Apply transition effect
     * 
     * @param {HTMLElement} element
     * @param {string} transition
     * @param {string} direction
     */
    async applyTransition(element, transition, direction) {
        return new Promise(resolve => {
            const className = `route-transition-${transition}-${direction}`;
            element.classList.add(className);
            
            if (direction === 'out') {
                setTimeout(() => {
                    element.classList.remove(className);
                    resolve();
                }, this.transitionDuration);
            } else {
                // Force reflow
                element.offsetHeight;
                
                requestAnimationFrame(() => {
                    element.classList.remove(className);
                    setTimeout(resolve, this.transitionDuration);
                });
            }
        });
    }
    
    /**
     * Initialize content after navigation
     * 
     * @param {HTMLElement} container
     * @param {Object} context
     */
    initializeContent(container, context) {
        // Dispatch custom event
        const event = new CustomEvent('route-loaded', {
            detail: context,
            bubbles: true
        });
        container.dispatchEvent(event);
        
        // Auto-focus first input if exists
        const firstInput = container.querySelector('input:not([type="hidden"]), textarea, select');
        if (firstInput) {
            firstInput.focus();
        }
        
        // Scroll to top or hash
        if (window.location.hash) {
            const element = document.querySelector(window.location.hash);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }
    
    /**
     * Match a route against the path
     * 
     * @param {string} path
     * @returns {Object|null}
     */
    matchRoute(path) {
        // Remove query string for matching
        const pathWithoutQuery = path.split('?')[0];
        
        for (const [_, route] of this.routes) {
            if (route.name) continue; // Skip named route entries
            
            const match = pathWithoutQuery.match(route.pattern);
            if (match) {
                const params = this.extractParams(route.path, match);
                return { route, params };
            }
        }
        
        return null;
    }
    
    /**
     * Convert path pattern to regex
     * 
     * @param {string} path
     * @returns {RegExp}
     */
    pathToRegex(path) {
        // Escape special regex characters except : and *
        const escaped = path.replace(/[|\\{}()[\]^$+?.]/g, '\\$&');
        
        // Convert :param to named capture groups
        const withParams = escaped.replace(/:(\w+)/g, '(?<$1>[^/]+)');
        
        // Convert * to wildcard
        const withWildcard = withParams.replace(/\*/g, '.*');
        
        return new RegExp(`^${withWildcard}$`);
    }
    
    /**
     * Extract parameters from route match
     * 
     * @param {string} pattern
     * @param {Array} match
     * @returns {Object}
     */
    extractParams(pattern, match) {
        const params = {};
        
        // Extract named groups
        if (match.groups) {
            Object.assign(params, match.groups);
        }
        
        // Extract positional params
        const paramNames = pattern.match(/:(\w+)/g);
        if (paramNames) {
            paramNames.forEach((name, index) => {
                const paramName = name.substring(1);
                if (!params[paramName] && match[index + 1]) {
                    params[paramName] = match[index + 1];
                }
            });
        }
        
        return params;
    }
    
    /**
     * Extract query parameters
     * 
     * @param {string} path
     * @returns {Object}
     */
    extractQueryParams(path) {
        const queryString = path.split('?')[1];
        if (!queryString) return {};
        
        const params = {};
        const searchParams = new URLSearchParams(queryString);
        
        for (const [key, value] of searchParams) {
            if (params[key]) {
                // Handle multiple values
                if (Array.isArray(params[key])) {
                    params[key].push(value);
                } else {
                    params[key] = [params[key], value];
                }
            } else {
                params[key] = value;
            }
        }
        
        return params;
    }
    
    /**
     * Handle not found route
     * 
     * @param {string} path
     */
    async handleNotFound(path) {
        if (this.notFoundHandler) {
            await this.notFoundHandler(path);
        }
        
        this.emit('not-found', { path });
    }
    
    /**
     * Default not found handler
     * 
     * @param {string} path
     */
    defaultNotFound(path) {
        const container = document.getElementById('app') || document.body;
        container.innerHTML = `
            <div class="error-page">
                <h1>404 - Page Not Found</h1>
                <p>The page "${path}" could not be found.</p>
                <a href="/">Go to Home</a>
            </div>
        `;
    }
    
    /**
     * Get current path
     * 
     * @returns {string}
     */
    getCurrentPath() {
        const path = window.location.pathname.replace(this.baseUrl, '') || '/';
        return path + window.location.search + window.location.hash;
    }
    
    /**
     * Normalize path
     * 
     * @param {string} path
     * @returns {string}
     */
    normalizePath(path) {
        // Handle named routes
        if (path.startsWith('name:')) {
            const route = this.routes.get(path);
            if (route) {
                path = route.path;
            }
        }
        
        // Ensure path starts with /
        if (!path.startsWith('/')) {
            path = '/' + path;
        }
        
        // Remove duplicate slashes
        path = path.replace(/\/+/g, '/');
        
        return path;
    }
    
    /**
     * Check if link should be intercepted
     * 
     * @param {HTMLAnchorElement} link
     * @returns {boolean}
     */
    shouldInterceptLink(link) {
        // Skip if has target
        if (link.target && link.target !== '_self') return false;
        
        // Skip if has download attribute
        if (link.hasAttribute('download')) return false;
        
        // Skip if external link
        const href = link.getAttribute('href');
        if (!href || href.startsWith('http') || href.startsWith('//')) return false;
        
        // Skip if has data-no-router attribute
        if (link.hasAttribute('data-no-router')) return false;
        
        // Skip if mailto or tel link
        if (href.startsWith('mailto:') || href.startsWith('tel:')) return false;
        
        return true;
    }
    
    /**
     * Go back in history
     */
    back() {
        window.history.back();
    }
    
    /**
     * Go forward in history
     */
    forward() {
        window.history.forward();
    }
    
    /**
     * Go to specific history index
     * 
     * @param {number} delta
     */
    go(delta) {
        window.history.go(delta);
    }
    
    /**
     * Replace current route
     * 
     * @param {string} path
     * @param {Object} options
     */
    replace(path, options = {}) {
        return this.navigate(path, { ...options, replace: true });
    }
    
    /**
     * Generate URL for a route
     * 
     * @param {string} name
     * @param {Object} params
     * @param {Object} query
     * @returns {string}
     */
    url(name, params = {}, query = {}) {
        const route = this.routes.get(`name:${name}`);
        if (!route) {
            console.warn(`Route with name "${name}" not found`);
            return '/';
        }
        
        let path = route.path;
        
        // Replace parameters
        Object.entries(params).forEach(([key, value]) => {
            path = path.replace(`:${key}`, value);
        });
        
        // Add query parameters
        if (Object.keys(query).length > 0) {
            const queryString = new URLSearchParams(query).toString();
            path += '?' + queryString;
        }
        
        return path;
    }
    
    /**
     * Emit event
     * 
     * @param {string} eventName
     * @param {Object} data
     */
    emit(eventName, data) {
        const event = new CustomEvent(`router:${eventName}`, {
            detail: data,
            bubbles: true
        });
        window.dispatchEvent(event);
    }
    
    /**
     * Add route guard
     * 
     * @param {Function} guard
     */
    addGuard(guard) {
        const originalBeforeEach = this.beforeEach;
        this.beforeEach = async (context) => {
            if (originalBeforeEach) {
                const result = await originalBeforeEach(context);
                if (result === false || typeof result === 'string') {
                    return result;
                }
            }
            return guard(context);
        };
    }
    
    /**
     * Get current route
     * 
     * @returns {Object|null}
     */
    getCurrentRoute() {
        return this.currentRoute;
    }
    
    /**
     * Get route parameters
     * 
     * @returns {Object}
     */
    getParams() {
        return { ...this.routeParams };
    }
    
    /**
     * Get query parameters
     * 
     * @returns {Object}
     */
    getQuery() {
        return { ...this.queryParams };
    }
    
    /**
     * Get route meta
     * 
     * @returns {Object}
     */
    getMeta() {
        return { ...this.meta };
    }
}

// Export for use
window.SPARouter = SPARouter;
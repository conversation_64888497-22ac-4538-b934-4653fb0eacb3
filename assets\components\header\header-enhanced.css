/**
 * Header Enhanced Styles
 * 
 * Estilos mejorados para el componente FuseHeader
 * - Canvas lateral de notificaciones
 * - Animaciones y transiciones
 * - Temas y responsividad
 */

/* Canvas de notificaciones */
#notificationsCanvas {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /* Fallback para navegadores sin soporte */
    background-color: rgba(255, 255, 255, 0.95);
}

/* Overlay de notificaciones */
#notificationsOverlay {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: opacity 0.3s ease-in-out;
}

/* Animación de entrada del canvas */
#notificationsCanvas.show {
    animation: slideInRight 0.3s ease-out;
}

/* Animación de salida del canvas */
#notificationsCanvas.hide {
    animation: slideOutRight 0.3s ease-in;
}

/* Animaciones personalizadas */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Items de notificación */
.notification-item {
    animation: fadeIn 0.3s ease-out;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.notification-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.notification-item:hover::before {
    left: 100%;
}

.notification-item:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Notificaciones no leídas */
.notification-item:not(.bg-gray-50) {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* Iconos de notificación */
.notification-item .text-xl {
    transition: all 0.2s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.notification-item:hover .text-xl {
    transform: scale(1.1);
}

/* Botones de acción */
.notification-item button {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.notification-item button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.notification-item button:active::before {
    width: 100px;
    height: 100px;
}

/* Header del canvas */
#notificationsCanvas .border-b {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Footer del canvas */
#notificationsCanvas .border-t {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-top: 1px solid #e2e8f0;
}

/* Botón principal del footer */
#notificationsCanvas .bg-sky-500 {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

#notificationsCanvas .bg-sky-500:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.4);
}

/* Botones de acción en header */
.clear-all-notifications,
.close-notifications {
    transition: all 0.2s ease;
    position: relative;
}

.clear-all-notifications:hover,
.close-notifications:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: scale(1.05);
}

.clear-all-notifications:active,
.close-notifications:active {
    transform: scale(0.95);
}

/* Botón de notificación */
.notification-toggle {
    transition: all 0.2s ease;
    position: relative;
}

.notification-toggle:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.notification-toggle:active {
    transform: translateY(0);
}

/* Badge de notificación */
.notification-toggle .absolute {
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
}

/* Botón de pantalla completa */
.fullscreen-toggle {
    transition: all 0.2s ease;
    position: relative;
}

.fullscreen-toggle:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.fullscreen-toggle:active {
    transform: translateY(0);
}

/* Iconos de pantalla completa */
.fullscreen-icon,
.fullscreen-exit-icon {
    transition: all 0.2s ease;
}

.fullscreen-toggle:hover .fullscreen-icon,
.fullscreen-toggle:hover .fullscreen-exit-icon {
    transform: scale(1.1);
}

/* Scrollbar personalizada para canvas */
#notificationsCanvas .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#notificationsCanvas .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#notificationsCanvas .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background 0.2s ease;
}

#notificationsCanvas .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Efecto de carga para notificaciones */
.notification-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.notification-item.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    #notificationsCanvas {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    #notificationsCanvas {
        width: 100%;
    }
    
    #notificationsCanvas .p-4 {
        padding: 1rem;
    }
    
    .notification-item {
        padding: 1rem;
    }
}

/* Modo oscuro soporte */
@media (prefers-color-scheme: dark) {
    #notificationsCanvas {
        background: #1e293b;
        border-left-color: #334155;
    }
    
    #notificationsCanvas .border-b,
    #notificationsCanvas .border-t {
        border-color: #334155;
    }
    
    #notificationsCanvas .text-gray-900 {
        color: #f1f5f9;
    }
    
    #notificationsCanvas .text-gray-600 {
        color: #cbd5e1;
    }
    
    #notificationsCanvas .text-gray-500 {
        color: #94a3b8;
    }
    
    #notificationsCanvas .bg-gray-50 {
        background: #334155;
    }
    
    #notificationsCanvas .hover\:bg-gray-100:hover {
        background: #475569;
    }
    
    #notificationsCanvas .bg-sky-500 {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    }
    
    #notificationsCanvas .bg-sky-500:hover {
        background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    }
}

/* Animación de entrada para el overlay */
#notificationsOverlay.show {
    animation: fadeIn 0.3s ease-out;
}

/* Efecto de zoom para notificaciones importantes */
.notification-item.important {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.notification-item.important .text-xl {
    color: #ef4444;
    animation: pulse 2s infinite;
}

/* Tooltips personalizados */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1e293b;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.25rem;
}

[data-tooltip]:hover::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1e293b;
    z-index: 999;
    margin-bottom: 2px;
}
/* Canvas Lateral */
#canvasLateral {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    /* Fallback para navegadores sin soporte */
    background-color: rgba(255, 255, 255, 0.98);
}

/* Overlay del canvas lateral */
#canvasLateralOverlay {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: opacity 0.3s ease-in-out;
}

/* Animación de entrada del canvas lateral */
#canvasLateral.show {
    animation: slideInRight 0.3s ease-out;
}

/* Animación de salida del canvas lateral */
#canvasLateral.hide {
    animation: slideOutRight 0.3s ease-in;
}

/* Botón de canvas lateral */
.canvas-lateral-toggle {
    transition: all 0.2s ease;
    position: relative;
}

.canvas-lateral-toggle:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.canvas-lateral-toggle:active {
    transform: translateY(0);
}

/* Icono del canvas lateral */
.canvas-lateral-toggle svg {
    transition: all 0.2s ease;
}

.canvas-lateral-toggle:hover svg {
    transform: scale(1.1);
}

/* Header del canvas lateral */
#canvasLateral .border-b {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Footer del canvas lateral */
#canvasLateral .border-t {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-top: 1px solid #e2e8f0;
}

/* Botón principal del footer del canvas lateral */
#canvasLateral .bg-sky-500 {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

#canvasLateral .bg-sky-500:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.4);
}

/* Botones de acción en header del canvas lateral */
.close-canvas-lateral {
    transition: all 0.2s ease;
    position: relative;
}

.close-canvas-lateral:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: scale(1.05);
}

.close-canvas-lateral:active {
    transform: scale(0.95);
}

/* Tarjetas de contenido del canvas lateral */
#canvasLateral .bg-blue-50 {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-left: 4px solid #3b82f6;
    transition: all 0.2s ease;
}

#canvasLateral .bg-blue-50:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

#canvasLateral .bg-green-50 {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-left: 4px solid #22c55e;
    transition: all 0.2s ease;
}

#canvasLateral .bg-green-50:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}

#canvasLateral .bg-yellow-50 {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border-left: 4px solid #eab308;
    transition: all 0.2s ease;
}

#canvasLateral .bg-yellow-50:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(234, 179, 8, 0.15);
}

/* Scrollbar personalizada para canvas lateral */
#canvasLateral .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#canvasLateral .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#canvasLateral .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background 0.2s ease;
}

#canvasLateral .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Responsive design para canvas lateral */
@media (max-width: 768px) {
    #canvasLateral {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    #canvasLateral {
        width: 100%;
    }
    
    #canvasLateral .p-4 {
        padding: 1rem;
    }
}

/* Modo oscuro soporte para canvas lateral */
@media (prefers-color-scheme: dark) {
    #canvasLateral {
        background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        border-left-color: #334155;
    }
    
    #canvasLateral .border-b,
    #canvasLateral .border-t {
        border-color: #334155;
    }
    
    #canvasLateral .text-gray-900 {
        color: #f1f5f9;
    }
    
    #canvasLateral .text-blue-900 {
        color: #bfdbfe;
    }
    
    #canvasLateral .text-blue-700 {
        color: #93c5fd;
    }
    
    #canvasLateral .text-green-900 {
        color: #bbf7d0;
    }
    
    #canvasLateral .text-green-700 {
        color: #86efac;
    }
    
    #canvasLateral .text-yellow-900 {
        color: #fef08a;
    }
    
    #canvasLateral .text-yellow-700 {
        color: #fde047;
    }
    
    #canvasLateral .bg-blue-50 {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        border-left-color: #3b82f6;
    }
    
    #canvasLateral .bg-green-50 {
        background: linear-gradient(135deg, #14532d 0%, #166534 100%);
        border-left-color: #22c55e;
    }
    
    #canvasLateral .bg-yellow-50 {
        background: linear-gradient(135deg, #422006 0%, #713f12 100%);
        border-left-color: #eab308;
    }
    
    #canvasLateral .hover\:bg-gray-100:hover {
        background: #475569;
    }
}

/* Estilos adicionales para accesibilidad */
.fullscreen-toggle:focus,
.notification-toggle:focus,
.canvas-lateral-toggle:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Animación de carga para inicialización */
.header-loading {
    position: relative;
    overflow: hidden;
}

.header-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    animation: headerLoading 1.5s ease-in-out;
}

@keyframes headerLoading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Estados de error para debugging */
.header-error {
    border: 2px solid #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1) !important;
}

.header-success {
    border: 2px solid #22c55e !important;
    background-color: rgba(34, 197, 94, 0.1) !important;
}

/* Tooltip mejorado para botones */
[data-header-tooltip] {
    position: relative;
}

[data-header-tooltip]:hover::before {
    content: attr(data-header-tooltip);
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

[data-header-tooltip]:hover::after {
    content: '';
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
    z-index: 999;
    margin-bottom: 1px;
}

/* Mejora de contraste para modo oscuro */
@media (prefers-color-scheme: dark) {
    [data-header-tooltip]:hover::before {
        background: #f9fafb;
        color: #1f2937;
        border: 1px solid #374151;
    }
    
    [data-header-tooltip]:hover::after {
        border-top-color: #f9fafb;
    }
}
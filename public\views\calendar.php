<!-- Calendar Page -->
<div class="page-content">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h1 class="page-title">Calendar</h1>
        <div class="page-actions">
            <button class="btn btn-secondary btn-sm mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                </svg>
                Import
            </button>
            <button class="btn btn-secondary btn-sm mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export
            </button>
            <button class="btn btn-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Event
            </button>
        </div>
    </div>

    <!-- Calendar Container -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="card-title">Mini Calendar</h2>
                </div>
                <div class="card-body p-0">
                    <!-- Mini Calendar -->
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-4">
                            <button class="text-gray-600 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                            </button>
                            <h3 class="text-base font-medium text-gray-900">July 2023</h3>
                            <button class="text-gray-600 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                        <div class="grid grid-cols-7 text-center text-xs text-gray-500 mb-2">
                            <div>Su</div>
                            <div>Mo</div>
                            <div>Tu</div>
                            <div>We</div>
                            <div>Th</div>
                            <div>Fr</div>
                            <div>Sa</div>
                        </div>
                        <div class="grid grid-cols-7 text-center">
                            <div class="py-1 text-gray-400">25</div>
                            <div class="py-1 text-gray-400">26</div>
                            <div class="py-1 text-gray-400">27</div>
                            <div class="py-1 text-gray-400">28</div>
                            <div class="py-1 text-gray-400">29</div>
                            <div class="py-1 text-gray-400">30</div>
                            <div class="py-1">1</div>
                            <div class="py-1">2</div>
                            <div class="py-1">3</div>
                            <div class="py-1">4</div>
                            <div class="py-1">5</div>
                            <div class="py-1">6</div>
                            <div class="py-1">7</div>
                            <div class="py-1">8</div>
                            <div class="py-1">9</div>
                            <div class="py-1">10</div>
                            <div class="py-1">11</div>
                            <div class="py-1">12</div>
                            <div class="py-1">13</div>
                            <div class="py-1">14</div>
                            <div class="py-1">15</div>
                            <div class="py-1 bg-blue-500 text-white rounded-full">16</div>
                            <div class="py-1 relative">
                                17
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
                            </div>
                            <div class="py-1 relative">
                                18
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-500 rounded-full"></div>
                            </div>
                            <div class="py-1">19</div>
                            <div class="py-1 relative">
                                20
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-red-500 rounded-full"></div>
                            </div>
                            <div class="py-1">21</div>
                            <div class="py-1">22</div>
                            <div class="py-1 relative">
                                23
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-purple-500 rounded-full"></div>
                            </div>
                            <div class="py-1">24</div>
                            <div class="py-1">25</div>
                            <div class="py-1 relative">
                                26
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-yellow-500 rounded-full"></div>
                            </div>
                            <div class="py-1">27</div>
                            <div class="py-1">28</div>
                            <div class="py-1">29</div>
                            <div class="py-1 relative">
                                30
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
                            </div>
                            <div class="py-1">31</div>
                            <div class="py-1 text-gray-400">1</div>
                            <div class="py-1 text-gray-400">2</div>
                            <div class="py-1 text-gray-400">3</div>
                            <div class="py-1 text-gray-400">4</div>
                            <div class="py-1 text-gray-400">5</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="card-title">My Calendars</h2>
                    <button class="btn-sm btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                    </button>
                </div>
                <div class="card-body">
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-gray-900">Personal</span>
                            <label class="ml-auto inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                            </label>
                        </li>
                        <li class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-gray-900">Work</span>
                            <label class="ml-auto inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                            </label>
                        </li>
                        <li class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            <span class="text-gray-900">Family</span>
                            <label class="ml-auto inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                            </label>
                        </li>
                        <li class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-gray-900">Holidays</span>
                            <label class="ml-auto inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                            </label>
                        </li>
                        <li class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                            <span class="text-gray-900">Reminders</span>
                            <label class="ml-auto inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                            </label>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Upcoming Events</h2>
                </div>
                <div class="card-body p-0">
                    <ul class="divide-y divide-gray-200">
                        <li class="p-4">
                            <div class="flex items-start">
                                <div class="w-2 h-2 mt-1.5 rounded-full bg-blue-500 mr-3"></div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Team Meeting</h3>
                                    <p class="text-sm text-gray-500">Today, 2:00 PM - 3:30 PM</p>
                                </div>
                            </div>
                        </li>
                        <li class="p-4">
                            <div class="flex items-start">
                                <div class="w-2 h-2 mt-1.5 rounded-full bg-green-500 mr-3"></div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Client Presentation</h3>
                                    <p class="text-sm text-gray-500">Tomorrow, 10:00 AM - 11:30 AM</p>
                                </div>
                            </div>
                        </li>
                        <li class="p-4">
                            <div class="flex items-start">
                                <div class="w-2 h-2 mt-1.5 rounded-full bg-purple-500 mr-3"></div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Sarah's Birthday</h3>
                                    <p class="text-sm text-gray-500">July 23, All day</p>
                                </div>
                            </div>
                        </li>
                        <li class="p-4">
                            <div class="flex items-start">
                                <div class="w-2 h-2 mt-1.5 rounded-full bg-yellow-500 mr-3"></div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Project Deadline</h3>
                                    <p class="text-sm text-gray-500">July 26</p>
                                </div>
                            </div>
                        </li>
                        <li class="p-4">
                            <div class="flex items-start">
                                <div class="w-2 h-2 mt-1.5 rounded-full bg-red-500 mr-3"></div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Quarterly Review</h3>
                                    <p class="text-sm text-gray-500">July 30, 9:00 AM - 12:00 PM</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Main Calendar -->
        <div class="lg:col-span-3">
            <div class="card mb-4">
                <div class="card-header">
                    <div class="flex items-center">
                        <button class="mr-4 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <h2 class="text-xl font-bold text-gray-900">July 2023</h2>
                        <button class="ml-4 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>
                    <div class="flex space-x-2">
                        <button class="btn btn-sm btn-secondary active">Month</button>
                        <button class="btn btn-sm btn-secondary">Week</button>
                        <button class="btn btn-sm btn-secondary">Day</button>
                        <button class="btn btn-sm btn-secondary">List</button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="calendar-container">
                        <!-- Calendar Header -->
                        <div class="grid grid-cols-7 text-center py-2 border-b border-gray-200 bg-gray-50">
                            <div class="text-sm font-medium text-gray-500">Sunday</div>
                            <div class="text-sm font-medium text-gray-500">Monday</div>
                            <div class="text-sm font-medium text-gray-500">Tuesday</div>
                            <div class="text-sm font-medium text-gray-500">Wednesday</div>
                            <div class="text-sm font-medium text-gray-500">Thursday</div>
                            <div class="text-sm font-medium text-gray-500">Friday</div>
                            <div class="text-sm font-medium text-gray-500">Saturday</div>
                        </div>

                        <!-- Calendar Grid -->
                        <div class="calendar-grid">
                            <!-- Week 1 -->
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">25</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">26</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">27</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">28</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">29</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">30</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">1</div>
                            </div>

                            <!-- Week 2 -->
                            <div class="calendar-day">
                                <div class="calendar-day-header">2</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">3</div>
                                <div class="calendar-event bg-green-100 text-green-800 border-l-4 border-green-500">
                                    9:00 AM - Project Kickoff
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">4</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">5</div>
                                <div class="calendar-event bg-blue-100 text-blue-800 border-l-4 border-blue-500">
                                    2:30 PM - Team Meeting
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">6</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">7</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">8</div>
                                <div class="calendar-event bg-purple-100 text-purple-800 border-l-4 border-purple-500">
                                    All Day - Conference
                                </div>
                            </div>

                            <!-- Week 3 -->
                            <div class="calendar-day">
                                <div class="calendar-day-header">9</div>
                                <div class="calendar-event bg-purple-100 text-purple-800 border-l-4 border-purple-500">
                                    All Day - Conference
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">10</div>
                                <div class="calendar-event bg-purple-100 text-purple-800 border-l-4 border-purple-500">
                                    All Day - Conference
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">11</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">12</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">13</div>
                                <div class="calendar-event bg-yellow-100 text-yellow-800 border-l-4 border-yellow-500">
                                    1:00 PM - Client Call
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">14</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">15</div>
                            </div>

                            <!-- Week 4 -->
                            <div class="calendar-day bg-blue-50">
                                <div class="calendar-day-header font-bold text-blue-600">16</div>
                                <div class="calendar-event bg-blue-100 text-blue-800 border-l-4 border-blue-500">
                                    10:00 AM - Team Meeting
                                </div>
                                <div class="calendar-event bg-green-100 text-green-800 border-l-4 border-green-500">
                                    2:00 PM - Design Review
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">17</div>
                                <div class="calendar-event bg-blue-100 text-blue-800 border-l-4 border-blue-500">
                                    11:30 AM - Standup
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">18</div>
                                <div class="calendar-event bg-green-100 text-green-800 border-l-4 border-green-500">
                                    9:00 AM - Client Presentation
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">19</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">20</div>
                                <div class="calendar-event bg-red-100 text-red-800 border-l-4 border-red-500">
                                    3:00 PM - Deadline
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">21</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">22</div>
                            </div>

                            <!-- Week 5 -->
                            <div class="calendar-day">
                                <div class="calendar-day-header">23</div>
                                <div class="calendar-event bg-purple-100 text-purple-800 border-l-4 border-purple-500">
                                    All Day - Sarah's Birthday
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">24</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">25</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">26</div>
                                <div class="calendar-event bg-yellow-100 text-yellow-800 border-l-4 border-yellow-500">
                                    All Day - Project Deadline
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">27</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">28</div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">29</div>
                            </div>

                            <!-- Week 6 -->
                            <div class="calendar-day">
                                <div class="calendar-day-header">30</div>
                                <div class="calendar-event bg-blue-100 text-blue-800 border-l-4 border-blue-500">
                                    9:00 AM - Quarterly Review
                                </div>
                            </div>
                            <div class="calendar-day">
                                <div class="calendar-day-header">31</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">1</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">2</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">3</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">4</div>
                            </div>
                            <div class="calendar-day bg-gray-50">
                                <div class="calendar-day-header text-gray-400">5</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Details -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Today's Events</h2>
                    <div>
                        <button class="btn btn-sm btn-secondary">View All</button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <ul class="divide-y divide-gray-200">
                        <li class="p-4">
                            <div class="flex justify-between items-start">
                                <div class="flex">
                                    <div class="w-12 text-center">
                                        <span class="text-sm font-medium text-gray-900">10:00</span>
                                        <span class="text-xs text-gray-500 block">AM</span>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="font-medium text-gray-900">Team Meeting</h3>
                                        <p class="text-sm text-gray-500">Conference Room A</p>
                                        <div class="flex items-center mt-2">
                                            <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
                                            <span class="text-xs text-gray-500">Personal</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="btn btn-sm btn-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                    </button>
                                    <button class="btn btn-sm btn-icon text-red-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </li>
                        <li class="p-4">
                            <div class="flex justify-between items-start">
                                <div class="flex">
                                    <div class="w-12 text-center">
                                        <span class="text-sm font-medium text-gray-900">2:00</span>
                                        <span class="text-xs text-gray-500 block">PM</span>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="font-medium text-gray-900">Design Review</h3>
                                        <p class="text-sm text-gray-500">Virtual Meeting</p>
                                        <div class="flex items-center mt-2">
                                            <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                                            <span class="text-xs text-gray-500">Work</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="btn btn-sm btn-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                    </button>
                                    <button class="btn btn-sm btn-icon text-red-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </li>
                        <li class="p-4">
                            <div class="flex justify-between items-start">
                                <div class="flex">
                                    <div class="w-12 text-center">
                                        <span class="text-sm font-medium text-gray-900">4:30</span>
                                        <span class="text-xs text-gray-500 block">PM</span>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="font-medium text-gray-900">Project Planning</h3>
                                        <p class="text-sm text-gray-500">Meeting Room B</p>
                                        <div class="flex items-center mt-2">
                                            <span class="w-2 h-2 rounded-full bg-yellow-500 mr-2"></span>
                                            <span class="text-xs text-gray-500">Reminders</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="btn btn-sm btn-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                    </button>
                                    <button class="btn btn-sm btn-icon text-red-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
/**
 * Configuración de Base de Datos
 * 
 * Configuración centralizada para conexiones MySQL con soporte
 * para múltiples ambientes (desarrollo, producción, pruebas)
 * 
 * <AUTHOR> with <PERSON>
 * @version 1.0
 */

// Detectar ambiente actual
$environment = $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? 'development';

// Configuraciones por ambiente
$configs = [
    'development' => [
        'host' => '**************',
        'port' => 3306,
        'database' => 'operaciones_tqw',
        'username' => 'ncornejo',
        'password' => 'N1c0l7as17',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'persistent' => false,
        'timeout' => 30,
        'logging' => true,
        'log_file' => __DIR__ . '/../logs/database_dev.log',
        'options' => [
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false, // Solo desarrollo
        ]
    ],
    
    'production' => [
        'host' => '**************',
        'port' => 3306,
        'database' => 'operaciones_tqw',
        'username' => 'ncornejo',
        'password' => 'N1c0l7as17',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'persistent' => true, // Conexiones persistentes en producción
        'timeout' => 10,
        'logging' => false, // Deshabilitado en producción por performance
        'log_file' => __DIR__ . '/../logs/database_prod.log',
        'options' => [
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => true,
            PDO::MYSQL_ATTR_SSL_CA => __DIR__ . '/ssl/ca-cert.pem', // Certificado SSL
        ]
    ],
    
    'testing' => [
        'host' => '**************',
        'port' => 3306,
        'database' => 'operaciones_tqw_test',
        'username' => 'ncornejo',
        'password' => 'N1c0l7as17',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'persistent' => false,
        'timeout' => 15,
        'logging' => true,
        'log_file' => __DIR__ . '/../logs/database_test.log',
        'options' => [
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ]
];

// Validar que el ambiente existe
if (!isset($configs[$environment])) {
    throw new InvalidArgumentException("Ambiente no válido: $environment");
}

// Configuración del ambiente actual
$config = $configs[$environment];

// Configuraciones adicionales comunes
$config['environment'] = $environment;

// Pool de conexiones - configuración avanzada
$config['pool'] = [
    'min_connections' => $environment === 'production' ? 5 : 2,
    'max_connections' => $environment === 'production' ? 20 : 10,
    'max_idle_time' => 300, // 5 minutos
    'connection_lifetime' => 3600, // 1 hora
];

// Configuración de retry y reconexión
$config['retry'] = [
    'max_attempts' => 3,
    'base_delay' => 1, // segundo
    'max_delay' => 10, // segundos
    'exponential_backoff' => true,
];

// Configuración de monitoreo y métricas
$config['monitoring'] = [
    'slow_query_threshold' => $environment === 'production' ? 1000 : 500, // ms
    'log_slow_queries' => true,
    'track_query_stats' => $environment !== 'production',
    'max_log_entries' => 1000,
];

// Configuración de seguridad
$config['security'] = [
    'encrypt_connection' => $environment === 'production',
    'verify_server_cert' => $environment === 'production',
    'disable_local_infile' => true,
    'sql_mode' => 'TRADITIONAL,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION',
];

// Configuración de transacciones
$config['transactions'] = [
    'isolation_level' => 'READ_COMMITTED',
    'autocommit' => false,
    'deadlock_retries' => 3,
];

// Variables de entorno específicas (pueden sobrescribir configuración)
if (isset($_ENV['DB_HOST'])) $config['host'] = $_ENV['DB_HOST'];
if (isset($_ENV['DB_PORT'])) $config['port'] = (int)$_ENV['DB_PORT'];
if (isset($_ENV['DB_DATABASE'])) $config['database'] = $_ENV['DB_DATABASE'];
if (isset($_ENV['DB_USERNAME'])) $config['username'] = $_ENV['DB_USERNAME'];
if (isset($_ENV['DB_PASSWORD'])) $config['password'] = $_ENV['DB_PASSWORD'];

// Logging adicional para debug (solo desarrollo)
if ($environment === 'development' && !empty($_GET['debug_db'])) {
    $config['logging'] = true;
    $config['monitoring']['track_query_stats'] = true;
    error_log("[DATABASE] Configuración cargada para ambiente: $environment");
}

return $config;
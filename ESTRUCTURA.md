# Estructura del Proyecto ProyectDash_

Este documento describe la estructura reorganizada del proyecto ProyectDash_.

## Estructura de Directorios

```
ProyectDash_/
├── index.php               # Punto de entrada principal del SPA
├── .htaccess               # Redirección al directorio public
│
├── core/                   # Sistema central de navegación y arquitectura
│   ├── NavigationManager.php # Gestor de navegación SPA (Backend)
│   └── Router.php          # Sistema de enrutamiento avanzado
│
├── public/                 # Directorio público accesible desde la web
│   ├── index.php           # Index público (legacy)
│   ├── views/              # Vistas de la aplicación
│   │   ├── project.php     # Vista del dashboard de proyectos
│   │   ├── analytics.php   # Vista de análisis
│   │   ├── finance.php     # Vista de finanzas
│   │   ├── crypto.php      # Vista de criptomonedas
│   │   └── ...             # Otras vistas SPA
│   ├── api/                # API SPA y endpoints RESTful
│   │   ├── navigation.php  # API de navegación SPA (NUEVO)
│   │   ├── index.php       # Punto de entrada de la API
│   │   ├── config/         # Configuración de la API
│   │   └── v1/             # Versión 1 de la API REST
│   │       ├── controllers/# Controladores de la API
│   │       └── models/     # Modelos de datos
│   └── .htaccess           # URLs limpias para SPA
│
├── components/             # Componentes reutilizables (clases PHP)
│   ├── sidebar/            # Componente de barra lateral mejorado
│   │   └── FuseSidebar.class.php # Clase PHP del sidebar con soporte SPA
│   └── FuseHeader.php      # Componente de encabezado
│
├── assets/                 # Recursos de desarrollo (fuente de verdad)
│   ├── css/                # Archivos CSS de desarrollo
│   │   ├── main.css        # CSS principal
│   │   ├── navigation.css  # Estilos de navegación SPA (NUEVO)
│   │   ├── base/           # Estilos base
│   │   ├── layout/         # Estilos de layout
│   │   ├── theme/          # Estilos del tema
│   │   └── utilities/      # Utilidades CSS
│   ├── js/                 # Archivos JavaScript de desarrollo
│   │   ├── main.js         # JavaScript principal
│   │   ├── core/           # Funcionalidades principales
│   │   │   └── navigation.js # Sistema de navegación SPA (NUEVO)
│   │   └── utils/          # Utilidades JavaScript
│   ├── components/         # Recursos de componentes de desarrollo
│   │   ├── header/         # Recursos del componente de encabezado
│   │   │   ├── header.css  # Estilos del encabezado
│   │   │   └── header.js   # JavaScript del encabezado
│   │   └── sidebar/        # Recursos del componente de barra lateral
│   │       ├── sidebar.css # Estilos de la barra lateral
│   │       └── sidebar.js  # JavaScript del sidebar mejorado (NUEVO)
│   └── images/             # Imágenes de desarrollo
│
├── services/               # Servicios y lógica de negocio
│   └── api_helpers/        # Clases auxiliares para la API
│
├── sync_assets.sh          # Script para sincronizar assets de desarrollo a public
└── README_NAVIGATION.md    # Documentación del sistema SPA (NUEVO)
```

## Proceso de Desarrollo

### Para el Sistema SPA (Recomendado)

1. **Modificar archivos de navegación**:
   - Backend: `/core/NavigationManager.php` para lógica de navegación
   - Frontend: `/assets/js/core/navigation.js` para comportamiento del cliente
   - API: `/public/api/navigation.php` para endpoints AJAX

2. **Modificar componentes**:
   - Clases PHP en `/components/`
   - Recursos CSS/JS en `/assets/components/`

3. **Agregar nuevas páginas**:
   - Crear vista en `/public/views/` o directorio raíz
   - Configurar en `NavigationManager.php`
   - El sistema SPA las manejará automáticamente

4. **Probar navegación SPA**:
   - Acceder desde `/index.php` (punto de entrada principal)
   - La navegación es instantánea sin recargas de página

### Para Desarrollo Tradicional (Legacy)

1. **Modificar archivos en `/assets/`** y componentes en `/components/`
   - Siempre trabajar con los archivos en estos directorios
   - Son la "fuente de verdad" para el desarrollo

2. **Ejecutar `./sync_assets.sh`** para sincronizar con `/public/assets/`
   - Esto asegura que los cambios se reflejen en la aplicación
   - No es necesario copiar manualmente los archivos

3. **Probar los cambios** accediendo a la aplicación a través del navegador
   - Acceso directo via `/public/index.php`

## Sistema de Navegación SPA

### Arquitectura del Sistema

El proyecto ahora utiliza un **sistema SPA (Single Page Application)** completo:

#### Backend (PHP)
- **NavigationManager** (`/core/NavigationManager.php`): Gestión centralizada de navegación
  - Control de estados y sesiones
  - Renderizado de vistas dinámico
  - Sistema de caché integrado
  - Configuración de páginas centralizada

#### Frontend (JavaScript)  
- **Sistema de navegación** (`/assets/js/core/navigation.js`): Cliente SPA completo
  - Navegación AJAX sin recargas
  - Caché en cliente (5 minutos de TTL)
  - Preload automático de páginas críticas
  - Historial del navegador integrado
  - Sistema de eventos extensible

#### API de Navegación
- **Endpoint AJAX** (`/public/api/navigation.php`): API para operaciones SPA
  - Navegación entre páginas
  - Preload de contenido
  - Validación de páginas
  - Respuestas JSON optimizadas

### Flujo de Navegación SPA

1. **Click en sidebar** → Evento JavaScript capturado
2. **Petición AJAX** → API de navegación procesa solicitud  
3. **Carga de contenido** → Solo el contenido necesario, no página completa
4. **Actualización DOM** → Transición suave sin recarga
5. **Actualización URL** → Historial del navegador preservado

## Organización de Componentes

### Componentes PHP Mejorados

- **FuseSidebar** (`/components/sidebar/FuseSidebar.class.php`): 
  - Integración completa con sistema SPA
  - Estados activos dinámicos
  - Soporte para navegación por teclado

- **Recursos de componentes** en `/assets/components/`:
  - `/sidebar/sidebar.js`: JavaScript mejorado con soporte SPA
  - `/header/header.js`: Funcionalidad del encabezado

### Estilos SPA

- **navigation.css** (`/assets/css/navigation.css`):
  - Transiciones suaves entre páginas
  - Estados de carga visuales
  - Indicadores de navegación
  - Diseño responsive

## API y Endpoints

### API SPA (Nuevo)
- **navigation.php**: Endpoint principal para navegación AJAX
- **Operaciones soportadas**:
  - `navigate`: Cambiar de página
  - `preload`: Precargar páginas para mejor rendimiento
  - `state`: Obtener estado actual de navegación
  - `validate`: Validar si una página existe

### API RESTful (Existente)
- **Endpoints públicos** en `/public/api/v1/`
- **Clases auxiliares** en `/public/api/helpers/`

## Cómo Agregar Nuevas Páginas SPA

1. **Crear la vista**:
   ```php
   // En /public/views/nueva-pagina.php o /nueva-pagina.php
   <div class="nueva-pagina">
       <h1>Nueva Página</h1>
       <!-- Contenido -->
   </div>
   ```

2. **Configurar en NavigationManager**:
   ```php
   // En /core/NavigationManager.php, array $pages
   'nueva-pagina' => [
       'title' => 'Nueva Página',
       'view' => 'nueva-pagina.php',
       'scripts' => ['nueva-pagina.js'], // Opcional
       'styles' => ['nueva-pagina.css'], // Opcional
       'preload' => false,
       'cache' => true
   ]
   ```

3. **Agregar al menú**:
   ```php
   // En NavigationManager::getMenuItems()
   ['id' => 'nueva-pagina', 'label' => 'Nueva Página', 'icon' => 'icono']
   ```

4. **La navegación funcionará automáticamente** - no requiere configuración adicional

## Notas Importantes

### Sistema SPA
- **Punto de entrada principal**: `/index.php` (no `/public/index.php`)
- **URLs limpias**: `/proyecto`, `/analytics`, etc.
- **Sin recargas de página**: Navegación instantánea
- **Historial funcional**: Botones atrás/adelante del navegador
- **Cache inteligente**: Mejor rendimiento con caché automático

### Desarrollo Legacy
- **Nunca modificar directamente** `/public/assets/` 
- **Usar script de sincronización** `./sync_assets.sh`
- **Punto de entrada legacy**: `/public/index.php`

### Documentación
- **README_NAVIGATION.md**: Guía completa del sistema SPA
- **ESTRUCTURA.md**: Este archivo con la estructura actualizada
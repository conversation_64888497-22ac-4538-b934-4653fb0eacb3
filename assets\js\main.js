/**
 * Archivo principal de JavaScript
 * 
 * Inicializa todos los componentes y funcionalidades del sistema
 */

// Cargar módulos
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar sistema principal
    initApp();
});

/**
 * Inicializa la aplicación
 */
function initApp() {
    console.log('Inicializando aplicación...');
    
    // Obtener configuración inicial
    const config = window.appConfig || {
        initialPage: 'project',
        baseUrl: '/',
        debug: false
    };
    
    if (config.debug) {
        console.log('Configuración:', config);
    }
    
    // Cargar componentes
    loadComponents(config);
    
    // Inicializar eventos globales
    setupGlobalEvents();
    
    console.log('Aplicación inicializada correctamente');
}

/**
 * Carga los componentes de la aplicación
 * 
 * @param {Object} config Configuración de la aplicación
 */
function loadComponents(config) {
    // Cargar estilos del header
    loadStylesheet('assets/components/header/header-enhanced.css');
    
    // Cargar componentes JS
    loadScript('assets/components/header/header.js', function() {
        if (typeof initHeader === 'function') {
            console.log('Initializing legacy header...');
            // Assuming initHeader from header.js is for basic setup
        } 
    });

    loadScript('assets/components/header/header-enhanced.js', function() {
        // Inicializar header cuando el script esté cargado
        if (typeof initHeader === 'function') {
            console.log('Initializing enhanced header...');
            initHeader();
        } else {
            console.warn('initHeader function not found in header-enhanced.js');
        }
    });
    
    loadScript('assets/components/sidebar/sidebar.js', function() {
        // Inicializar sidebar cuando el script esté cargado
        if (typeof initFuseSidebar === 'function') {
            // Destruir instancia anterior si existe
            if (window.fuseSidebarInstance) {
                window.fuseSidebarInstance.destroy();
            }
            
            window.fuseSidebarInstance = initFuseSidebar({
                container: '#fuse-sidebar', // Pass the container element selector
                activeItem: config.initialPage || getCurrentPage(),
                navigationMode: 'spa', // 'spa' mode will trigger the onItemClick callback
                baseUrl: config.baseUrl || window.location.pathname.replace(/\/$/, '') + '/',
                onItemClick: function(itemId) {
                    // Use the global navigation handler
                    handleNavigation(itemId);
                },
                debug: config.debug || false
            });
        }
    });
    
    loadScript('assets/js/core/navigation.js', function() {
        // Inicializar navegación cuando el script esté cargado
        if (typeof initNavigation === 'function') {
            initNavigation({
                initialPage: config.initialPage || getCurrentPage(),
                baseUrl: config.baseUrl || window.location.pathname
            });
        }
    });
    
    // Cargar utilidades
    loadScript('assets/js/utils/ajax.js');
    
    // Inicializar otros componentes específicos de la página actual
    setTimeout(initPageComponents, 500);
}

/**
 * Configura eventos globales
 */
function setupGlobalEvents() {
    // Manejar clics en enlaces internos para navegación SPA
    document.addEventListener('click', function(event) {
        const link = event.target.closest('a[data-spa-link]');
        if (link) {
            event.preventDefault();
            const page = link.dataset.page || link.getAttribute('href').replace(/^\/|\/$/g, '');
            handleNavigation(page);
        }
    });
    
    // Manejar evento de navegación personalizado del sidebar
    window.addEventListener('fuse-sidebar-navigation', function(event) {
        const itemId = event.detail.itemId;
        console.log('Custom navigation event received for:', itemId);
        handleNavigation(itemId);
    });
    
    // Manejar envío de formularios AJAX
    document.addEventListener('submit', function(event) {
        const form = event.target.closest('form[data-ajax-form]');
        if (form) {
            event.preventDefault();
            handleAjaxForm(form);
        }
    });
    
    // Detectar cambios de tema
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (e.matches) {
            document.documentElement.classList.add('dark-theme');
            localStorage.setItem('darkMode', 'true');
        } else {
            document.documentElement.classList.remove('dark-theme');
            localStorage.setItem('darkMode', 'false');
        }
    });
    
    // Aplicar tema guardado o predeterminado
    if (localStorage.getItem('darkMode') === 'true') {
        document.documentElement.classList.add('dark-theme');
    }
}

/**
 * Obtiene la página actual desde la URL
 * 
 * @returns {string} Nombre de la página actual
 */
function getCurrentPage() {
    const path = window.location.pathname;
    const page = path.split('/').filter(Boolean).pop() || 'project';
    return page;
}

/**
 * Inicializa componentes específicos de la página actual
 */
function initPageComponents() {
    // Inicializar gráficos si la página los contiene
    initCharts();
    
    // Inicializar tablas de datos si la página las contiene
    initDataTables();
    
    // Inicializar calendario si la página lo contiene
    initCalendar();
}

/**
 * Destruye todos los gráficos existentes
 */
function destroyAllCharts() {
    if (typeof Chart === 'undefined') return;

    // Destruir todas las instancias de Chart.js
    Object.keys(Chart.instances).forEach(key => {
        const chart = Chart.instances[key];
        if (chart) {
            chart.destroy();
        }
    });

    // También buscar por canvas y destruir manualmente
    const canvases = document.querySelectorAll('canvas');
    canvases.forEach(canvas => {
        const chart = Chart.getChart(canvas);
        if (chart) {
            chart.destroy();
        }
    });

    console.log('✅ Todos los gráficos destruidos');
}

/**
 * Inicializa gráficos de Chart.js si existen en la página
 */
function initCharts() {
    // Primero destruir gráficos existentes
    destroyAllCharts();

    const chartContainers = document.querySelectorAll('.chart-container[data-chart]');
    if (chartContainers.length === 0) {
        // También buscar canvas directamente
        const canvases = document.querySelectorAll('canvas[id*="Chart"]');
        if (canvases.length > 0) {
            initChartsFromCanvas(canvases);
        }
        return;
    }

    if (typeof Chart === 'undefined') {
        console.warn('Chart.js no está cargado');
        return;
    }

    chartContainers.forEach(container => {
        try {
            const chartType = container.dataset.chartType || 'line';
            const chartData = JSON.parse(container.dataset.chartData || '{}');
            const chartOptions = JSON.parse(container.dataset.chartOptions || '{}');

            const canvas = container.querySelector('canvas');
            if (!canvas) return;

            // Asegurar que el canvas esté limpio
            const existingChart = Chart.getChart(canvas);
            if (existingChart) {
                existingChart.destroy();
            }

            // Crear nuevo gráfico
            const newChart = new Chart(canvas, {
                type: chartType,
                data: chartData,
                options: chartOptions
            });

            console.log(`✅ Gráfico ${canvas.id || 'sin-id'} inicializado`);
        } catch (error) {
            console.error('Error al inicializar gráfico:', error);
        }
    });
}

/**
 * Inicializa gráficos desde canvas directamente (fallback)
 */
function initChartsFromCanvas(canvases) {
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js no está cargado');
        return;
    }

    canvases.forEach(canvas => {
        try {
            const chartId = canvas.id;

            // Destruir gráfico existente
            const existingChart = Chart.getChart(canvas);
            if (existingChart) {
                existingChart.destroy();
            }

            // Inicializar según el ID del canvas
            if (chartId === 'salesChart') {
                initSalesChart(canvas);
            } else if (chartId === 'visitsChart') {
                initVisitsChart(canvas);
            } else if (chartId === 'conversionChart') {
                initConversionChart(canvas);
            } else if (chartId === 'revenueChart') {
                initRevenueChart(canvas);
            }

            console.log(`✅ Gráfico ${chartId} inicializado desde canvas`);
        } catch (error) {
            console.error(`Error al inicializar gráfico ${canvas.id}:`, error);
        }
    });
}

/**
 * Inicializa el gráfico de ventas
 */
function initSalesChart(canvas) {
    const ctx = canvas.getContext('2d');
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
            datasets: [{
                label: 'Ventas',
                data: [12000, 19000, 15000, 25000, 22000, 30000, 28000, 33000, 29000, 35000, 38000, 42000],
                borderColor: '#0ea5e9',
                backgroundColor: 'rgba(14, 165, 233, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de visitas
 */
function initVisitsChart(canvas) {
    const ctx = canvas.getContext('2d');
    return new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
            datasets: [{
                label: 'Visitas',
                data: [1200, 1900, 1500, 2200, 2800, 1800, 1400],
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: '#22c55e',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de conversión
 */
function initConversionChart(canvas) {
    const ctx = canvas.getContext('2d');
    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Convertidos', 'No convertidos'],
            datasets: [{
                data: [35, 65],
                backgroundColor: ['#f59e0b', '#e5e7eb'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de ingresos
 */
function initRevenueChart(canvas) {
    const ctx = canvas.getContext('2d');
    return new Chart(ctx, {
        type: 'area',
        data: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            datasets: [{
                label: 'Ingresos',
                data: [85000, 92000, 88000, 95000],
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

/**
 * Inicializa tablas de datos si existen en la página
 */
function initDataTables() {
    const tables = document.querySelectorAll('.data-table[data-sortable]');
    if (tables.length === 0) return;
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        
        headers.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const direction = this.dataset.sortDirection === 'asc' ? 'desc' : 'asc';
                
                // Reiniciar todos los headers
                headers.forEach(h => {
                    h.dataset.sortDirection = '';
                    h.classList.remove('sorting-asc', 'sorting-desc');
                });
                
                // Establecer dirección en el header actual
                this.dataset.sortDirection = direction;
                this.classList.add(direction === 'asc' ? 'sorting-asc' : 'sorting-desc');
                
                // Ordenar la tabla
                sortTable(table, column, direction);
            });
        });
    });
}

/**
 * Ordena una tabla por una columna
 * 
 * @param {HTMLTableElement} table Tabla a ordenar
 * @param {string} column Nombre de la columna a ordenar
 * @param {string} direction Dirección ('asc' o 'desc')
 */
function sortTable(table, column, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const sortedRows = rows.sort((a, b) => {
        const aValue = a.querySelector(`td[data-column="${column}"]`).textContent.trim();
        const bValue = b.querySelector(`td[data-column="${column}"]`).textContent.trim();
        
        // Determinar tipo de datos
        if (!isNaN(parseFloat(aValue)) && !isNaN(parseFloat(bValue))) {
            // Numérico
            return direction === 'asc' 
                ? parseFloat(aValue) - parseFloat(bValue)
                : parseFloat(bValue) - parseFloat(aValue);
        } else {
            // Texto
            return direction === 'asc'
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        }
    });
    
    // Limpiar tabla
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }
    
    // Añadir filas ordenadas
    sortedRows.forEach(row => {
        tbody.appendChild(row);
    });
}

/**
 * Inicializa el calendario si existe en la página
 */
function initCalendar() {
    const calendarContainer = document.querySelector('.calendar-container');
    if (!calendarContainer) return;
    
    if (typeof luxon === 'undefined') {
        console.warn('Luxon no está cargado');
        return;
    }
    
    // Implementar calendario
    console.log('Inicializando calendario...');
}

/**
 * Maneja el envío de formularios AJAX
 * 
 * @param {HTMLFormElement} form Formulario a enviar
 */
function handleAjaxForm(form) {
    const url = form.action;
    const method = form.method.toUpperCase();
    const formData = new FormData(form);
    
    // Mostrar indicador de carga
    const submitButton = form.querySelector('[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<div class="loader"></div> Procesando...';
    
    // Realizar petición
    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Manejar respuesta exitosa
        if (data.redirect) {
            // Redireccionar
            window.location.href = data.redirect;
        } else if (data.message) {
            // Mostrar mensaje
            showNotification(data.message, 'success');
        }
        
        // Si hay un callback especificado, ejecutarlo
        if (form.dataset.callback && window[form.dataset.callback]) {
            window[form.dataset.callback](data);
        }
        
        // Resetear formulario si se especificó
        if (form.dataset.reset === 'true') {
            form.reset();
        }
    })
    .catch(error => {
        // Manejar error
        console.error('Error en envío de formulario:', error);
        showNotification(error.message || 'Error al procesar el formulario', 'error');
    })
    .finally(() => {
        // Restaurar botón
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    });
}

/**
 * Muestra una notificación
 * 
 * @param {string} message Mensaje a mostrar
 * @param {string} type Tipo de notificación ('success', 'error', 'warning', 'info')
 * @param {number} duration Duración en ms (por defecto 5000)
 */
function showNotification(message, type = 'info', duration = 5000) {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon"></div>
            <div class="notification-message">${message}</div>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Añadir al contenedor de notificaciones (crearlo si no existe)
    let notificationsContainer = document.querySelector('.notifications-container');
    if (!notificationsContainer) {
        notificationsContainer = document.createElement('div');
        notificationsContainer.className = 'notifications-container';
        document.body.appendChild(notificationsContainer);
    }
    
    notificationsContainer.appendChild(notification);
    
    // Mostrar con animación
    setTimeout(() => {
        notification.classList.add('notification-show');
    }, 10);
    
    // Configurar cierre
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        closeNotification(notification);
    });
    
    // Auto cerrar después de duration
    setTimeout(() => {
        closeNotification(notification);
    }, duration);
}

/**
 * Cierra una notificación con animación
 * 
 * @param {HTMLElement} notification Elemento de notificación
 */
function closeNotification(notification) {
    notification.classList.remove('notification-show');
    notification.classList.add('notification-hide');
    
    // Eliminar después de la animación
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Carga un script JavaScript de forma dinámica
 * 
 * @param {string} url URL del script a cargar
 * @param {Function} callback Función a ejecutar cuando el script esté cargado
 */
function loadScript(url, callback) {
    // Verificar si el script ya está cargado
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
        if (callback) callback();
        return;
    }
    
    // Crear elemento script
    const script = document.createElement('script');
    script.src = url;
    script.async = true;
    
    // Configurar callback
    if (callback) {
        script.onload = callback;
        script.onerror = () => {
            console.error(`Error loading script: ${url}`);
        };
    }
    
    // Añadir al documento
    document.head.appendChild(script);
}

/**
 * Carga una hoja de estilos CSS de forma dinámica
 * 
 * @param {string} url URL de la hoja de estilos a cargar
 * @param {Function} callback Función a ejecutar cuando la hoja esté cargada
 */
function loadStylesheet(url, callback) {
    // Verificar si la hoja de estilos ya está cargada
    const existingLink = document.querySelector(`link[href="${url}"]`);
    if (existingLink) {
        if (callback) callback();
        return;
    }
    
    // Crear elemento link
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    
    // Configurar callback
    if (callback) {
        link.onload = callback;
        link.onerror = () => {
            console.error(`Error loading stylesheet: ${url}`);
        };
    }
    
    // Añadir al documento
    document.head.appendChild(link);
}

// Definición temporal de handleNavigation para evitar errores hasta que se cargue navigation.js
if (typeof handleNavigation === 'undefined') {
    window.handleNavigation = function(page) {
        console.log('Navigation handler will be loaded soon... Requested page:', page);
        // Temporal fallback while navigation.js loads
        setTimeout(function() {
            if (typeof handleNavigation === 'function') {
                handleNavigation(page);
            }
        }, 100);
    };
}
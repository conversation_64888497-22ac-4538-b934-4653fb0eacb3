/**
 * Estilos para el componente FuseHeader
 */

/* Variables */
:root {
    --header-height: 4rem;
    --header-bg: #ffffff;
    --header-border: #e2e8f0;
    --icon-hover: #f1f5f9;
    --dropdown-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --badge-bg: #ef4444;
    --badge-color: #ffffff;
}

/* Header principal */
.fuse-header {
    height: var(--header-height);
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--header-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    position: relative;
    z-index: 40;
}

/* Área del logo */
.header-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    width: 2rem;
    height: 2rem;
    background-color: #0ea5e9;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

/* Botones de navegación */
.header-nav-buttons {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.nav-button {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.nav-button:hover {
    background-color: var(--icon-hover);
}

.nav-button svg {
    width: 1rem;
    height: 1rem;
}

/* Menú de idiomas */
.language-dropdown {
    position: relative;
}

.language-dropdown-content {
    position: absolute;
    right: 0;
    top: 100%;
    margin-top: 0.25rem;
    background-color: white;
    border: 1px solid var(--header-border);
    border-radius: 0.375rem;
    box-shadow: var(--dropdown-shadow);
    min-width: 8rem;
    z-index: 50;
}

.language-item {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.language-item:hover {
    background-color: var(--icon-hover);
}

/* Distintivo de notificaciones */
.notification-badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    height: 1rem;
    width: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.625rem;
    min-width: 1rem;
    background-color: var(--badge-bg);
    color: var(--badge-color);
    border-radius: 9999px;
}

/* Avatar de usuario */
.user-avatar {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(to bottom right, #0ea5e9, #6366f1);
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.user-avatar:hover {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #0ea5e9;
}

/* Botón de Vitejs */
.vitejs-button {
    height: 2rem;
    padding: 0 0.75rem;
    font-size: 0.75rem;
    border: 1px solid var(--header-border);
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.vitejs-button:hover {
    background-color: var(--icon-hover);
}

/* Paneles laterales */
#notificationsCanvas,
#canvasLateral {
    position: fixed;
    top: var(--header-height);
    right: 0;
    width: 320px;
    height: calc(100vh - var(--header-height));
    background-color: var(--header-bg);
    border-left: 1px solid var(--header-border);
    box-shadow: -4px 0 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 30;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    padding: 1rem;
    display: none;
}

#notificationsCanvas.active,
#canvasLateral.active {
    transform: translateX(0);
}

/* Botones activos */
.nav-button.active {
    background-color: var(--icon-hover);
    color: #0ea5e9;
}

.nav-button.active svg {
    stroke: #0ea5e9;
}

/* Overlay para cerrar paneles en mobile */
.panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 20;
    display: none;
}

.panel-overlay.active {
    display: block;
}

/* Estilos específicos para paneles */
.panel-header {
    border-bottom: 1px solid var(--header-border);
    padding-bottom: 0.75rem;
    margin-bottom: 1rem;
}

.panel-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

/* Diseño responsivo */
@media (max-width: 768px) {
    .fuse-header {
        padding: 0 0.75rem;
    }
    
    .logo-text {
        display: none;
    }
    
    .vitejs-button {
        display: none;
    }
    
    #notificationsCanvas,
    #canvasLateral {
        width: 100%;
        transform: translateX(100%);
    }
}

/* Soporte para tema oscuro */
.dark-theme {
    --header-bg: #1e293b;
    --header-border: #334155;
    --icon-hover: #334155;
}

.dark-theme .fuse-header {
    background-color: var(--header-bg);
    border-color: var(--header-border);
}

.dark-theme .logo-text {
    color: #f1f5f9;
}

.dark-theme .nav-button,
.dark-theme .language-dropdown button {
    color: #f1f5f9;
}

.dark-theme .language-dropdown-content {
    background-color: #1e293b;
    border-color: #334155;
}

.dark-theme .language-item {
    color: #f1f5f9;
}

.dark-theme .language-item:hover {
    background-color: #334155;
}

.dark-theme #notificationsCanvas,
.dark-theme #canvasLateral {
    background-color: var(--header-bg);
    border-color: var(--header-border);
}

.dark-theme .panel-title {
    color: #f1f5f9;
}

.dark-theme .nav-button.active {
    background-color: var(--icon-hover);
    color: #38bdf8;
}

.dark-theme .nav-button.active svg {
    stroke: #38bdf8;
}
# ProyectDash Migration Guide

## Overview

This guide provides step-by-step instructions for migrating from the current PHP-based navigation system to the modern SPA architecture.

## Migration Phases

### Phase 1: Setup New Infrastructure (Week 1-2)

#### 1.1 Backend Setup

1. **Install Dependencies**
```bash
composer require vlucas/phpdotenv
composer require firebase/php-jwt
composer require respect/validation
```

2. **Create Directory Structure**
```
/app
  /Controllers
  /Models
  /Services
  /Middleware
/core
  Router.php
  Application.php
/config
  app.php
  database.php
  routes.php
```

3. **Environment Configuration**
Create `.env` file:
```env
APP_NAME=ProyectDash
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=proyectdash
DB_USERNAME=root
DB_PASSWORD=

JWT_SECRET=your-secret-key
JWT_EXPIRATION=3600
```

4. **Update index.php**
```php
<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/core/Application.php';

$app = new Core\Application();
$app->run();
```

#### 1.2 Frontend Setup

1. **Install Build Tools**
```bash
npm init -y
npm install webpack webpack-cli babel-loader @babel/core @babel/preset-env
npm install --save-dev webpack-dev-server
```

2. **Create Webpack Configuration**
```javascript
// webpack.config.js
module.exports = {
  entry: './assets/js/app.js',
  output: {
    path: __dirname + '/dist',
    filename: 'bundle.js'
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      }
    ]
  }
};
```

### Phase 2: Implement Core Systems (Week 3-4)

#### 2.1 Implement Router

1. **Backend Routes Configuration**
```php
// config/routes.php
use Core\Router;

return function(Router $router) {
    // API Routes
    $router->group(['prefix' => 'api/v1'], function($router) {
        // Authentication
        $router->post('/auth/login', 'AuthController@login');
        $router->post('/auth/logout', 'AuthController@logout');
        $router->post('/auth/refresh', 'AuthController@refresh');
        
        // Protected routes
        $router->group(['middleware' => 'auth'], function($router) {
            // Dashboard
            $router->get('/dashboard/stats', 'DashboardController@stats');
            
            // Projects
            $router->get('/projects', 'ProjectController@index');
            $router->get('/projects/{id}', 'ProjectController@show');
            $router->post('/projects', 'ProjectController@store');
            $router->put('/projects/{id}', 'ProjectController@update');
            $router->delete('/projects/{id}', 'ProjectController@destroy');
        });
    });
    
    // Catch-all route for SPA
    $router->any('/{any}', function() {
        include 'index.html';
    })->where('any', '.*');
};
```

2. **Frontend Routes Configuration**
```javascript
// assets/js/routes/index.js
export default [
    {
        path: '/',
        redirect: '/dashboard'
    },
    {
        path: '/dashboard',
        component: () => import('../pages/Dashboard.js'),
        meta: { title: 'Dashboard', requiresAuth: true }
    },
    {
        path: '/projects',
        component: () => import('../pages/Projects.js'),
        meta: { title: 'Projects', requiresAuth: true }
    },
    {
        path: '/login',
        component: () => import('../pages/Login.js'),
        meta: { title: 'Login', requiresAuth: false }
    }
];
```

#### 2.2 Implement Store Modules

1. **User Module**
```javascript
// assets/js/store/modules/user.js
export default {
    state: {
        profile: null,
        token: null,
        isAuthenticated: false
    },
    
    mutations: {
        SET_USER(state, user) {
            state.profile = user;
            state.isAuthenticated = !!user;
        },
        SET_TOKEN(state, token) {
            state.token = token;
        }
    },
    
    actions: {
        async login({ commit }, credentials) {
            const response = await $api.post('/auth/login', credentials);
            commit('SET_TOKEN', response.data.token);
            commit('SET_USER', response.data.user);
            $api.setToken(response.data.token);
            return response;
        },
        
        async logout({ commit }) {
            await $api.post('/auth/logout');
            commit('SET_TOKEN', null);
            commit('SET_USER', null);
            $api.clearAuth();
        }
    }
};
```

### Phase 3: Component Migration (Week 5-6)

#### 3.1 Convert PHP Components to JavaScript

**Before (PHP):**
```php
// components/sidebar/FuseSidebar.class.php
class FuseSidebar {
    public function render() {
        return '<div class="sidebar">...</div>';
    }
}
```

**After (JavaScript):**
```javascript
// assets/js/components/Sidebar.js
export default class Sidebar {
    constructor({ container, store, router }) {
        this.container = container;
        this.store = store;
        this.router = router;
    }
    
    async render() {
        const navigation = this.store.state.navigation.items;
        const activeItem = this.store.state.navigation.activeItem;
        
        this.container.innerHTML = `
            <nav class="sidebar">
                ${this.renderNavItems(navigation, activeItem)}
            </nav>
        `;
        
        this.attachEventListeners();
    }
    
    renderNavItems(items, activeItem) {
        return items.map(item => `
            <a href="${item.path}" 
               class="nav-item ${item.id === activeItem ? 'active' : ''}"
               data-item-id="${item.id}">
                <i class="${item.icon}"></i>
                <span>${item.label}</span>
            </a>
        `).join('');
    }
    
    attachEventListeners() {
        this.container.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const itemId = e.currentTarget.dataset.itemId;
                this.router.push(e.currentTarget.getAttribute('href'));
            });
        });
    }
}
```

#### 3.2 Page Component Migration

**Before (PHP):**
```php
// project.php
<div class="project-dashboard">
    <h1>Project Dashboard</h1>
    <?php include 'components/project-stats.php'; ?>
</div>
```

**After (JavaScript):**
```javascript
// assets/js/pages/ProjectDashboard.js
export default {
    async render(route) {
        const stats = await $api.get('/dashboard/project-stats');
        
        return `
            <div class="project-dashboard">
                <h1>Project Dashboard</h1>
                ${this.renderStats(stats.data)}
            </div>
        `;
    },
    
    renderStats(stats) {
        return `
            <div class="stats-grid">
                ${stats.map(stat => `
                    <div class="stat-card">
                        <h3>${stat.label}</h3>
                        <p class="value">${stat.value}</p>
                    </div>
                `).join('')}
            </div>
        `;
    },
    
    mounted(container, route) {
        // Initialize any JavaScript functionality
        this.initCharts(container);
    }
};
```

### Phase 4: Data Migration (Week 7-8)

#### 4.1 API Endpoints Migration

1. **Create RESTful Controllers**
```php
// app/Controllers/ProjectController.php
namespace App\Controllers;

class ProjectController extends BaseController {
    public function index() {
        $this->authorize('view-projects');
        
        $query = "SELECT * FROM projects WHERE user_id = ?";
        $query = $this->applyFilters($query, [
            'status' => 'exact',
            'name' => 'like'
        ]);
        $query = $this->applySort($query, ['name', 'created_at']);
        
        $paginated = $this->paginate($query);
        
        // Execute query and get results
        $projects = []; // TODO: Execute query
        
        return $this->success([
            'projects' => $projects,
            'pagination' => $paginated['pagination']
        ]);
    }
}
```

2. **Update Frontend API Calls**
```javascript
// Before
$.ajax({
    url: 'index.php?page=project',
    success: function(html) {
        $('#main-content').html(html);
    }
});

// After
const response = await $api.get('/projects');
const projects = response.data.projects;
```

### Phase 5: Testing & Optimization (Week 9-10)

#### 5.1 Testing Strategy

1. **Backend Tests**
```php
// tests/Feature/ProjectTest.php
class ProjectTest extends TestCase {
    public function test_can_list_projects() {
        $response = $this->actingAs($this->user)
                         ->get('/api/v1/projects');
        
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'projects',
                         'pagination'
                     ]
                 ]);
    }
}
```

2. **Frontend Tests**
```javascript
// tests/unit/store/user.test.js
describe('User Store Module', () => {
    test('login action sets user and token', async () => {
        const commit = jest.fn();
        const credentials = { email: '<EMAIL>', password: 'password' };
        
        await actions.login({ commit }, credentials);
        
        expect(commit).toHaveBeenCalledWith('SET_TOKEN', expect.any(String));
        expect(commit).toHaveBeenCalledWith('SET_USER', expect.any(Object));
    });
});
```

#### 5.2 Performance Optimization

1. **Enable Caching**
```javascript
// Cache API responses
$api.cacheEnabled = true;
$api.cacheTTL = 5 * 60 * 1000; // 5 minutes

// Cache route components
const routes = [
    {
        path: '/dashboard',
        component: () => import(/* webpackChunkName: "dashboard" */ '../pages/Dashboard.js')
    }
];
```

2. **Implement Code Splitting**
```javascript
// Lazy load heavy components
const Chart = () => import(/* webpackChunkName: "charts" */ './components/Chart.js');
```

### Phase 6: Deployment (Week 11-12)

#### 6.1 Build Process

1. **Create Build Script**
```json
// package.json
{
  "scripts": {
    "build": "webpack --mode production",
    "dev": "webpack-dev-server --mode development",
    "watch": "webpack --mode development --watch"
  }
}
```

2. **Production Configuration**
```javascript
// webpack.prod.js
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  mode: 'production',
  optimization: {
    minimizer: [new TerserPlugin()],
    splitChunks: {
      chunks: 'all'
    }
  }
};
```

#### 6.2 Deployment Steps

1. **Pre-deployment Checklist**
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] Assets built and minified
- [ ] HTTPS configured
- [ ] CORS headers set correctly

2. **Deployment Commands**
```bash
# Build assets
npm run build

# Clear caches
php artisan cache:clear

# Run migrations
php artisan migrate

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

## Migration Utilities

### Data Migration Script
```php
// scripts/migrate-data.php
<?php
// Migrate existing session data to new format
$oldSessions = $_SESSION;
$newFormat = [
    'user' => [
        'profile' => $oldSessions['user_data'] ?? null,
        'token' => $oldSessions['auth_token'] ?? null
    ],
    'ui' => [
        'theme' => $oldSessions['theme'] ?? 'light',
        'sidebarOpen' => true
    ]
];

// Save to new storage
file_put_contents('storage/app-state.json', json_encode($newFormat));
```

### URL Redirect Rules
```apache
# .htaccess
RewriteEngine On

# Redirect old URLs to new SPA routes
RewriteRule ^index\.php\?page=(.*)$ /$1 [R=301,L]

# Handle SPA routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(?!api/).*$ index.html [L]
```

## Rollback Plan

In case of issues, maintain the ability to rollback:

1. **Feature Flags**
```javascript
// config/features.js
export default {
    useSPARouter: true,
    useNewAPI: true,
    useStateManagement: true
};
```

2. **Gradual Migration**
```javascript
// Conditionally load old or new system
if (features.useSPARouter) {
    import('./app.js');
} else {
    window.location.href = '/index.php';
}
```

## Post-Migration

### Monitoring
- Set up error tracking (Sentry, Rollbar)
- Monitor API performance
- Track user engagement metrics
- Monitor JavaScript errors

### Optimization
- Enable HTTP/2
- Implement service workers for offline support
- Set up CDN for static assets
- Enable gzip compression

### Documentation
- Update API documentation
- Create component library
- Document state management patterns
- Update deployment procedures
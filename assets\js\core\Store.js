/**
 * Store - Centralized State Management System
 * 
 * Features:
 * - Reactive state updates
 * - Actions and mutations
 * - Modules for organization
 * - Persistence support
 * - Time-travel debugging
 * - Middleware support
 */

class Store {
    constructor(options = {}) {
        // Core properties
        this.state = options.state || {};
        this.mutations = options.mutations || {};
        this.actions = options.actions || {};
        this.getters = options.getters || {};
        this.modules = options.modules || {};
        
        // Store configuration
        this.strict = options.strict || false;
        this.plugins = options.plugins || [];
        this.subscribers = new Map();
        this.actionSubscribers = new Map();
        this.history = [];
        this.historyLimit = options.historyLimit || 50;
        
        // Create reactive state
        this.state = this.createReactiveState(this.state);
        
        // Process modules
        this.processModules();
        
        // Apply plugins
        this.applyPlugins();
        
        // Initialize persistence if configured
        if (options.persist) {
            this.initPersistence(options.persist);
        }
    }

    /**
     * Create reactive state object
     */
    createReactiveState(state, path = []) {
        const store = this;
        
        return new Proxy(state, {
            get(target, property) {
                if (typeof target[property] === 'object' && target[property] !== null) {
                    return store.createReactiveState(target[property], [...path, property]);
                }
                return target[property];
            },
            set(target, property, value) {
                if (store.strict) {
                    console.error('Do not mutate store state outside mutations!');
                    return false;
                }
                
                const oldValue = target[property];
                target[property] = value;
                
                // Notify subscribers
                store.notifySubscribers([...path, property], value, oldValue);
                
                return true;
            }
        });
    }

    /**
     * Process modules
     */
    processModules() {
        Object.entries(this.modules).forEach(([name, module]) => {
            // Merge module state
            if (module.state) {
                this.state[name] = this.createReactiveState(module.state, [name]);
            }
            
            // Register module mutations
            if (module.mutations) {
                Object.entries(module.mutations).forEach(([mutationName, mutation]) => {
                    this.mutations[`${name}/${mutationName}`] = (state, payload) => {
                        mutation(state[name], payload);
                    };
                });
            }
            
            // Register module actions
            if (module.actions) {
                Object.entries(module.actions).forEach(([actionName, action]) => {
                    this.actions[`${name}/${actionName}`] = (context, payload) => {
                        const moduleContext = {
                            ...context,
                            state: context.state[name],
                            rootState: context.state
                        };
                        return action(moduleContext, payload);
                    };
                });
            }
            
            // Register module getters
            if (module.getters) {
                Object.entries(module.getters).forEach(([getterName, getter]) => {
                    this.getters[`${name}/${getterName}`] = (state) => {
                        return getter(state[name], this.getters, state);
                    };
                });
            }
        });
    }

    /**
     * Commit a mutation
     */
    commit(type, payload, options = {}) {
        const mutation = this.mutations[type];
        
        if (!mutation) {
            console.error(`[Store] Unknown mutation type: ${type}`);
            return;
        }
        
        // Save state snapshot for history
        if (!options.silent) {
            this.saveHistory();
        }
        
        // Execute mutation
        mutation(this.state, payload);
        
        // Notify mutation subscribers
        this.notifyMutationSubscribers({
            type,
            payload,
            state: this.state
        });
    }

    /**
     * Dispatch an action
     */
    async dispatch(type, payload) {
        const action = this.actions[type];
        
        if (!action) {
            console.error(`[Store] Unknown action type: ${type}`);
            return;
        }
        
        // Create action context
        const context = {
            state: this.state,
            getters: this.getters,
            commit: this.commit.bind(this),
            dispatch: this.dispatch.bind(this)
        };
        
        // Notify action subscribers (before)
        this.notifyActionSubscribers({
            type,
            payload,
            state: this.state
        }, 'before');
        
        try {
            // Execute action
            const result = await action(context, payload);
            
            // Notify action subscribers (after)
            this.notifyActionSubscribers({
                type,
                payload,
                state: this.state,
                result
            }, 'after');
            
            return result;
        } catch (error) {
            // Notify action subscribers (error)
            this.notifyActionSubscribers({
                type,
                payload,
                state: this.state,
                error
            }, 'error');
            
            throw error;
        }
    }

    /**
     * Get a getter value
     */
    get(name) {
        const getter = this.getters[name];
        
        if (!getter) {
            console.error(`[Store] Unknown getter: ${name}`);
            return undefined;
        }
        
        return getter(this.state);
    }

    /**
     * Subscribe to state changes
     */
    subscribe(path, callback) {
        const pathStr = Array.isArray(path) ? path.join('.') : path;
        
        if (!this.subscribers.has(pathStr)) {
            this.subscribers.set(pathStr, new Set());
        }
        
        this.subscribers.get(pathStr).add(callback);
        
        // Return unsubscribe function
        return () => {
            const callbacks = this.subscribers.get(pathStr);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.subscribers.delete(pathStr);
                }
            }
        };
    }

    /**
     * Subscribe to mutations
     */
    subscribeMutation(callback) {
        const id = Symbol();
        this.actionSubscribers.set(id, { type: 'mutation', callback });
        
        return () => {
            this.actionSubscribers.delete(id);
        };
    }

    /**
     * Subscribe to actions
     */
    subscribeAction(callback) {
        const id = Symbol();
        this.actionSubscribers.set(id, { type: 'action', callback });
        
        return () => {
            this.actionSubscribers.delete(id);
        };
    }

    /**
     * Notify subscribers of state changes
     */
    notifySubscribers(path, newValue, oldValue) {
        const pathStr = path.join('.');
        
        // Notify exact path subscribers
        const exactCallbacks = this.subscribers.get(pathStr);
        if (exactCallbacks) {
            exactCallbacks.forEach(callback => {
                callback(newValue, oldValue, path);
            });
        }
        
        // Notify parent path subscribers
        for (let i = path.length - 1; i >= 0; i--) {
            const parentPath = path.slice(0, i).join('.');
            const parentCallbacks = this.subscribers.get(parentPath);
            if (parentCallbacks) {
                parentCallbacks.forEach(callback => {
                    callback(this.getValueAtPath(path.slice(0, i)), oldValue, path);
                });
            }
        }
    }

    /**
     * Notify mutation subscribers
     */
    notifyMutationSubscribers(mutation) {
        this.actionSubscribers.forEach(({ type, callback }) => {
            if (type === 'mutation') {
                callback(mutation);
            }
        });
    }

    /**
     * Notify action subscribers
     */
    notifyActionSubscribers(action, stage) {
        this.actionSubscribers.forEach(({ type, callback }) => {
            if (type === 'action') {
                callback(action, stage);
            }
        });
    }

    /**
     * Get value at path
     */
    getValueAtPath(path) {
        return path.reduce((obj, key) => obj?.[key], this.state);
    }

    /**
     * Save state history
     */
    saveHistory() {
        const snapshot = JSON.parse(JSON.stringify(this.state));
        this.history.push({
            state: snapshot,
            timestamp: Date.now()
        });
        
        // Limit history size
        if (this.history.length > this.historyLimit) {
            this.history.shift();
        }
    }

    /**
     * Time travel to previous state
     */
    timeTravel(index) {
        if (index < 0 || index >= this.history.length) {
            console.error('[Store] Invalid history index');
            return;
        }
        
        const snapshot = this.history[index];
        this.state = this.createReactiveState(snapshot.state);
        
        // Notify all subscribers
        this.notifySubscribers([], this.state, null);
    }

    /**
     * Initialize persistence
     */
    initPersistence(config) {
        const {
            key = 'app-state',
            storage = localStorage,
            paths = null,
            filter = null
        } = config;
        
        // Load persisted state
        try {
            const savedState = storage.getItem(key);
            if (savedState) {
                const parsed = JSON.parse(savedState);
                if (paths) {
                    // Restore only specified paths
                    paths.forEach(path => {
                        const pathArray = path.split('.');
                        const value = pathArray.reduce((obj, key) => obj?.[key], parsed);
                        if (value !== undefined) {
                            this.setValueAtPath(pathArray, value);
                        }
                    });
                } else {
                    // Restore entire state
                    Object.assign(this.state, parsed);
                }
            }
        } catch (error) {
            console.error('[Store] Failed to load persisted state:', error);
        }
        
        // Subscribe to changes for persistence
        this.subscribeMutation(() => {
            try {
                let stateToSave = this.state;
                
                if (filter) {
                    stateToSave = filter(this.state);
                } else if (paths) {
                    stateToSave = {};
                    paths.forEach(path => {
                        const pathArray = path.split('.');
                        const value = this.getValueAtPath(pathArray);
                        this.setValueAtPath(pathArray, value, stateToSave);
                    });
                }
                
                storage.setItem(key, JSON.stringify(stateToSave));
            } catch (error) {
                console.error('[Store] Failed to persist state:', error);
            }
        });
    }

    /**
     * Set value at path
     */
    setValueAtPath(path, value, target = this.state) {
        const last = path.pop();
        const parent = path.reduce((obj, key) => {
            if (!obj[key]) obj[key] = {};
            return obj[key];
        }, target);
        parent[last] = value;
    }

    /**
     * Apply plugins
     */
    applyPlugins() {
        this.plugins.forEach(plugin => {
            plugin(this);
        });
    }

    /**
     * Create a namespaced helper
     */
    createNamespacedHelpers(namespace) {
        return {
            mapState: (states) => {
                const mapped = {};
                if (Array.isArray(states)) {
                    states.forEach(state => {
                        mapped[state] = () => this.state[namespace]?.[state];
                    });
                } else {
                    Object.entries(states).forEach(([key, value]) => {
                        if (typeof value === 'string') {
                            mapped[key] = () => this.state[namespace]?.[value];
                        } else if (typeof value === 'function') {
                            mapped[key] = () => value(this.state[namespace]);
                        }
                    });
                }
                return mapped;
            },
            mapMutations: (mutations) => {
                const mapped = {};
                if (Array.isArray(mutations)) {
                    mutations.forEach(mutation => {
                        mapped[mutation] = (payload) => this.commit(`${namespace}/${mutation}`, payload);
                    });
                } else {
                    Object.entries(mutations).forEach(([key, value]) => {
                        mapped[key] = (payload) => this.commit(`${namespace}/${value}`, payload);
                    });
                }
                return mapped;
            },
            mapActions: (actions) => {
                const mapped = {};
                if (Array.isArray(actions)) {
                    actions.forEach(action => {
                        mapped[action] = (payload) => this.dispatch(`${namespace}/${action}`, payload);
                    });
                } else {
                    Object.entries(actions).forEach(([key, value]) => {
                        mapped[key] = (payload) => this.dispatch(`${namespace}/${value}`, payload);
                    });
                }
                return mapped;
            },
            mapGetters: (getters) => {
                const mapped = {};
                if (Array.isArray(getters)) {
                    getters.forEach(getter => {
                        mapped[getter] = () => this.get(`${namespace}/${getter}`);
                    });
                } else {
                    Object.entries(getters).forEach(([key, value]) => {
                        mapped[key] = () => this.get(`${namespace}/${value}`);
                    });
                }
                return mapped;
            }
        };
    }

    /**
     * Reset store to initial state
     */
    reset() {
        this.history = [];
        Object.keys(this.state).forEach(key => {
            delete this.state[key];
        });
        
        // Re-initialize with original state
        if (this.options && this.options.state) {
            Object.assign(this.state, JSON.parse(JSON.stringify(this.options.state)));
        }
    }

    /**
     * Hot update modules
     */
    hotUpdate(options) {
        if (options.modules) {
            Object.entries(options.modules).forEach(([name, module]) => {
                this.modules[name] = module;
            });
            this.processModules();
        }
        
        if (options.mutations) {
            Object.assign(this.mutations, options.mutations);
        }
        
        if (options.actions) {
            Object.assign(this.actions, options.actions);
        }
        
        if (options.getters) {
            Object.assign(this.getters, options.getters);
        }
    }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Store;
} else {
    window.Store = Store;
}
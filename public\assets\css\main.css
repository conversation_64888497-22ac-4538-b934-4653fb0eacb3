/**
 * Archivo principal CSS que importa todos los módulos CSS
 */

/* Base */
@import url('base/base.css');

/* Layout */
@import url('layout/layout.css');

/* Theme */
@import url('theme/theme.css');

/* Utilities */
@import url('utilities/utilities.css');

/* Componentes */
@import url('../components/header/header.css');
@import url('../components/sidebar/sidebar.css');

/* Loader animation */
.loader {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 165, 233, 0.3);
    border-radius: 50%;
    border-top-color: #0ea5e9;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notificaciones */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    min-width: 300px;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

.notification-show {
    transform: translateX(0);
}

.notification-hide {
    transform: translateX(100%);
}

.notification-content {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-info {
    border-left: 4px solid #0ea5e9;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #374151;
}

/* Responsive - Clases específicas para distintos breakpoints */
@media (max-width: 768px) {
    /* Estilos móviles */
    .notifications-container {
        left: 20px;
        right: 20px;
    }
    
    .notification {
        min-width: auto;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    /* Estilos para tablets */
}

@media (min-width: 1025px) {
    /* Estilos desktop */
}
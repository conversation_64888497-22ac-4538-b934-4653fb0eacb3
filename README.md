# ProyectDash - PHP Single Page Application

Este proyecto es una implementación de una Single Page Application (SPA) en PHP con componentes reutilizables inspirados en React, utilizando Tailwind CSS para el diseño.

## Estructura del Proyecto

```
ProyectDash_/
├── public/                 # Directorio público accesible desde la web
│   ├── index.php           # Punto de entrada principal
│   ├── views/              # Vistas de la aplicación
│   │   ├── project.php     # Vista del dashboard de proyectos
│   │   ├── analytics.php   # Vista de análisis
│   │   └── ...             # Otras vistas
│   ├── assets/             # Recursos públicos (copias de los assets)
│   │   ├── css/            # Archivos CSS
│   │   ├── js/             # Archivos JavaScript
│   │   ├── components/     # Recursos específicos de componentes
│   │   └── img/            # Imágenes
│   ├── api/                # API RESTful
│   │   ├── index.php       # Punto de entrada de la API
│   │   ├── config/         # Configuración de la API
│   │   └── v1/             # Versión 1 de la API
│   │       ├── controllers/# Controladores de la API
│   │       └── models/     # Modelos de datos
│   └── .htaccess           # Configuración de Apache para el directorio público
├── components/             # Componentes reutilizables
│   ├── sidebar/            # Componente de barra lateral
│   │   └── FuseSidebar.class.php # Clase PHP del sidebar
│   └── FuseHeader.php      # Componente de encabezado
├── assets/                 # Recursos de desarrollo
│   ├── css/                # Archivos CSS
│   │   ├── main.css        # Archivo principal que importa todos los estilos
│   │   ├── base/           # Estilos base
│   │   ├── layout/         # Estilos de layout
│   │   ├── theme/          # Estilos del tema
│   │   └── utilities/      # Utilidades CSS
│   ├── js/                 # Archivos JavaScript
│   │   ├── main.js         # Archivo principal que carga todos los scripts
│   │   ├── core/           # Funcionalidades principales
│   │   └── utils/          # Utilidades JavaScript
│   └── components/         # Recursos de componentes
│       ├── header/         # Recursos del componente de encabezado
│       │   ├── header.css  # Estilos del encabezado
│       │   └── header.js   # JavaScript del encabezado
│       └── sidebar/        # Recursos del componente de barra lateral
│           ├── sidebar.css # Estilos de la barra lateral
│           └── sidebar.js  # JavaScript de la barra lateral
└── .htaccess               # Redirección al directorio public
```

## Arquitectura

### Single Page Application (SPA)
- La navegación se realiza sin recargar la página completa
- Las solicitudes AJAX cargan solo el contenido necesario
- La historia del navegador se actualiza mediante la API History

### Componentes
- Implementación basada en clases PHP reutilizables
- Cada componente tiene su propia lógica y estilos
- Los componentes se pueden reutilizar en diferentes vistas

### Estructura Modular
- Separación clara de responsabilidades
- Archivos organizados por funcionalidad
- Sistema escalable para agregar nuevos componentes y vistas

## Tecnologías Utilizadas

- **PHP**: Backend y renderizado de componentes
- **JavaScript**: Navegación SPA, AJAX, manipulación del DOM
- **Tailwind CSS**: Framework de utilidades CSS
- **Chart.js**: Visualización de datos
- **Luxon**: Manipulación de fechas y horas

## Componentes Principales

### FuseSidebar
- Barra lateral navegable
- Gestión de estados activos
- Navegación AJAX integrada

### FuseHeader
- Encabezado responsive
- Soporte para múltiples idiomas
- Sistema de notificaciones

## API RESTful

La API sigue principios RESTful:
- Endpoints organizados por recursos
- Métodos HTTP estándar (GET, POST, PUT, DELETE)
- Respuestas en formato JSON
- Versionado mediante URL (v1, v2, etc.)

## Guías de Desarrollo

### Agregar un Nuevo Componente

1. Crear una clase PHP en el directorio `components/`
2. Agregar archivos CSS/JS en `assets/components/nombre-componente/`
3. Importar los archivos en `main.css` y `main.js`
4. Instanciar y usar el componente en las vistas

### Agregar una Nueva Vista

1. Crear un archivo PHP en `public/views/`
2. Agregar la página a la lista de páginas válidas en `public/index.php`
3. Actualizar el sidebar para incluir un enlace a la nueva vista

### Integración con la API

1. Crear un controlador en `public/api/v1/controllers/`
2. Definir los modelos necesarios en `public/api/v1/models/`
3. Utilizar fetch en JavaScript para consumir la API

## Instalación y Configuración

### Requisitos
- PHP 7.4 o superior
- Servidor web con soporte para .htaccess (Apache recomendado)
- Módulo mod_rewrite habilitado

### Instalación
1. Clonar el repositorio
2. Configurar el servidor web para apuntar al directorio `public/`
3. Verificar permisos de archivos y directorios
4. Acceder a través del navegador
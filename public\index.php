<?php
/**
 * FUSE React Sidebar - PHP SPA Demo
 * 
 * Este archivo es el punto de entrada principal para la demostración de SPA.
 * Carga el sidebar y proporciona el contenedor para las páginas dinámicas.
 * 
 * <AUTHOR> with <PERSON>
 * @version 1.0
 */

// Incluir las clases de componentes
require_once __DIR__ . '/../components/sidebar/FuseSidebar.class.php';
require_once __DIR__ . '/../components/FuseHeader.php';

// Determinar la página activa basada en el parámetro de URL
$page = isset($_GET['page']) ? $_GET['page'] : 'project';

// Crear instancia del sidebar con la página activa
$sidebar = new FuseSidebar($page);

// Obtener el HTML del sidebar
$sidebarHtml = $sidebar->render();

// Lista de páginas válidas
$validPages = [
    'project', 'analytics', 'finance', 'crypto', 'ai-image', 
    'academy', 'calendar', 'messenger', 'contacts', 'ecommerce', 
    'file-manager', 'help-center'
];

// Verificar si la página solicitada es válida
if (!in_array($page, $validPages)) {
    $page = 'project'; // Página por defecto si la solicitada no es válida
}

// Función para obtener el título de la página
function getPageTitle($page) {
    $titles = [
        'project' => 'Project Dashboard',
        'analytics' => 'Analytics Dashboard',
        'finance' => 'Finance Dashboard',
        'crypto' => 'Crypto Dashboard',
        'ai-image' => 'AI Image Generator',
        'academy' => 'Academy',
        'calendar' => 'Calendar',
        'messenger' => 'Messenger',
        'contacts' => 'Contacts',
        'ecommerce' => 'E-Commerce',
        'file-manager' => 'File Manager',
        'help-center' => 'Help Center'
    ];
    
    return isset($titles[$page]) ? $titles[$page] : 'Dashboard';
}

// Obtener el título de la página
$pageTitle = getPageTitle($page);

// Determinar si se está haciendo una solicitud AJAX
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// Si es una solicitud AJAX, solo devolver el contenido de la página sin el sidebar
if ($isAjax) {
    // Construir la ruta al archivo de la vista
    $viewPath = __DIR__ . "/views/{$page}.php";

    // Comprobar si el archivo de la vista existe, si no, usar la vista por defecto
    if (!file_exists($viewPath)) {
        $viewPath = __DIR__ . "/views/project.php";
    }

    // Cargar el contenido de la vista
    ob_start();
    include $viewPath;
    $content = ob_get_clean();
    
    // Limpiar cualquier contenido de sidebar que pudiera estar en la página
    $content = preg_replace('/<div class="fuse-sidebar.*?<\/div>\s*<\/div>/s', '', $content);
    $content = preg_replace('/<script[^>]*sidebar\.js[^>]*>.*?<\/script>/s', '', $content);
    echo $content;
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - FUSE React PHP SPA</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/main.css">
    <!-- Cargar scripts externos -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>
    <!-- Cargar el archivo principal de JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="flex flex-col h-screen">
        <!-- Header -->
        <?php 
        $header = new FuseHeader();
        echo $header->render();
        ?>
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar - Solo incluido en el contenedor principal, no dentro de páginas individuales -->
            <div id="sidebar-container">
                <?php echo $sidebarHtml; ?>
            </div>
            
            <!-- Contenido principal -->
            <main class="flex-1 overflow-y-auto" id="main-content">
            <div class="main-content-inner">
                <?php include __DIR__ . "/views/{$page}.php"; ?>
            </div>
        </main>
        </div>
    </div>

    <script>
        // Pasar configuración a la inicialización del sistema
        window.appConfig = {
            initialPage: '<?php echo $page; ?>',
            baseUrl: '<?php echo rtrim(dirname($_SERVER["SCRIPT_NAME"]), "/") . "/"; ?>',
            debug: true
        };
        
        // Configuración de debugging para identificar problemas
        console.log('App config:', window.appConfig);
        console.log('Current page:', '<?php echo $page; ?>');
        console.log('Base URL:', window.appConfig.baseUrl);
        
        // La inicialización se maneja en main.js
    </script>
</body>
</html>
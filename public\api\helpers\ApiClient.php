<?php
/**
 * Cliente base para la API
 * 
 * Esta clase proporciona una base para realizar peticiones a la API
 * con métodos para manejar la autenticación, cabeceras y respuestas.
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

class ApiClient {
    /**
     * URL base de la API
     * @var string
     */
    private $baseUrl;
    
    /**
     * Token de autenticación
     * @var string|null
     */
    private $token;
    
    /**
     * Cabeceras personalizadas
     * @var array
     */
    private $headers = [];
    
    /**
     * Timeout para las peticiones en segundos
     * @var int
     */
    private $timeout = 30;
    
    /**
     * Constructor
     * 
     * @param string $baseUrl URL base de la API
     * @param string|null $token Token de autenticación opcional
     */
    public function __construct($baseUrl, $token = null) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->token = $token;
        
        // Cabeceras por defecto
        $this->headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];
        
        // Añadir token si existe
        if ($this->token) {
            $this->headers['Authorization'] = 'Bearer ' . $this->token;
        }
    }
    
    /**
     * Establece un token de autenticación
     * 
     * @param string $token Token de autenticación
     * @return self
     */
    public function setToken($token) {
        $this->token = $token;
        $this->headers['Authorization'] = 'Bearer ' . $token;
        return $this;
    }
    
    /**
     * Establece una cabecera personalizada
     * 
     * @param string $name Nombre de la cabecera
     * @param string $value Valor de la cabecera
     * @return self
     */
    public function setHeader($name, $value) {
        $this->headers[$name] = $value;
        return $this;
    }
    
    /**
     * Establece el timeout para las peticiones
     * 
     * @param int $seconds Segundos para el timeout
     * @return self
     */
    public function setTimeout($seconds) {
        $this->timeout = max(1, intval($seconds));
        return $this;
    }
    
    /**
     * Realiza una petición GET
     * 
     * @param string $endpoint Endpoint de la API
     * @param array $params Parámetros para la query string
     * @return array Respuesta de la API como array asociativo
     * @throws Exception Si hay un error en la petición
     */
    public function get($endpoint, $params = []) {
        $url = $this->buildUrl($endpoint, $params);
        return $this->request('GET', $url);
    }
    
    /**
     * Realiza una petición POST
     * 
     * @param string $endpoint Endpoint de la API
     * @param array $data Datos a enviar en el cuerpo de la petición
     * @return array Respuesta de la API como array asociativo
     * @throws Exception Si hay un error en la petición
     */
    public function post($endpoint, $data = []) {
        $url = $this->buildUrl($endpoint);
        return $this->request('POST', $url, $data);
    }
    
    /**
     * Realiza una petición PUT
     * 
     * @param string $endpoint Endpoint de la API
     * @param array $data Datos a enviar en el cuerpo de la petición
     * @return array Respuesta de la API como array asociativo
     * @throws Exception Si hay un error en la petición
     */
    public function put($endpoint, $data = []) {
        $url = $this->buildUrl($endpoint);
        return $this->request('PUT', $url, $data);
    }
    
    /**
     * Realiza una petición PATCH
     * 
     * @param string $endpoint Endpoint de la API
     * @param array $data Datos a enviar en el cuerpo de la petición
     * @return array Respuesta de la API como array asociativo
     * @throws Exception Si hay un error en la petición
     */
    public function patch($endpoint, $data = []) {
        $url = $this->buildUrl($endpoint);
        return $this->request('PATCH', $url, $data);
    }
    
    /**
     * Realiza una petición DELETE
     * 
     * @param string $endpoint Endpoint de la API
     * @param array $params Parámetros para la query string
     * @return array Respuesta de la API como array asociativo
     * @throws Exception Si hay un error en la petición
     */
    public function delete($endpoint, $params = []) {
        $url = $this->buildUrl($endpoint, $params);
        return $this->request('DELETE', $url);
    }
    
    /**
     * Construye la URL completa para la petición
     * 
     * @param string $endpoint Endpoint de la API
     * @param array $params Parámetros para la query string
     * @return string URL completa
     */
    private function buildUrl($endpoint, $params = []) {
        $endpoint = ltrim($endpoint, '/');
        $url = $this->baseUrl . '/' . $endpoint;
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * Realiza una petición HTTP
     * 
     * @param string $method Método HTTP (GET, POST, etc)
     * @param string $url URL completa
     * @param array $data Datos a enviar en el cuerpo (para POST, PUT, PATCH)
     * @return array Respuesta de la API como array asociativo
     * @throws Exception Si hay un error en la petición
     */
    private function request($method, $url, $data = null) {
        // Inicializar cURL
        $curl = curl_init();
        
        // Formatear las cabeceras para cURL
        $headers = [];
        foreach ($this->headers as $name => $value) {
            $headers[] = "$name: $value";
        }
        
        // Configurar opciones de cURL
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
        ]);
        
        // Añadir datos para métodos que los admiten
        if ($data !== null && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        // Ejecutar la petición
        $response = curl_exec($curl);
        $error = curl_error($curl);
        $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        // Cerrar la conexión
        curl_close($curl);
        
        // Manejar errores
        if ($error) {
            throw new Exception("Error en la petición cURL: $error");
        }
        
        // Decodificar la respuesta
        $decodedResponse = json_decode($response, true);
        
        // Verificar si la decodificación fue exitosa
        if ($decodedResponse === null && json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Error al decodificar la respuesta JSON: " . json_last_error_msg() . " - Respuesta: $response");
        }
        
        // Verificar el código de estado
        if ($statusCode >= 400) {
            $errorMessage = isset($decodedResponse['message']) ? $decodedResponse['message'] : "Error $statusCode";
            throw new Exception("Error en la API: $errorMessage", $statusCode);
        }
        
        return $decodedResponse;
    }
}
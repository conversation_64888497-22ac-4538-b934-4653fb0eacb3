<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Header Functions</title>
    <link rel="stylesheet" href="assets/css/header.css">
</head>
<body>
    <!-- Head<PERSON> de prueba -->
    <header class="fuse-header">
        <div class="header-logo">
            <div class="logo-icon">F</div>
            <div class="logo-text">Fuse</div>
        </div>
        
        <div class="header-nav-buttons">
            <!-- Botón de pantalla completa -->
            <button class="nav-button fullscreen-toggle" title="Pantalla completa">
                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"/>
                </svg>
            </button>
            
            <!-- Botón de notificaciones -->
            <button class="nav-button notification-toggle" title="Notificaciones">
                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"/>
                </svg>
                <span class="notification-badge">3</span>
            </button>
            
            <!-- Botón de menú lateral -->
            <button class="nav-button canvas-lateral-toggle" title="Menú lateral">
                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
            </button>
        </div>
    </header>
    
    <!-- Panel de notificaciones -->
    <div id="notificationsCanvas" class="notifications-panel">
        <div class="panel-header">
            <h3 class="panel-title">Notificaciones</h3>
        </div>
        <div class="notification-item">
            <p>Nueva notificación 1</p>
        </div>
        <div class="notification-item">
            <p>Nueva notificación 2</p>
        </div>
        <div class="notification-item">
            <p>Nueva notificación 3</p>
        </div>
    </div>
    
    <!-- Canvas lateral -->
    <div id="canvasLateral" class="lateral-panel">
        <div class="panel-header">
            <h3 class="panel-title">Menú Lateral</h3>
        </div>
        <div class="menu-item">
            <p>Opción 1</p>
        </div>
        <div class="menu-item">
            <p>Opción 2</p>
        </div>
        <div class="menu-item">
            <p>Opción 3</p>
        </div>
    </div>
    
    <!-- Test buttons -->
    <div style="margin: 20px; text-align: center;">
        <h2>Test de Funciones</h2>
        <button onclick="toggleFullscreen()" style="margin: 5px; padding: 10px;">Test Pantalla Completa</button>
        <button onclick="toggleNotifications()" style="margin: 5px; padding: 10px;">Test Notificaciones</button>
        <button onclick="toggleCanvasLateral()" style="margin: 5px; padding: 10px;">Test Canvas Lateral</button>
        
        <div style="margin-top: 20px;">
            <h3>Estado de las funciones:</h3>
            <p id="status"></p>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="checkFunctions()" style="padding: 10px; background: #0ea5e9; color: white; border: none; border-radius: 5px;">
                Verificar Funciones
            </button>
        </div>
    </div>
    
    <script src="assets/components/header/header.js"></script>
    <script>
        function checkFunctions() {
            const status = document.getElementById('status');
            let result = '';
            
            result += 'toggleFullscreen: ' + (typeof window.toggleFullscreen === 'function' ? '✅' : '❌') + '<br>';
            result += 'toggleNotifications: ' + (typeof window.toggleNotifications === 'function' ? '✅' : '❌') + '<br>';
            result += 'toggleCanvasLateral: ' + (typeof window.toggleCanvasLateral === 'function' ? '✅' : '❌') + '<br>';
            
            // Verificar elementos DOM
            result += '<br>Elementos DOM:<br>';
            result += '.fullscreen-toggle: ' + (document.querySelector('.fullscreen-toggle') ? '✅' : '❌') + '<br>';
            result += '.notification-toggle: ' + (document.querySelector('.notification-toggle') ? '✅' : '❌') + '<br>';
            result += '.canvas-lateral-toggle: ' + (document.querySelector('.canvas-lateral-toggle') ? '✅' : '❌') + '<br>';
            result += '#notificationsCanvas: ' + (document.getElementById('notificationsCanvas') ? '✅' : '❌') + '<br>';
            result += '#canvasLateral: ' + (document.getElementById('canvasLateral') ? '✅' : '❌') + '<br>';
            
            status.innerHTML = result;
        }
        
        // Verificar automáticamente al cargar
        window.addEventListener('load', checkFunctions);
    </script>
</body>
</html>
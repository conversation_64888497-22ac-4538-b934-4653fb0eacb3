/**
 * Navegación simplificada
 * Sistema directo de rutas sin complejidad
 */

// Variables globales
let currentPage = 'project';

/**
 * Navegación directa a página
 * @param {string} page - ID de la página
 */
function navigateToPage(page) {
    if (page === currentPage) return;
    
    console.log('Navegando a:', page);
    
    // Actualizar sidebar
    if (typeof setActiveItem === 'function') {
        setActiveItem(page);
    }
    
    // Cargar contenido
    loadPageContent(page);
}

/**
 * Cargar contenido de página
 * @param {string} page - ID de la página
 */
function loadPageContent(page) {
    const mainContent = document.getElementById('main-content');
    
    // Mostrar cargando
    if (mainContent) {
        mainContent.innerHTML = '<div class="flex justify-center items-center h-full"><div class="loader">Cargando...</div></div>';
    }
    
    // URL simple
    const pageUrl = `/ProyectDash_/views/${page}.php`;
    
    fetch(pageUrl, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        if (mainContent) {
            mainContent.innerHTML = html;
        }
        
        // Actualizar URL
        const newUrl = `${window.location.pathname}?page=${page}`;
        window.history.pushState({ page: page }, '', newUrl);
        currentPage = page;
        
        // Actualizar título
        document.title = getPageTitle(page);
    })
    .catch(error => {
        console.error('Error:', error);
        if (mainContent) {
            mainContent.innerHTML = `<div class="text-red-500">Error al cargar ${page}</div>`;
        }
    });
}

/**
 * Obtener título de página
 * @param {string} page
 */
function getPageTitle(page) {
    const titles = {
        'project': 'Project Dashboard',
        'analytics': 'Analytics Dashboard',
        'finance': 'Finance Dashboard',
        'crypto': 'Crypto Dashboard'
    };
    return titles[page] || page;
}

/**
 * Inicializar navegación
 */
function initNavigation() {
    // Detectar página actual
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = urlParams.get('page') || 'project';
    
    // Cargar página inicial
    loadPageContent(currentPage);
    
    // Manejar navegación del navegador
    window.addEventListener('popstate', (event) => {
        if (event.state && event.state.page) {
            loadPageContent(event.state.page);
        }
    });
}

/**
 * Inicializa los componentes específicos de la página
 */
function initPageComponents() {
    // Inicializar gráficos si existen
    const chartElements = document.querySelectorAll('.chart-container canvas');
    if (chartElements.length > 0) {
        initCharts();
    }
    
    // Inicializar calendarios si existen
    const calendarElements = document.querySelectorAll('.calendar-container');
    if (calendarElements.length > 0) {
        initCalendars();
    }
    
    // Inicializar tablas de datos si existen
    const dataTableElements = document.querySelectorAll('.data-table');
    if (dataTableElements.length > 0) {
        initDataTables();
    }
}

/**
 * Inicializa los gráficos de la página
 */
function initCharts() {
    const charts = document.querySelectorAll('.chart-container canvas');
    charts.forEach(canvas => {
        const chartType = canvas.dataset.chartType || 'line';
        const chartId = canvas.id;
        
        if (chartId === 'salesChart') {
            initSalesChart(canvas);
        } else if (chartId === 'visitsChart') {
            initVisitsChart(canvas);
        } else if (chartId === 'conversionChart') {
            initConversionChart(canvas);
        }
    });
}

/**
 * Inicializa el gráfico de ventas
 * @param {HTMLCanvasElement} canvas - Elemento canvas del gráfico
 */
function initSalesChart(canvas) {
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Sales',
                data: [12, 19, 3, 5, 2, 3, 20, 33, 23, 12, 33, 55],
                borderColor: '#0ea5e9',
                backgroundColor: 'rgba(14, 165, 233, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de visitas
 * @param {HTMLCanvasElement} canvas - Elemento canvas del gráfico
 */
function initVisitsChart(canvas) {
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Visits',
                data: [1200, 1900, 3000, 5000, 2000, 3000, 4000],
                backgroundColor: '#0ea5e9',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Inicializa el gráfico de conversión
 * @param {HTMLCanvasElement} canvas - Elemento canvas del gráfico
 */
function initConversionChart(canvas) {
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Direct', 'Social', 'Email', 'Other'],
            datasets: [{
                data: [30, 40, 20, 10],
                backgroundColor: ['#0ea5e9', '#ef4444', '#10b981', '#f59e0b'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '70%'
        }
    });
}

/**
 * Inicializa los calendarios
 */
function initCalendars() {
    // Implementación del calendario si es necesario
    console.log('Calendars initialized');
}

/**
 * Inicializa las tablas de datos
 */
function initDataTables() {
    // Implementación de tablas de datos si es necesario
    console.log('Data tables initialized');
}
# API .htaccess
# Configuración para API RESTful

# Activar el motor de reescritura
RewriteEngine On

# Redirigir todas las solicitudes al index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L,QSA]

# Establecer encabezados CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header set Access-Control-Max-Age "86400"
    Header set Content-Type "application/json" 
</IfModule>

# Agregar encabezados de seguridad
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "DENY"
</IfModule>

# Deshabilitar listado de directorios
Options -Indexes
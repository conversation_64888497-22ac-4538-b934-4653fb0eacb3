<!-- Dashboard Page -->
<div class="page-content">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h1 class="page-title">Project Dashboard</h1>
        <div class="page-actions">
            <button class="btn btn-secondary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                Export
            </button>
            <button class="btn btn-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Project
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Total Projects</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 text-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">42</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        12%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Compared to last month</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Active Tasks</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-50 text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">128</h2>
                    <span class="ml-2 text-sm font-medium text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        5%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Compared to last month</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Team Members</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-purple-50 text-purple-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">16</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        2%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Compared to last month</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Budget Spent</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-50 text-yellow-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$32,450</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        8%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Compared to last month</p>
            </div>
        </div>
    </div>

    <!-- Project Progress & Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Project Progress -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Project Progress</h2>
                <div>
                    <button class="btn btn-sm btn-secondary">View All</button>
                </div>
            </div>
            <div class="card-body p-0">
                <ul class="divide-y divide-gray-200">
                    <li class="p-4">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium text-gray-900">Website Redesign</span>
                            <span class="badge badge-success">On Track</span>
                        </div>
                        <div class="text-sm text-gray-500 mb-2">Deadline: 15 Aug 2023</div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                        </div>
                        <div class="mt-2 flex items-center justify-between text-xs text-gray-500">
                            <span>Progress: 75%</span>
                            <div class="flex -space-x-2">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=John+Doe&background=random" alt="">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Jane+Smith&background=random" alt="">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Bob+Johnson&background=random" alt="">
                            </div>
                        </div>
                    </li>
                    <li class="p-4">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium text-gray-900">Mobile App Development</span>
                            <span class="badge badge-warning">At Risk</span>
                        </div>
                        <div class="text-sm text-gray-500 mb-2">Deadline: 30 Sep 2023</div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                        <div class="mt-2 flex items-center justify-between text-xs text-gray-500">
                            <span>Progress: 45%</span>
                            <div class="flex -space-x-2">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Emma+Wilson&background=random" alt="">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Mike+Brown&background=random" alt="">
                            </div>
                        </div>
                    </li>
                    <li class="p-4">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium text-gray-900">E-commerce Platform</span>
                            <span class="badge badge-success">On Track</span>
                        </div>
                        <div class="text-sm text-gray-500 mb-2">Deadline: 10 Oct 2023</div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 60%"></div>
                        </div>
                        <div class="mt-2 flex items-center justify-between text-xs text-gray-500">
                            <span>Progress: 60%</span>
                            <div class="flex -space-x-2">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Sarah+Jones&background=random" alt="">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Tom+Davis&background=random" alt="">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=Lisa+Miller&background=random" alt="">
                                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://ui-avatars.com/api/?name=David+Garcia&background=random" alt="">
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Recent Activity</h2>
                <div>
                    <button class="btn btn-sm btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <ul class="divide-y divide-gray-200">
                    <li class="p-4 flex">
                        <div class="flex-shrink-0 mr-3">
                            <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe&background=random" alt="">
                        </div>
                        <div>
                            <p class="text-sm text-gray-900">
                                <span class="font-medium">John Doe</span> completed task 
                                <span class="font-medium">Update homepage design</span>
                            </p>
                            <p class="text-xs text-gray-500 mt-1">5 minutes ago</p>
                        </div>
                    </li>
                    <li class="p-4 flex">
                        <div class="flex-shrink-0 mr-3">
                            <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Jane+Smith&background=random" alt="">
                        </div>
                        <div>
                            <p class="text-sm text-gray-900">
                                <span class="font-medium">Jane Smith</span> added a comment on 
                                <span class="font-medium">E-commerce Platform</span>
                            </p>
                            <div class="mt-2 p-2 bg-gray-50 rounded text-xs text-gray-700">
                                "We should review the payment gateway integration before proceeding further."
                            </div>
                            <p class="text-xs text-gray-500 mt-1">35 minutes ago</p>
                        </div>
                    </li>
                    <li class="p-4 flex">
                        <div class="flex-shrink-0 mr-3">
                            <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Mike+Brown&background=random" alt="">
                        </div>
                        <div>
                            <p class="text-sm text-gray-900">
                                <span class="font-medium">Mike Brown</span> updated status of 
                                <span class="font-medium">Mobile App Development</span> to 
                                <span class="text-yellow-600 font-medium">At Risk</span>
                            </p>
                            <p class="text-xs text-gray-500 mt-1">2 hours ago</p>
                        </div>
                    </li>
                    <li class="p-4 flex">
                        <div class="flex-shrink-0 mr-3">
                            <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Emma+Wilson&background=random" alt="">
                        </div>
                        <div>
                            <p class="text-sm text-gray-900">
                                <span class="font-medium">Emma Wilson</span> uploaded 5 new files to 
                                <span class="font-medium">Mobile App Development</span>
                            </p>
                            <div class="mt-2 grid grid-cols-5 gap-2">
                                <div class="h-12 bg-blue-50 rounded flex items-center justify-center text-blue-500 text-xs">UI.fig</div>
                                <div class="h-12 bg-green-50 rounded flex items-center justify-center text-green-500 text-xs">API.doc</div>
                                <div class="h-12 bg-purple-50 rounded flex items-center justify-center text-purple-500 text-xs">Flow.pdf</div>
                                <div class="h-12 bg-yellow-50 rounded flex items-center justify-center text-yellow-500 text-xs">Auth.js</div>
                                <div class="h-12 bg-red-50 rounded flex items-center justify-center text-red-500 text-xs">Logo.png</div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">4 hours ago</p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Chart & Tasks -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Chart -->
        <div class="card lg:col-span-2">
            <div class="card-header">
                <h2 class="card-title">Project Performance</h2>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-secondary active">This Month</button>
                    <button class="btn btn-sm btn-secondary">Last Month</button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="salesChart" data-chart-type="line"></canvas>
                </div>
            </div>
        </div>

        <!-- Upcoming Tasks -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Upcoming Tasks</h2>
                <div>
                    <button class="btn btn-sm btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Add
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <ul class="divide-y divide-gray-200">
                    <li class="p-4">
                        <div class="flex justify-between">
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 rounded border-gray-300 text-blue-600">
                                <div>
                                    <p class="font-medium text-gray-900">Update user documentation</p>
                                    <p class="text-sm text-gray-500 mt-1">Website Redesign</p>
                                </div>
                            </div>
                            <span class="text-xs text-red-500 font-medium">Today</span>
                        </div>
                    </li>
                    <li class="p-4">
                        <div class="flex justify-between">
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 rounded border-gray-300 text-blue-600">
                                <div>
                                    <p class="font-medium text-gray-900">Review pull requests</p>
                                    <p class="text-sm text-gray-500 mt-1">Mobile App Development</p>
                                </div>
                            </div>
                            <span class="text-xs text-orange-500 font-medium">Tomorrow</span>
                        </div>
                    </li>
                    <li class="p-4">
                        <div class="flex justify-between">
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 rounded border-gray-300 text-blue-600">
                                <div>
                                    <p class="font-medium text-gray-900">Prepare weekly report</p>
                                    <p class="text-sm text-gray-500 mt-1">Project Management</p>
                                </div>
                            </div>
                            <span class="text-xs text-gray-500 font-medium">In 2 days</span>
                        </div>
                    </li>
                    <li class="p-4">
                        <div class="flex justify-between">
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 rounded border-gray-300 text-blue-600">
                                <div>
                                    <p class="font-medium text-gray-900">Team meeting</p>
                                    <p class="text-sm text-gray-500 mt-1">E-commerce Platform</p>
                                </div>
                            </div>
                            <span class="text-xs text-gray-500 font-medium">In 3 days</span>
                        </div>
                    </li>
                    <li class="p-4">
                        <div class="flex justify-between">
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 rounded border-gray-300 text-blue-600">
                                <div>
                                    <p class="font-medium text-gray-900">Client presentation</p>
                                    <p class="text-sm text-gray-500 mt-1">Website Redesign</p>
                                </div>
                            </div>
                            <span class="text-xs text-gray-500 font-medium">In 5 days</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="card-footer bg-gray-50">
                <button class="w-full text-center text-sm text-blue-600 hover:text-blue-700">
                    View All Tasks
                </button>
            </div>
        </div>
    </div>
</div>
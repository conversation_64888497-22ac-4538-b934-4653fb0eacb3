<?php
/**
 * Ejemplo de Modelo: ProjectModel
 * 
 * Integración con el modelo Project existente usando el nuevo BaseModel
 * Demuestra cómo migrar modelos existentes al nuevo sistema
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

require_once __DIR__ . '/../core/BaseModel.php';

class ProjectModel extends BaseModel {
    /**
     * Nombre de la tabla
     * @var string
     */
    protected $table = 'projects';
    
    /**
     * Campos asignables masivamente
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
        'priority',
        'start_date',
        'end_date',
        'budget',
        'client_id',
        'manager_id'
    ];
    
    /**
     * Campos protegidos
     * @var array
     */
    protected $guarded = [
        'id',
        'created_at',
        'updated_at'
    ];
    
    /**
     * Activar timestamps automáticos
     * @var bool
     */
    protected $timestamps = true;
    
    /**
     * Activar soft deletes
     * @var bool
     */
    protected $softDeletes = true;
    
    /**
     * Reglas de validación
     * @var array
     */
    protected $rules = [
        'name' => 'required|min:3|max:200',
        'description' => 'required|min:10',
        'status' => 'required',
        'priority' => 'required',
        'start_date' => 'required',
        'budget' => 'numeric',
        'client_id' => 'numeric',
        'manager_id' => 'numeric'
    ];
    
    /**
     * Estados válidos del proyecto
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_ACTIVE = 'active';
    const STATUS_ON_HOLD = 'on_hold';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    
    /**
     * Prioridades válidas
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_CRITICAL = 'critical';
    
    /**
     * Scope para proyectos activos
     * 
     * @param QueryBuilder $query
     * @return QueryBuilder
     */
    public function scopeActive(QueryBuilder $query): QueryBuilder {
        return $query->where('status', self::STATUS_ACTIVE);
    }
    
    /**
     * Scope para proyectos por estado
     * 
     * @param QueryBuilder $query
     * @param string $status
     * @return QueryBuilder
     */
    public function scopeByStatus(QueryBuilder $query, string $status): QueryBuilder {
        return $query->where('status', $status);
    }
    
    /**
     * Scope para proyectos por prioridad
     * 
     * @param QueryBuilder $query
     * @param string $priority
     * @return QueryBuilder
     */
    public function scopeByPriority(QueryBuilder $query, string $priority): QueryBuilder {
        return $query->where('priority', $priority);
    }
    
    /**
     * Scope para proyectos por cliente
     * 
     * @param QueryBuilder $query
     * @param int $clientId
     * @return QueryBuilder
     */
    public function scopeByClient(QueryBuilder $query, int $clientId): QueryBuilder {
        return $query->where('client_id', $clientId);
    }
    
    /**
     * Scope para proyectos por manager
     * 
     * @param QueryBuilder $query
     * @param int $managerId
     * @return QueryBuilder
     */
    public function scopeByManager(QueryBuilder $query, int $managerId): QueryBuilder {
        return $query->where('manager_id', $managerId);
    }
    
    /**
     * Scope para buscar proyectos
     * 
     * @param QueryBuilder $query
     * @param string $search
     * @return QueryBuilder
     */
    public function scopeSearch(QueryBuilder $query, string $search): QueryBuilder {
        return $query->where('name', 'like', "%$search%")
                    ->orWhere('description', 'like', "%$search%");
    }
    
    /**
     * Scope para proyectos con presupuesto
     * 
     * @param QueryBuilder $query
     * @param float $minBudget
     * @param float $maxBudget
     * @return QueryBuilder
     */
    public function scopeByBudgetRange(QueryBuilder $query, float $minBudget, float $maxBudget): QueryBuilder {
        return $query->whereBetween('budget', $minBudget, $maxBudget);
    }
    
    /**
     * Verificar si el proyecto está activo
     * 
     * @return bool
     */
    public function isActive(): bool {
        return $this->getAttribute('status') === self::STATUS_ACTIVE;
    }
    
    /**
     * Verificar si el proyecto está completado
     * 
     * @return bool
     */
    public function isCompleted(): bool {
        return $this->getAttribute('status') === self::STATUS_COMPLETED;
    }
    
    /**
     * Verificar si el proyecto está vencido
     * 
     * @return bool
     */
    public function isOverdue(): bool {
        $endDate = $this->getAttribute('end_date');
        return $endDate && strtotime($endDate) < time() && !$this->isCompleted();
    }
    
    /**
     * Obtener el progreso del proyecto (0-100)
     * 
     * @return float
     * @throws Exception
     */
    public function getProgress(): float {
        $totalTasks = static::$db->query(
            "SELECT COUNT(*) as total FROM project_tasks WHERE project_id = ?", 
            [$this->getAttribute('id')]
        )->fetch()['total'];
        
        if ($totalTasks == 0) {
            return 0;
        }
        
        $completedTasks = static::$db->query(
            "SELECT COUNT(*) as completed FROM project_tasks WHERE project_id = ? AND status = 'completed'", 
            [$this->getAttribute('id')]
        )->fetch()['completed'];
        
        return round(($completedTasks / $totalTasks) * 100, 2);
    }
    
    /**
     * Obtener días restantes
     * 
     * @return int|null
     */
    public function getDaysRemaining(): ?int {
        $endDate = $this->getAttribute('end_date');
        if (!$endDate) {
            return null;
        }
        
        $endTimestamp = strtotime($endDate);
        $currentTimestamp = time();
        
        return (int) ceil(($endTimestamp - $currentTimestamp) / (24 * 60 * 60));
    }
    
    /**
     * Obtener el cliente del proyecto
     * 
     * @return array|null
     * @throws Exception
     */
    public function getClient(): ?array {
        $clientId = $this->getAttribute('client_id');
        if (!$clientId) {
            return null;
        }
        
        return static::$db->query(
            "SELECT * FROM clients WHERE id = ?", 
            [$clientId]
        )->fetch() ?: null;
    }
    
    /**
     * Obtener el manager del proyecto
     * 
     * @return array|null
     * @throws Exception
     */
    public function getManager(): ?array {
        $managerId = $this->getAttribute('manager_id');
        if (!$managerId) {
            return null;
        }
        
        return static::$db->query(
            "SELECT id, name, email FROM users WHERE id = ?", 
            [$managerId]
        )->fetch() ?: null;
    }
    
    /**
     * Obtener tareas del proyecto
     * 
     * @return array
     * @throws Exception
     */
    public function getTasks(): array {
        return static::$db->query(
            "SELECT * FROM project_tasks WHERE project_id = ? ORDER BY priority DESC, created_at ASC", 
            [$this->getAttribute('id')]
        )->fetchAll();
    }
    
    /**
     * Obtener miembros del equipo
     * 
     * @return array
     * @throws Exception
     */
    public function getTeamMembers(): array {
        return static::$db->query(
            "SELECT u.id, u.name, u.email, pm.role, pm.joined_at 
             FROM users u 
             INNER JOIN project_members pm ON u.id = pm.user_id 
             WHERE pm.project_id = ? 
             ORDER BY pm.joined_at ASC", 
            [$this->getAttribute('id')]
        )->fetchAll();
    }
    
    /**
     * Calcular presupuesto utilizado
     * 
     * @return float
     * @throws Exception
     */
    public function getBudgetUsed(): float {
        $result = static::$db->query(
            "SELECT COALESCE(SUM(amount), 0) as used FROM project_expenses WHERE project_id = ?", 
            [$this->getAttribute('id')]
        )->fetch();
        
        return (float) $result['used'];
    }
    
    /**
     * Cambiar estado del proyecto
     * 
     * @param string $status
     * @param string $reason
     * @return bool
     * @throws Exception
     */
    public function changeStatus(string $status, string $reason = ''): bool {
        $validStatuses = [
            self::STATUS_DRAFT,
            self::STATUS_ACTIVE,
            self::STATUS_ON_HOLD,
            self::STATUS_COMPLETED,
            self::STATUS_CANCELLED
        ];
        
        if (!in_array($status, $validStatuses)) {
            throw new InvalidArgumentException("Estado no válido: $status");
        }
        
        $oldStatus = $this->getAttribute('status');
        $this->setAttribute('status', $status);
        
        // Registrar cambio de estado en log
        if ($this->save()) {
            static::$db->query(
                "INSERT INTO project_status_log (project_id, old_status, new_status, reason, changed_by, created_at) 
                 VALUES (?, ?, ?, ?, ?, ?)",
                [
                    $this->getAttribute('id'),
                    $oldStatus,
                    $status,
                    $reason,
                    $_SESSION['user_id'] ?? null,
                    date('Y-m-d H:i:s')
                ]
            );
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Convertir a array con información adicional
     * 
     * @return array
     */
    public function toArray(): array {
        $attributes = parent::toArray();
        
        try {
            // Añadir campos calculados
            $attributes['is_active'] = $this->isActive();
            $attributes['is_completed'] = $this->isCompleted();
            $attributes['is_overdue'] = $this->isOverdue();
            $attributes['progress'] = $this->getProgress();
            $attributes['days_remaining'] = $this->getDaysRemaining();
            $attributes['budget_used'] = $this->getBudgetUsed();
            
            // Añadir información de relaciones si está disponible
            $attributes['client'] = $this->getClient();
            $attributes['manager'] = $this->getManager();
            
        } catch (Exception $e) {
            // En caso de error, continuar sin los campos adicionales
            error_log("Error al cargar datos adicionales del proyecto: " . $e->getMessage());
        }
        
        return $attributes;
    }
    
    /**
     * Obtener estadísticas del proyecto
     * 
     * @return array
     * @throws Exception
     */
    public function getStatistics(): array {
        $projectId = $this->getAttribute('id');
        
        // Tareas
        $taskStats = static::$db->query(
            "SELECT 
                COUNT(*) as total_tasks,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as active_tasks,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks
             FROM project_tasks 
             WHERE project_id = ?",
            [$projectId]
        )->fetch();
        
        // Presupuesto
        $budgetStats = static::$db->query(
            "SELECT 
                COALESCE(SUM(amount), 0) as total_expenses,
                COUNT(*) as expense_count
             FROM project_expenses 
             WHERE project_id = ?",
            [$projectId]
        )->fetch();
        
        // Miembros del equipo
        $teamStats = static::$db->query(
            "SELECT COUNT(*) as team_size FROM project_members WHERE project_id = ?",
            [$projectId]
        )->fetch();
        
        return [
            'tasks' => $taskStats,
            'budget' => [
                'total' => (float) $this->getAttribute('budget'),
                'used' => (float) $budgetStats['total_expenses'],
                'remaining' => (float) $this->getAttribute('budget') - (float) $budgetStats['total_expenses'],
                'expense_count' => (int) $budgetStats['expense_count']
            ],
            'team' => [
                'size' => (int) $teamStats['team_size']
            ],
            'timeline' => [
                'start_date' => $this->getAttribute('start_date'),
                'end_date' => $this->getAttribute('end_date'),
                'days_remaining' => $this->getDaysRemaining(),
                'is_overdue' => $this->isOverdue()
            ]
        ];
    }
}
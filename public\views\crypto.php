<!-- Crypto Dashboard -->
<div class="page-content">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h1 class="page-title">Crypto Dashboard</h1>
        <div class="page-actions">
            <button class="btn btn-secondary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export Data
            </button>
            <button class="btn btn-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Trade Now
            </button>
        </div>
    </div>

    <!-- Crypto Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Bitcoin (BTC)</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-orange-50 text-orange-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$42,847.32</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        2.4%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Last 24h</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Ethereum (ETH)</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-indigo-50 text-indigo-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$2,563.47</h2>
                    <span class="ml-2 text-sm font-medium text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        1.2%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Last 24h</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Portfolio Value</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-50 text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$15,847.23</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        12.7%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Total portfolio</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">24h Volume</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-50 text-yellow-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$2.4B</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        23.1%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Global 24h volume</p>
            </div>
        </div>
    </div>

    <!-- Crypto Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="card">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Price Trends</h3>
                <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="text-gray-400 mb-2">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <p class="text-sm text-gray-500">Cryptocurrency Price Chart</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Trading Volume</h3>
                <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="text-gray-400 mb-2">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <p class="text-sm text-gray-500">Trading Volume Chart</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Cryptocurrencies -->
    <div class="card">
        <div class="p-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Top Cryptocurrencies</h3>
                <button class="text-sm text-blue-600 hover:text-blue-700">View All</button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">24h Change</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Cap</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr>
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-orange-600 font-bold text-sm">₿</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Bitcoin</div>
                                        <div class="text-sm text-gray-500">BTC</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-900">$42,847.32</td>
                            <td class="px-4 py-3 text-sm text-green-600">+2.4%</td>
                            <td class="px-4 py-3 text-sm text-gray-900">$842.5B</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-indigo-600 font-bold text-sm">Ξ</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Ethereum</div>
                                        <div class="text-sm text-gray-500">ETH</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-900">$2,563.47</td>
                            <td class="px-4 py-3 text-sm text-red-600">-1.2%</td>
                            <td class="px-4 py-3 text-sm text-gray-900">$308.2B</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-yellow-600 font-bold text-sm">₿</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Binance Coin</div>
                                        <div class="text-sm text-gray-500">BNB</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-900">$312.45</td>
                            <td class="px-4 py-3 text-sm text-green-600">+0.8%</td>
                            <td class="px-4 py-3 text-sm text-gray-900">$48.7B</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

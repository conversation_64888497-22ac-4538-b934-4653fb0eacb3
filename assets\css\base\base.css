/**
 * Estilos comunes para todas las páginas del SPA
 */

/* Estilos para el contenedor principal */
.main-content-inner {
    padding: 1.5rem;
    width: 100%;
    max-width: 100%;
}

/* Cabeceras de página */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 0.5rem;
}

/* Tarjetas */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-weight: 600;
    font-size: 1rem;
    color: #374151;
    margin: 0;
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
}

/* Gráficos */
.chart-container {
    height: 300px;
    position: relative;
}

/* Tablas */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
}

.data-table tbody tr:hover {
    background-color: #f9fafb;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Botones */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: background-color 0.2s, border-color 0.2s, color 0.2s;
    cursor: pointer;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #2563eb;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #1d4ed8;
}

.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-icon {
    padding: 0.375rem;
}

/* Indicador de carga */
.loader {
    border: 3px solid rgba(229, 231, 235, 0.3);
    border-radius: 50%;
    border-top: 3px solid #3b82f6;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Campos de formulario */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
}

.form-control:focus {
    outline: none;
    border-color: #93c5fd;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* Elementos para calendario */
.calendar-container {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

.calendar-header {
    background-color: #f9fafb;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    border-top: 1px solid #e5e7eb;
    border-left: 1px solid #e5e7eb;
}

.calendar-day {
    height: 6rem;
    padding: 0.5rem;
    border-right: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
    overflow-y: auto;
}

.calendar-day-header {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-align: right;
    margin-bottom: 0.5rem;
}

.calendar-event {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsividad */
@media (max-width: 768px) {
    .main-content-inner {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
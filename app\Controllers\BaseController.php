<?php
/**
 * Base Controller for SPA Backend
 * 
 * Provides common functionality for all controllers including
 * request handling, validation, response formatting, and utilities.
 * 
 * <AUTHOR> with Claude Code
 * @version 2.0
 */

namespace App\Controllers;

abstract class BaseController
{
    /**
     * @var array Request data
     */
    protected array $request = [];
    
    /**
     * @var array Validation errors
     */
    protected array $errors = [];
    
    /**
     * @var int Default items per page for pagination
     */
    protected int $perPage = 20;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->parseRequest();
        $this->setHeaders();
    }
    
    /**
     * Parse incoming request data
     * 
     * @return void
     */
    protected function parseRequest(): void
    {
        // Get request method
        $method = $_SERVER['REQUEST_METHOD'];
        
        // Parse JSON body for non-GET requests
        if ($method !== 'GET') {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            
            if (strpos($contentType, 'application/json') !== false) {
                $json = file_get_contents('php://input');
                $data = json_decode($json, true) ?? [];
                $this->request = array_merge($data, $_REQUEST);
            } else {
                $this->request = $_REQUEST;
            }
        } else {
            $this->request = $_GET;
        }
        
        // Add uploaded files if any
        if (!empty($_FILES)) {
            $this->request['files'] = $_FILES;
        }
    }
    
    /**
     * Set default response headers
     * 
     * @return void
     */
    protected function setHeaders(): void
    {
        header('Content-Type: application/json; charset=UTF-8');
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
    }
    
    /**
     * Send JSON response
     * 
     * @param mixed $data
     * @param int $statusCode
     * @param array $headers
     * @return void
     */
    protected function json($data, int $statusCode = 200, array $headers = []): void
    {
        http_response_code($statusCode);
        
        foreach ($headers as $key => $value) {
            header("{$key}: {$value}");
        }
        
        echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Send success response
     * 
     * @param mixed $data
     * @param string $message
     * @param int $statusCode
     * @return void
     */
    protected function success($data = null, string $message = 'Success', int $statusCode = 200): void
    {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->json($response, $statusCode);
    }
    
    /**
     * Send error response
     * 
     * @param string $message
     * @param int $statusCode
     * @param array $errors
     * @return void
     */
    protected function error(string $message, int $statusCode = 400, array $errors = []): void
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if (!empty($errors)) {
            $response['errors'] = $errors;
        }
        
        $this->json($response, $statusCode);
    }
    
    /**
     * Send paginated response
     * 
     * @param array $items
     * @param int $total
     * @param int $page
     * @param int $perPage
     * @return void
     */
    protected function paginated(array $items, int $total, int $page, int $perPage): void
    {
        $totalPages = ceil($total / $perPage);
        
        $this->success([
            'items' => $items,
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => $totalPages,
                'from' => ($page - 1) * $perPage + 1,
                'to' => min($page * $perPage, $total)
            ]
        ]);
    }
    
    /**
     * Validate request data
     * 
     * @param array $rules
     * @return bool
     */
    protected function validate(array $rules): bool
    {
        $this->errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $this->request[$field] ?? null;
            $fieldRules = explode('|', $rule);
            
            foreach ($fieldRules as $fieldRule) {
                $ruleParts = explode(':', $fieldRule);
                $ruleName = $ruleParts[0];
                $ruleValue = $ruleParts[1] ?? null;
                
                if (!$this->validateRule($field, $value, $ruleName, $ruleValue)) {
                    break;
                }
            }
        }
        
        return empty($this->errors);
    }
    
    /**
     * Validate a single rule
     * 
     * @param string $field
     * @param mixed $value
     * @param string $rule
     * @param mixed $ruleValue
     * @return bool
     */
    protected function validateRule(string $field, $value, string $rule, $ruleValue = null): bool
    {
        switch ($rule) {
            case 'required':
                if (empty($value) && $value !== '0' && $value !== 0) {
                    $this->errors[$field][] = "The {$field} field is required.";
                    return false;
                }
                break;
                
            case 'email':
                if ($value && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->errors[$field][] = "The {$field} field must be a valid email address.";
                    return false;
                }
                break;
                
            case 'numeric':
                if ($value && !is_numeric($value)) {
                    $this->errors[$field][] = "The {$field} field must be numeric.";
                    return false;
                }
                break;
                
            case 'min':
                if ($value && strlen($value) < $ruleValue) {
                    $this->errors[$field][] = "The {$field} field must be at least {$ruleValue} characters.";
                    return false;
                }
                break;
                
            case 'max':
                if ($value && strlen($value) > $ruleValue) {
                    $this->errors[$field][] = "The {$field} field must not exceed {$ruleValue} characters.";
                    return false;
                }
                break;
                
            case 'in':
                $allowedValues = explode(',', $ruleValue);
                if ($value && !in_array($value, $allowedValues)) {
                    $this->errors[$field][] = "The {$field} field must be one of: " . implode(', ', $allowedValues);
                    return false;
                }
                break;
                
            case 'date':
                if ($value && !strtotime($value)) {
                    $this->errors[$field][] = "The {$field} field must be a valid date.";
                    return false;
                }
                break;
                
            case 'url':
                if ($value && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $this->errors[$field][] = "The {$field} field must be a valid URL.";
                    return false;
                }
                break;
                
            case 'boolean':
                if ($value !== null && !in_array($value, [true, false, 1, 0, '1', '0', 'true', 'false'], true)) {
                    $this->errors[$field][] = "The {$field} field must be true or false.";
                    return false;
                }
                break;
                
            case 'array':
                if ($value && !is_array($value)) {
                    $this->errors[$field][] = "The {$field} field must be an array.";
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Get validation errors
     * 
     * @return array
     */
    protected function getErrors(): array
    {
        return $this->errors;
    }
    
    /**
     * Get request parameter
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function input(string $key, $default = null)
    {
        return $this->request[$key] ?? $default;
    }
    
    /**
     * Get all request parameters
     * 
     * @return array
     */
    protected function all(): array
    {
        return $this->request;
    }
    
    /**
     * Get only specified request parameters
     * 
     * @param array $keys
     * @return array
     */
    protected function only(array $keys): array
    {
        return array_intersect_key($this->request, array_flip($keys));
    }
    
    /**
     * Get all except specified request parameters
     * 
     * @param array $keys
     * @return array
     */
    protected function except(array $keys): array
    {
        return array_diff_key($this->request, array_flip($keys));
    }
    
    /**
     * Check if request has a parameter
     * 
     * @param string $key
     * @return bool
     */
    protected function has(string $key): bool
    {
        return isset($this->request[$key]);
    }
    
    /**
     * Get pagination parameters
     * 
     * @return array
     */
    protected function getPaginationParams(): array
    {
        $page = max(1, (int) $this->input('page', 1));
        $perPage = min(100, max(1, (int) $this->input('per_page', $this->perPage)));
        $offset = ($page - 1) * $perPage;
        
        return [
            'page' => $page,
            'per_page' => $perPage,
            'offset' => $offset
        ];
    }
    
    /**
     * Get sorting parameters
     * 
     * @param array $allowedFields
     * @param string $defaultField
     * @param string $defaultDirection
     * @return array
     */
    protected function getSortingParams(array $allowedFields, string $defaultField = 'id', string $defaultDirection = 'asc'): array
    {
        $sortBy = $this->input('sort_by', $defaultField);
        $sortDirection = strtolower($this->input('sort_direction', $defaultDirection));
        
        // Validate sort field
        if (!in_array($sortBy, $allowedFields)) {
            $sortBy = $defaultField;
        }
        
        // Validate sort direction
        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = $defaultDirection;
        }
        
        return [
            'sort_by' => $sortBy,
            'sort_direction' => $sortDirection
        ];
    }
    
    /**
     * Get filtering parameters
     * 
     * @param array $allowedFilters
     * @return array
     */
    protected function getFilterParams(array $allowedFilters): array
    {
        $filters = [];
        
        foreach ($allowedFilters as $filter) {
            if ($this->has($filter)) {
                $filters[$filter] = $this->input($filter);
            }
        }
        
        return $filters;
    }
    
    /**
     * Authorize request
     * 
     * @param string $ability
     * @param mixed $resource
     * @return bool
     */
    protected function authorize(string $ability, $resource = null): bool
    {
        // Implement your authorization logic here
        // This is a placeholder that always returns true
        return true;
    }
    
    /**
     * Check if request is AJAX
     * 
     * @return bool
     */
    protected function isAjax(): bool
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get client IP address
     * 
     * @return string
     */
    protected function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                return trim($ip);
            }
        }
        
        return '0.0.0.0';
    }
    
    /**
     * Log activity
     * 
     * @param string $action
     * @param array $data
     * @return void
     */
    protected function logActivity(string $action, array $data = []): void
    {
        // Implement your activity logging here
        // This is a placeholder
        $log = [
            'action' => $action,
            'data' => $data,
            'ip' => $this->getClientIp(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log to file, database, or external service
        error_log(json_encode($log));
    }
    
    /**
     * Handle file upload
     * 
     * @param string $field
     * @param string $directory
     * @param array $allowedTypes
     * @param int $maxSize
     * @return string|null
     */
    protected function uploadFile(string $field, string $directory, array $allowedTypes = [], int $maxSize = 10485760): ?string
    {
        if (!isset($_FILES[$field]) || $_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
            return null;
        }
        
        $file = $_FILES[$field];
        
        // Validate file size
        if ($file['size'] > $maxSize) {
            $this->errors[$field][] = "File size exceeds maximum allowed size.";
            return null;
        }
        
        // Validate file type
        if (!empty($allowedTypes)) {
            $fileType = mime_content_type($file['tmp_name']);
            if (!in_array($fileType, $allowedTypes)) {
                $this->errors[$field][] = "File type not allowed.";
                return null;
            }
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = rtrim($directory, '/') . '/' . $filename;
        
        // Create directory if it doesn't exist
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return $filepath;
        }
        
        return null;
    }
}
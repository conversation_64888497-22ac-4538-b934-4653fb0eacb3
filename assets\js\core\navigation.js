/**
 * Navigation System for SPA
 * 
 * Handles all client-side navigation, state management,
 * and coordination with the backend API
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

(function() {
    'use strict';
    
    // Navigation configuration
    const config = {
        apiEndpoint: '/public/api/navigation.php',
        contentSelector: '#main-content',
        sidebarSelector: '.fuse-sidebar',
        loadingClass: 'loading',
        transitionDuration: 300,
        cacheEnabled: true,
        cacheExpiry: 300000, // 5 minutes
        preloadEnabled: true,
        debug: false
    };
    
    // Navigation state
    const state = {
        currentPage: null,
        previousPage: null,
        history: [],
        isNavigating: false,
        cache: new Map(),
        preloadQueue: [],
        pendingRequests: new Map()
    };
    
    // Event emitter for navigation events
    const events = {
        listeners: new Map(),
        
        on(event, callback) {
            if (!this.listeners.has(event)) {
                this.listeners.set(event, []);
            }
            this.listeners.get(event).push(callback);
        },
        
        off(event, callback) {
            if (this.listeners.has(event)) {
                const callbacks = this.listeners.get(event);
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
            }
        },
        
        emit(event, data) {
            if (this.listeners.has(event)) {
                this.listeners.get(event).forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error('Event listener error:', error);
                    }
                });
            }
        }
    };
    
    /**
     * Initialize navigation system
     * 
     * @param {Object} options Configuration options
     */
    function initNavigation(options = {}) {
        // Merge options with config
        Object.assign(config, options);
        
        // Set initial state
        state.currentPage = options.initialPage || getCurrentPageFromURL();
        
        // Log initialization
        log('Navigation initialized', { config, state });
        
        // Setup event listeners
        setupEventListeners();
        
        // Setup browser history
        setupBrowserHistory();
        
        // Preload pages if enabled
        if (config.preloadEnabled) {
            setTimeout(preloadPages, 1000);
        }
        
        // Export API
        window.navigation = {
            navigate: navigateToPage,
            getCurrentPage: () => state.currentPage,
            getHistory: () => [...state.history],
            on: events.on.bind(events),
            off: events.off.bind(events),
            refresh: refreshCurrentPage,
            clearCache: clearNavigationCache
        };
        
        // Emit ready event
        events.emit('ready', { currentPage: state.currentPage });
    }
    
    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Handle popstate for browser back/forward
        window.addEventListener('popstate', handlePopState);
        
        // Handle link clicks
        document.addEventListener('click', handleLinkClick);
        
        // Handle sidebar navigation events
        window.addEventListener('fuse-sidebar-navigation', handleSidebarNavigation);
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', handleKeyboardShortcuts);
    }
    
    /**
     * Setup browser history
     */
    function setupBrowserHistory() {
        // Replace current state with proper data
        const url = new URL(window.location);
        url.searchParams.set('page', state.currentPage);
        
        window.history.replaceState(
            { page: state.currentPage },
            '',
            url.toString()
        );
    }
    
    /**
     * Handle browser back/forward
     * 
     * @param {PopStateEvent} event
     */
    function handlePopState(event) {
        if (event.state && event.state.page) {
            navigateToPage(event.state.page, { pushState: false });
        }
    }
    
    /**
     * Handle link clicks
     * 
     * @param {MouseEvent} event
     */
    function handleLinkClick(event) {
        const link = event.target.closest('a[data-spa-link], a[href^="?page="]');
        if (!link) return;
        
        event.preventDefault();
        
        const page = link.dataset.page || 
                    link.getAttribute('href').match(/page=([^&]+)/)?.[1];
                    
        if (page) {
            navigateToPage(page);
        }
    }
    
    /**
     * Handle sidebar navigation events
     * 
     * @param {CustomEvent} event
     */
    function handleSidebarNavigation(event) {
        const page = event.detail.itemId;
        if (page && page !== state.currentPage) {
            navigateToPage(page);
        }
    }
    
    /**
     * Handle keyboard shortcuts
     * 
     * @param {KeyboardEvent} event
     */
    function handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K for quick navigation
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            showQuickNavigation();
        }
    }
    
    /**
     * Navigate to page
     * 
     * @param {string} page Page identifier
     * @param {Object} options Navigation options
     */
    async function navigateToPage(page, options = {}) {
        // Prevent duplicate navigation
        if (state.isNavigating || page === state.currentPage) {
            return;
        }
        
        // Set navigating flag
        state.isNavigating = true;
        
        // Emit navigation start event
        events.emit('navigationStart', { from: state.currentPage, to: page });
        
        try {
            // Check cache first
            let pageData = null;
            
            if (config.cacheEnabled && state.cache.has(page)) {
                const cached = state.cache.get(page);
                if (Date.now() - cached.timestamp < config.cacheExpiry) {
                    pageData = cached.data;
                    log('Using cached page data', { page });
                }
            }
            
            // Fetch from server if not cached
            if (!pageData) {
                pageData = await fetchPage(page);
                
                // Cache if enabled
                if (config.cacheEnabled && pageData.cache !== false) {
                    state.cache.set(page, {
                        data: pageData,
                        timestamp: Date.now()
                    });
                }
            }
            
            // Update page content
            await updatePageContent(pageData);
            
            // Update state
            state.previousPage = state.currentPage;
            state.currentPage = page;
            state.history.push({
                page,
                timestamp: Date.now()
            });
            
            // Update browser history
            if (options.pushState !== false) {
                const url = new URL(window.location);
                url.searchParams.set('page', page);
                window.history.pushState(
                    { page },
                    pageData.title,
                    url.toString()
                );
            }
            
            // Update document title
            document.title = pageData.title + ' - FUSE React PHP SPA';
            
            // Update sidebar active state
            updateSidebarActiveState(page);
            
            // Load page-specific resources
            await loadPageResources(pageData);
            
            // Initialize page components
            initializePageComponents();
            
            // Emit navigation complete event
            events.emit('navigationComplete', {
                page,
                data: pageData,
                from: state.previousPage
            });
            
        } catch (error) {
            console.error('Navigation error:', error);
            
            // Emit navigation error event
            events.emit('navigationError', {
                page,
                error,
                from: state.currentPage
            });
            
            // Show error message
            showNavigationError(error.message || 'Failed to load page');
            
        } finally {
            // Clear navigating flag
            state.isNavigating = false;
        }
    }
    
    /**
     * Fetch page from server
     * 
     * @param {string} page Page identifier
     * @returns {Promise<Object>} Page data
     */
    async function fetchPage(page) {
        // Check if request is already pending
        if (state.pendingRequests.has(page)) {
            return state.pendingRequests.get(page);
        }
        
        // Create request promise
        const requestPromise = fetch(`${config.apiEndpoint}?action=navigate&page=${page}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data.success) {
                throw new Error(data.error || 'Failed to load page');
            }
            return data;
        })
        .finally(() => {
            // Remove from pending requests
            state.pendingRequests.delete(page);
        });
        
        // Store in pending requests
        state.pendingRequests.set(page, requestPromise);
        
        return requestPromise;
    }
    
    /**
     * Update page content with transition
     * 
     * @param {Object} pageData Page data from server
     */
    async function updatePageContent(pageData) {
        const container = document.querySelector(config.contentSelector);
        if (!container) return;
        
        // Add loading class
        container.classList.add(config.loadingClass);
        
        // Fade out current content
        await fadeOut(container);
        
        // Update content
        const contentWrapper = container.querySelector('.main-content-inner') || container;
        contentWrapper.innerHTML = pageData.content;
        
        // Fade in new content
        await fadeIn(container);
        
        // Remove loading class
        container.classList.remove(config.loadingClass);
    }
    
    /**
     * Fade out element
     * 
     * @param {HTMLElement} element
     */
    function fadeOut(element) {
        return new Promise(resolve => {
            element.style.opacity = '0';
            element.style.transition = `opacity ${config.transitionDuration}ms ease-out`;
            
            setTimeout(resolve, config.transitionDuration);
        });
    }
    
    /**
     * Fade in element
     * 
     * @param {HTMLElement} element
     */
    function fadeIn(element) {
        return new Promise(resolve => {
            element.style.opacity = '1';
            element.style.transition = `opacity ${config.transitionDuration}ms ease-in`;
            
            setTimeout(resolve, config.transitionDuration);
        });
    }
    
    /**
     * Update sidebar active state
     * 
     * @param {string} page Active page
     */
    function updateSidebarActiveState(page) {
        // Update sidebar instance if available
        if (window.fuseSidebarInstance && typeof window.fuseSidebarInstance.setActiveItem === 'function') {
            window.fuseSidebarInstance.setActiveItem(page);
        }
        
        // Manual update as fallback
        const sidebarItems = document.querySelectorAll('.fuse-sidebar-item');
        sidebarItems.forEach(item => {
            const itemId = item.dataset.itemId;
            if (itemId === page) {
                item.classList.add('bg-sky-50', 'text-sky-600', 'shadow-sm');
                item.classList.remove('text-gray-600', 'hover:bg-gray-50');
            } else {
                item.classList.remove('bg-sky-50', 'text-sky-600', 'shadow-sm');
                item.classList.add('text-gray-600', 'hover:bg-gray-50');
            }
        });
    }
    
    /**
     * Load page-specific resources
     * 
     * @param {Object} pageData Page data
     */
    async function loadPageResources(pageData) {
        const promises = [];
        
        // Load scripts
        if (pageData.scripts && pageData.scripts.length > 0) {
            pageData.scripts.forEach(script => {
                if (!document.querySelector(`script[src="${script}"]`)) {
                    promises.push(loadScript(script));
                }
            });
        }
        
        // Load styles
        if (pageData.styles && pageData.styles.length > 0) {
            pageData.styles.forEach(style => {
                if (!document.querySelector(`link[href="${style}"]`)) {
                    promises.push(loadStyle(style));
                }
            });
        }
        
        // Wait for all resources to load
        if (promises.length > 0) {
            await Promise.all(promises);
        }
    }
    
    /**
     * Load script dynamically
     * 
     * @param {string} src Script URL
     * @returns {Promise}
     */
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            script.onload = resolve;
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            document.head.appendChild(script);
        });
    }
    
    /**
     * Load stylesheet dynamically
     * 
     * @param {string} href Stylesheet URL
     * @returns {Promise}
     */
    function loadStyle(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = resolve;
            link.onerror = () => reject(new Error(`Failed to load stylesheet: ${href}`));
            document.head.appendChild(link);
        });
    }
    
    /**
     * Initialize page components
     */
    function initializePageComponents() {
        // Re-initialize any page-specific components
        if (typeof initPageComponents === 'function') {
            initPageComponents();
        }
        
        // Emit page ready event
        events.emit('pageReady', { page: state.currentPage });
    }
    
    /**
     * Preload pages for faster navigation
     */
    async function preloadPages() {
        try {
            const response = await fetch(`${config.apiEndpoint}?action=preload`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) return;
            
            const data = await response.json();
            if (data.success && data.pages) {
                // Add pages to preload queue
                state.preloadQueue = Object.keys(data.pages);
                
                // Start preloading
                preloadNextPage();
            }
        } catch (error) {
            log('Preload error:', error);
        }
    }
    
    /**
     * Preload next page in queue
     */
    function preloadNextPage() {
        if (state.preloadQueue.length === 0) return;
        
        const page = state.preloadQueue.shift();
        
        // Skip if already cached or current page
        if (state.cache.has(page) || page === state.currentPage) {
            preloadNextPage();
            return;
        }
        
        // Fetch page in background
        fetchPage(page)
            .then(pageData => {
                if (config.cacheEnabled && pageData.cache !== false) {
                    state.cache.set(page, {
                        data: pageData,
                        timestamp: Date.now()
                    });
                }
                log('Preloaded page:', page);
            })
            .catch(error => {
                log('Preload error for page:', page, error);
            })
            .finally(() => {
                // Continue with next page after delay
                setTimeout(preloadNextPage, 500);
            });
    }
    
    /**
     * Show quick navigation dialog
     */
    function showQuickNavigation() {
        // TODO: Implement quick navigation UI
        console.log('Quick navigation not yet implemented');
    }
    
    /**
     * Show navigation error
     * 
     * @param {string} message Error message
     */
    function showNavigationError(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert('Navigation Error: ' + message);
        }
    }
    
    /**
     * Refresh current page
     */
    function refreshCurrentPage() {
        // Clear cache for current page
        state.cache.delete(state.currentPage);
        
        // Navigate to current page (will fetch fresh data)
        navigateToPage(state.currentPage, { pushState: false });
    }
    
    /**
     * Clear navigation cache
     */
    function clearNavigationCache() {
        state.cache.clear();
        log('Navigation cache cleared');
    }
    
    /**
     * Get current page from URL
     * 
     * @returns {string} Current page
     */
    function getCurrentPageFromURL() {
        const params = new URLSearchParams(window.location.search);
        return params.get('page') || 'project';
    }
    
    /**
     * Log debug message
     * 
     * @param {...any} args
     */
    function log(...args) {
        if (config.debug) {
            console.log('[Navigation]', ...args);
        }
    }
    
    // Export initialization function
    window.initNavigation = initNavigation;
    
    // Also make navigation handler available immediately
    window.handleNavigation = function(page) {
        if (window.navigation && window.navigation.navigate) {
            window.navigation.navigate(page);
        } else {
            // Queue navigation until system is ready
            setTimeout(() => handleNavigation(page), 100);
        }
    };
    
})();
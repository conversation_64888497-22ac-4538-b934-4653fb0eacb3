<!-- Finance Dashboard -->
<div class="page-content">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h1 class="page-title">Finance Dashboard</h1>
        <div class="page-actions">
            <button class="btn btn-secondary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export Report
            </button>
            <button class="btn btn-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Transaction
            </button>
        </div>
    </div>

    <!-- Financial Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Total Revenue</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-50 text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$124,563</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        8.2%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">vs last month</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Total Expenses</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-red-50 text-red-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7H4m0 0l8 8m-8-8l8-8m8 8v10a2 2 0 01-2 2H6a2 2 0 01-2-2V7z" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$87,421</h2>
                    <span class="ml-2 text-sm font-medium text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        3.1%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">vs last month</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">Net Profit</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-50 text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">$37,142</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        15.3%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">vs last month</p>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">ROI</h3>
                    <span class="flex items-center justify-center w-8 h-8 rounded-full bg-purple-50 text-purple-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </span>
                </div>
                <div class="flex items-baseline">
                    <h2 class="text-2xl font-bold text-gray-900">42.5%</h2>
                    <span class="ml-2 text-sm font-medium text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        2.1%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">vs last month</p>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="card">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue vs Expenses</h3>
                <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="text-gray-400 mb-2">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <p class="text-sm text-gray-500">Revenue vs Expenses Chart</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cash Flow</h3>
                <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="text-gray-400 mb-2">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <p class="text-sm text-gray-500">Cash Flow Chart</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="card">
        <div class="p-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Transactions</h3>
                <button class="text-sm text-blue-600 hover:text-blue-700">View All</button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr>
                            <td class="px-4 py-3 text-sm text-gray-900">Office Supplies</td>
                            <td class="px-4 py-3 text-sm text-gray-500">Office</td>
                            <td class="px-4 py-3 text-sm text-red-600">-$245.00</td>
                            <td class="px-4 py-3 text-sm text-gray-500">2024-01-15</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 text-sm text-gray-900">Client Payment</td>
                            <td class="px-4 py-3 text-sm text-gray-500">Revenue</td>
                            <td class="px-4 py-3 text-sm text-green-600">+$5,200.00</td>
                            <td class="px-4 py-3 text-sm text-gray-500">2024-01-14</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 text-sm text-gray-900">Software License</td>
                            <td class="px-4 py-3 text-sm text-gray-500">Technology</td>
                            <td class="px-4 py-3 text-sm text-red-600">-$899.00</td>
                            <td class="px-4 py-3 text-sm text-gray-500">2024-01-13</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

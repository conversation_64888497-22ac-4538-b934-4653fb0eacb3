/**
 * Navigation System Styles
 * 
 * Styles for SPA navigation transitions and states
 * 
 * <AUTHOR> with <PERSON>
 * @version 1.0
 */

/* Loading states */
#main-content {
    position: relative;
    transition: opacity 0.3s ease;
}

#main-content.loading {
    opacity: 0.5;
    pointer-events: none;
}

#main-content.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 100;
}

#main-content.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0ea5e9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 101;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Page transitions */
.page-transition-enter {
    opacity: 0;
    transform: translateY(10px);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-transition-exit {
    opacity: 1;
    transform: translateY(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Navigation links */
a[data-spa-link] {
    cursor: pointer;
    transition: color 0.2s ease;
}

a[data-spa-link]:hover {
    color: #0ea5e9;
}

/* Sidebar active state transitions */
.fuse-sidebar-item {
    transition: all 0.2s ease;
}

.fuse-sidebar-item.transitioning {
    background-color: rgba(14, 165, 233, 0.1);
}

/* Navigation error state */
.navigation-error {
    padding: 2rem;
    text-align: center;
    color: #ef4444;
}

.navigation-error h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.navigation-error p {
    color: #6b7280;
}

.navigation-error button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #0ea5e9;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.navigation-error button:hover {
    background-color: #0284c7;
}

/* Quick navigation modal */
.quick-navigation-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

.quick-navigation-modal.active {
    opacity: 1;
    pointer-events: auto;
}

.quick-navigation-content {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 90%;
    max-width: 600px;
    max-height: 70vh;
    overflow: hidden;
    transform: scale(0.95);
    transition: transform 0.2s ease;
}

.quick-navigation-modal.active .quick-navigation-content {
    transform: scale(1);
}

.quick-navigation-search {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.quick-navigation-search input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.2s ease;
}

.quick-navigation-search input:focus {
    border-color: #0ea5e9;
}

.quick-navigation-results {
    max-height: calc(70vh - 5rem);
    overflow-y: auto;
}

.quick-navigation-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.quick-navigation-item:hover {
    background-color: #f9fafb;
}

.quick-navigation-item.active {
    background-color: #f3f4f6;
}

.quick-navigation-item-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #6b7280;
}

.quick-navigation-item-text {
    flex: 1;
}

.quick-navigation-item-title {
    font-weight: 500;
    color: #111827;
}

.quick-navigation-item-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Preload indicator */
.preload-indicator {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    opacity: 0;
    transform: translateY(1rem);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.preload-indicator.active {
    opacity: 1;
    transform: translateY(0);
}

.preload-indicator-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid #e5e7eb;
    border-top-color: #0ea5e9;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* History indicator */
.history-indicator {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    opacity: 0;
    transform: translateX(-1rem);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.history-indicator.active {
    opacity: 1;
    transform: translateX(0);
}

.history-indicator button {
    padding: 0.25rem 0.5rem;
    background: none;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: #6b7280;
}

.history-indicator button:hover:not(:disabled) {
    background-color: #f3f4f6;
}

.history-indicator button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Mobile navigation */
@media (max-width: 768px) {
    .quick-navigation-content {
        width: 95%;
        max-height: 80vh;
    }
    
    .preload-indicator,
    .history-indicator {
        display: none;
    }
}
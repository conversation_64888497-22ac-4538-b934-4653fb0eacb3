<?php
/**
 * Navigation Manager for SPA System
 * 
 * Handles all navigation logic, state management, and coordination
 * between frontend and backend for SPA-like behavior.
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

namespace Core;

class NavigationManager
{
    /**
     * @var array Valid pages configuration
     */
    private array $pages = [
        'project' => [
            'title' => 'Project Dashboard',
            'view' => 'project.php',
            'scripts' => ['project.js'],
            'styles' => ['project.css'],
            'preload' => true,
            'cache' => true
        ],
        'analytics' => [
            'title' => 'Analytics Dashboard',
            'view' => 'analytics.php',
            'scripts' => ['analytics.js', 'charts.js'],
            'styles' => ['analytics.css'],
            'preload' => false,
            'cache' => true
        ],
        'finance' => [
            'title' => 'Finance Dashboard',
            'view' => 'finance.php',
            'scripts' => ['finance.js'],
            'styles' => ['finance.css'],
            'preload' => false,
            'cache' => true
        ],
        'crypto' => [
            'title' => 'Crypto Dashboard',
            'view' => 'crypto.php',
            'scripts' => ['crypto.js'],
            'styles' => ['crypto.css'],
            'preload' => false,
            'cache' => false
        ],
        'ai-image' => [
            'title' => 'AI Image Generator',
            'view' => 'ai-image.php',
            'scripts' => ['ai-image.js'],
            'styles' => ['ai-image.css'],
            'preload' => false,
            'cache' => false
        ],
        'academy' => [
            'title' => 'Academy',
            'view' => 'academy.php',
            'scripts' => ['academy.js'],
            'styles' => ['academy.css'],
            'preload' => false,
            'cache' => true
        ],
        'calendar' => [
            'title' => 'Calendar',
            'view' => 'calendar.php',
            'scripts' => ['calendar.js', 'fullcalendar.js'],
            'styles' => ['calendar.css'],
            'preload' => false,
            'cache' => true
        ],
        'messenger' => [
            'title' => 'Messenger',
            'view' => 'messenger.php',
            'scripts' => ['messenger.js', 'websocket.js'],
            'styles' => ['messenger.css'],
            'preload' => false,
            'cache' => false
        ],
        'contacts' => [
            'title' => 'Contacts',
            'view' => 'contacts.php',
            'scripts' => ['contacts.js'],
            'styles' => ['contacts.css'],
            'preload' => false,
            'cache' => true
        ],
        'ecommerce' => [
            'title' => 'E-Commerce',
            'view' => 'ecommerce.php',
            'scripts' => ['ecommerce.js', 'cart.js'],
            'styles' => ['ecommerce.css'],
            'preload' => false,
            'cache' => true
        ],
        'file-manager' => [
            'title' => 'File Manager',
            'view' => 'file-manager.php',
            'scripts' => ['file-manager.js'],
            'styles' => ['file-manager.css'],
            'preload' => false,
            'cache' => false
        ],
        'help-center' => [
            'title' => 'Help Center',
            'view' => 'help-center.php',
            'scripts' => ['help-center.js'],
            'styles' => ['help-center.css'],
            'preload' => false,
            'cache' => true
        ]
    ];
    
    /**
     * @var string Current page
     */
    private string $currentPage;
    
    /**
     * @var array Navigation state
     */
    private array $state = [];
    
    /**
     * @var bool Is AJAX request
     */
    private bool $isAjax;
    
    /**
     * Constructor
     * 
     * @param string $page Initial page
     * @param array $options Configuration options
     */
    public function __construct(string $page = 'project', array $options = [])
    {
        $this->currentPage = $this->validatePage($page);
        $this->isAjax = $this->detectAjaxRequest();
        $this->initializeState($options);
    }
    
    /**
     * Validate if page exists
     * 
     * @param string $page Page name
     * @return string Valid page name
     */
    private function validatePage(string $page): string
    {
        return isset($this->pages[$page]) ? $page : 'project';
    }
    
    /**
     * Detect if request is AJAX
     * 
     * @return bool
     */
    private function detectAjaxRequest(): bool
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Initialize navigation state
     * 
     * @param array $options Configuration options
     */
    private function initializeState(array $options): void
    {
        $this->state = [
            'currentPage' => $this->currentPage,
            'previousPage' => $_SESSION['previous_page'] ?? null,
            'history' => $_SESSION['navigation_history'] ?? [],
            'timestamp' => time(),
            'user' => $_SESSION['user'] ?? null,
            'options' => $options
        ];
        
        // Update session
        $_SESSION['current_page'] = $this->currentPage;
        $_SESSION['previous_page'] = $this->state['previousPage'];
        
        // Update history
        $this->updateHistory();
    }
    
    /**
     * Update navigation history
     */
    private function updateHistory(): void
    {
        $history = $this->state['history'];
        
        // Add current page to history
        $history[] = [
            'page' => $this->currentPage,
            'timestamp' => time(),
            'title' => $this->getPageTitle()
        ];
        
        // Keep only last 10 entries
        if (count($history) > 10) {
            $history = array_slice($history, -10);
        }
        
        $this->state['history'] = $history;
        $_SESSION['navigation_history'] = $history;
    }
    
    /**
     * Get current page configuration
     * 
     * @return array Page configuration
     */
    public function getCurrentPageConfig(): array
    {
        return $this->pages[$this->currentPage] ?? [];
    }
    
    /**
     * Get page title
     * 
     * @param string|null $page Page name (current if null)
     * @return string Page title
     */
    public function getPageTitle(?string $page = null): string
    {
        $page = $page ?? $this->currentPage;
        return $this->pages[$page]['title'] ?? 'Dashboard';
    }
    
    /**
     * Get page view path
     * 
     * @param string|null $page Page name (current if null)
     * @return string View file path
     */
    public function getViewPath(?string $page = null): string
    {
        $page = $page ?? $this->currentPage;
        $view = $this->pages[$page]['view'] ?? 'project.php';
        
        // Check multiple possible locations
        $locations = [
            __DIR__ . '/../' . $view,
            __DIR__ . '/../views/' . $view,
            __DIR__ . '/../public/views/' . $view
        ];
        
        foreach ($locations as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }
        
        // Default fallback
        return __DIR__ . '/../' . $view;
    }
    
    /**
     * Get required scripts for page
     * 
     * @param string|null $page Page name (current if null)
     * @return array Script paths
     */
    public function getPageScripts(?string $page = null): array
    {
        $page = $page ?? $this->currentPage;
        $scripts = $this->pages[$page]['scripts'] ?? [];
        
        // Add full paths
        return array_map(function($script) {
            return 'assets/js/pages/' . $script;
        }, $scripts);
    }
    
    /**
     * Get required styles for page
     * 
     * @param string|null $page Page name (current if null)
     * @return array Style paths
     */
    public function getPageStyles(?string $page = null): array
    {
        $page = $page ?? $this->currentPage;
        $styles = $this->pages[$page]['styles'] ?? [];
        
        // Add full paths
        return array_map(function($style) {
            return 'assets/css/pages/' . $style;
        }, $styles);
    }
    
    /**
     * Handle page navigation
     * 
     * @param string $page Target page
     * @return array Navigation response
     */
    public function navigate(string $page): array
    {
        $page = $this->validatePage($page);
        
        // Update state
        $this->state['previousPage'] = $this->currentPage;
        $this->currentPage = $page;
        $this->initializeState([]);
        
        // Prepare response
        $response = [
            'success' => true,
            'page' => $page,
            'title' => $this->getPageTitle(),
            'content' => $this->renderPage(),
            'scripts' => $this->getPageScripts(),
            'styles' => $this->getPageStyles(),
            'state' => $this->getClientState(),
            'cache' => $this->pages[$page]['cache'] ?? true
        ];
        
        return $response;
    }
    
    /**
     * Render current page
     * 
     * @return string Rendered HTML
     */
    public function renderPage(): string
    {
        $viewPath = $this->getViewPath();
        
        if (!file_exists($viewPath)) {
            return $this->renderError('Page not found');
        }
        
        // Start output buffering
        ob_start();
        
        // Make navigation manager available in view
        $navigation = $this;
        
        // Include the view
        include $viewPath;
        
        // Get content
        $content = ob_get_clean();
        
        // Clean content if AJAX request
        if ($this->isAjax) {
            $content = $this->cleanAjaxContent($content);
        }
        
        return $content;
    }
    
    /**
     * Clean content for AJAX response
     * 
     * @param string $content Raw content
     * @return string Cleaned content
     */
    private function cleanAjaxContent(string $content): string
    {
        // Remove any sidebar elements
        $content = preg_replace('/<div[^>]*class="[^"]*fuse-sidebar[^"]*"[^>]*>.*?<\/div>\s*<\/div>/s', '', $content);
        $content = preg_replace('/<div[^>]*id="[^"]*sidebar[^"]*"[^>]*>.*?<\/div>/s', '', $content);
        
        // Remove header elements
        $content = preg_replace('/<header[^>]*>.*?<\/header>/s', '', $content);
        
        // Remove any navigation scripts
        $content = preg_replace('/<script[^>]*navigation[^>]*>.*?<\/script>/s', '', $content);
        
        return trim($content);
    }
    
    /**
     * Render error page
     * 
     * @param string $message Error message
     * @return string Error HTML
     */
    private function renderError(string $message): string
    {
        return <<<HTML
        <div class="flex items-center justify-center min-h-screen">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Error</h1>
                <p class="text-xl text-gray-600">{$message}</p>
            </div>
        </div>
        HTML;
    }
    
    /**
     * Get client-side state
     * 
     * @return array State for client
     */
    public function getClientState(): array
    {
        return [
            'currentPage' => $this->currentPage,
            'previousPage' => $this->state['previousPage'],
            'history' => array_slice($this->state['history'], -5), // Last 5 entries
            'timestamp' => $this->state['timestamp']
        ];
    }
    
    /**
     * Get pages that should be preloaded
     * 
     * @return array Pages to preload
     */
    public function getPreloadPages(): array
    {
        $preloadPages = [];
        
        foreach ($this->pages as $page => $config) {
            if ($config['preload'] ?? false) {
                $preloadPages[] = $page;
            }
        }
        
        return $preloadPages;
    }
    
    /**
     * Get all valid pages
     * 
     * @return array Page names
     */
    public function getValidPages(): array
    {
        return array_keys($this->pages);
    }
    
    /**
     * Check if page should be cached
     * 
     * @param string $page Page name
     * @return bool Should cache
     */
    public function shouldCachePage(string $page): bool
    {
        return $this->pages[$page]['cache'] ?? true;
    }
    
    /**
     * Get navigation menu items
     * 
     * @return array Menu structure
     */
    public function getMenuItems(): array
    {
        return [
            'dashboards' => [
                ['id' => 'project', 'label' => 'Project', 'icon' => 'layout-dashboard'],
                ['id' => 'analytics', 'label' => 'Analytics', 'icon' => 'trending-up'],
                ['id' => 'finance', 'label' => 'Finance', 'icon' => 'dollar-sign'],
                ['id' => 'crypto', 'label' => 'Crypto', 'icon' => 'bitcoin']
            ],
            'applications' => [
                ['id' => 'ai-image', 'label' => 'AI Image Generator', 'icon' => 'image', 'isNew' => true],
                ['id' => 'academy', 'label' => 'Academy', 'icon' => 'graduation-cap'],
                ['id' => 'calendar', 'label' => 'Calendar', 'icon' => 'calendar'],
                ['id' => 'messenger', 'label' => 'Messenger', 'icon' => 'message-circle'],
                ['id' => 'contacts', 'label' => 'Contacts', 'icon' => 'users'],
                ['id' => 'ecommerce', 'label' => 'E-Commerce', 'icon' => 'shopping-cart'],
                ['id' => 'file-manager', 'label' => 'File Manager', 'icon' => 'folder-open'],
                ['id' => 'help-center', 'label' => 'Help Center', 'icon' => 'help-circle']
            ]
        ];
    }
}
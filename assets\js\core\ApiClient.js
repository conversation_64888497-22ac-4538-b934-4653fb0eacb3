/**
 * ApiClient - Centralized API Communication Layer
 * 
 * Features:
 * - RESTful API methods
 * - Request/Response interceptors
 * - Token management
 * - Request caching
 * - Retry logic
 * - Progress tracking
 * - Error handling
 */

class ApiClient {
    constructor(options = {}) {
        this.baseURL = options.baseURL || '/api/v1';
        this.timeout = options.timeout || 30000;
        this.headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...options.headers
        };
        
        // Interceptors
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        
        // Cache configuration
        this.cache = new Map();
        this.cacheEnabled = options.cache !== false;
        this.cacheTTL = options.cacheTTL || 5 * 60 * 1000; // 5 minutes
        
        // Retry configuration
        this.retryEnabled = options.retry !== false;
        this.maxRetries = options.maxRetries || 3;
        this.retryDelay = options.retryDelay || 1000;
        
        // Progress tracking
        this.onUploadProgress = options.onUploadProgress;
        this.onDownloadProgress = options.onDownloadProgress;
        
        // Token management
        this.tokenKey = options.tokenKey || 'auth_token';
        this.tokenType = options.tokenType || 'Bearer';
        
        // Initialize token from storage
        this.token = this.getStoredToken();
        
        // Request queue for offline support
        this.requestQueue = [];
        this.online = navigator.onLine;
        
        // Listen for online/offline events
        window.addEventListener('online', () => this.handleOnline());
        window.addEventListener('offline', () => this.handleOffline());
    }

    /**
     * Make a request
     */
    async request(config) {
        // Apply request interceptors
        for (const interceptor of this.requestInterceptors) {
            config = await interceptor(config);
        }
        
        // Merge with defaults
        config = {
            method: 'GET',
            headers: { ...this.headers },
            ...config
        };
        
        // Add auth token if available
        if (this.token && !config.headers.Authorization) {
            config.headers.Authorization = `${this.tokenType} ${this.token}`;
        }
        
        // Build full URL
        const url = this.buildURL(config.url, config.params);
        
        // Check cache for GET requests
        if (config.method === 'GET' && this.cacheEnabled && !config.noCache) {
            const cachedResponse = this.getFromCache(url);
            if (cachedResponse) {
                return cachedResponse;
            }
        }
        
        // Create request key for deduplication
        const requestKey = `${config.method}:${url}`;
        
        try {
            // Perform request with retry logic
            const response = await this.performRequest(url, config);
            
            // Cache successful GET responses
            if (config.method === 'GET' && this.cacheEnabled && response.ok) {
                this.addToCache(url, response.data);
            }
            
            return response;
        } catch (error) {
            // Handle offline mode
            if (!this.online && config.queueOffline !== false) {
                this.queueRequest(config);
                throw new Error('Request queued for offline sync');
            }
            
            throw error;
        }
    }

    /**
     * Perform the actual request
     */
    async performRequest(url, config, retryCount = 0) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout || this.timeout);
        
        try {
            const requestConfig = {
                method: config.method,
                headers: config.headers,
                signal: controller.signal
            };
            
            // Add body for non-GET requests
            if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
                requestConfig.body = JSON.stringify(config.data);
            }
            
            // Make the request
            const response = await fetch(url, requestConfig);
            clearTimeout(timeoutId);
            
            // Parse response
            let data;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }
            
            // Create response object
            const responseObj = {
                data,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                config,
                ok: response.ok
            };
            
            // Apply response interceptors
            let finalResponse = responseObj;
            for (const interceptor of this.responseInterceptors) {
                finalResponse = await interceptor(finalResponse);
            }
            
            // Handle errors
            if (!response.ok) {
                const error = new Error(data.message || response.statusText);
                error.response = finalResponse;
                error.status = response.status;
                
                // Retry logic
                if (this.shouldRetry(error, retryCount)) {
                    await this.delay(this.retryDelay * Math.pow(2, retryCount));
                    return this.performRequest(url, config, retryCount + 1);
                }
                
                throw error;
            }
            
            return finalResponse;
        } catch (error) {
            clearTimeout(timeoutId);
            
            // Handle abort
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }
            
            // Retry logic for network errors
            if (this.shouldRetry(error, retryCount)) {
                await this.delay(this.retryDelay * Math.pow(2, retryCount));
                return this.performRequest(url, config, retryCount + 1);
            }
            
            throw error;
        }
    }

    /**
     * GET request
     */
    get(url, config = {}) {
        return this.request({ ...config, method: 'GET', url });
    }

    /**
     * POST request
     */
    post(url, data, config = {}) {
        return this.request({ ...config, method: 'POST', url, data });
    }

    /**
     * PUT request
     */
    put(url, data, config = {}) {
        return this.request({ ...config, method: 'PUT', url, data });
    }

    /**
     * PATCH request
     */
    patch(url, data, config = {}) {
        return this.request({ ...config, method: 'PATCH', url, data });
    }

    /**
     * DELETE request
     */
    delete(url, config = {}) {
        return this.request({ ...config, method: 'DELETE', url });
    }

    /**
     * HEAD request
     */
    head(url, config = {}) {
        return this.request({ ...config, method: 'HEAD', url });
    }

    /**
     * OPTIONS request
     */
    options(url, config = {}) {
        return this.request({ ...config, method: 'OPTIONS', url });
    }

    /**
     * Build URL with parameters
     */
    buildURL(url, params) {
        // Handle absolute URLs
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        
        // Build full URL
        let fullURL = this.baseURL + (url.startsWith('/') ? url : '/' + url);
        
        // Add query parameters
        if (params && Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            fullURL += (fullURL.includes('?') ? '&' : '?') + queryString;
        }
        
        return fullURL;
    }

    /**
     * Add request interceptor
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
        return () => {
            const index = this.requestInterceptors.indexOf(interceptor);
            if (index !== -1) {
                this.requestInterceptors.splice(index, 1);
            }
        };
    }

    /**
     * Add response interceptor
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
        return () => {
            const index = this.responseInterceptors.indexOf(interceptor);
            if (index !== -1) {
                this.responseInterceptors.splice(index, 1);
            }
        };
    }

    /**
     * Set authentication token
     */
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem(this.tokenKey, token);
        } else {
            localStorage.removeItem(this.tokenKey);
        }
    }

    /**
     * Get stored token
     */
    getStoredToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * Clear authentication
     */
    clearAuth() {
        this.token = null;
        localStorage.removeItem(this.tokenKey);
    }

    /**
     * Check if should retry request
     */
    shouldRetry(error, retryCount) {
        if (!this.retryEnabled || retryCount >= this.maxRetries) {
            return false;
        }
        
        // Retry on network errors or 5xx errors
        return !error.response || error.response.status >= 500;
    }

    /**
     * Delay helper
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get from cache
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        
        // Check if expired
        if (Date.now() - cached.timestamp > this.cacheTTL) {
            this.cache.delete(key);
            return null;
        }
        
        return cached.data;
    }

    /**
     * Add to cache
     */
    addToCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
        
        // Limit cache size
        if (this.cache.size > 100) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Queue request for offline sync
     */
    queueRequest(config) {
        this.requestQueue.push({
            config,
            timestamp: Date.now()
        });
        
        // Save to localStorage for persistence
        localStorage.setItem('api_request_queue', JSON.stringify(this.requestQueue));
    }

    /**
     * Handle online event
     */
    async handleOnline() {
        this.online = true;
        
        // Process queued requests
        const queue = [...this.requestQueue];
        this.requestQueue = [];
        
        for (const { config } of queue) {
            try {
                await this.request(config);
            } catch (error) {
                console.error('Failed to sync queued request:', error);
            }
        }
        
        // Clear persisted queue
        localStorage.removeItem('api_request_queue');
    }

    /**
     * Handle offline event
     */
    handleOffline() {
        this.online = false;
    }

    /**
     * Create a resource helper
     */
    resource(name, options = {}) {
        const baseURL = options.baseURL || `/${name}`;
        
        return {
            list: (params) => this.get(baseURL, { params }),
            get: (id, params) => this.get(`${baseURL}/${id}`, { params }),
            create: (data) => this.post(baseURL, data),
            update: (id, data) => this.put(`${baseURL}/${id}`, data),
            patch: (id, data) => this.patch(`${baseURL}/${id}`, data),
            delete: (id) => this.delete(`${baseURL}/${id}`),
            
            // Custom actions
            action: (id, action, config = {}) => {
                const url = id ? `${baseURL}/${id}/${action}` : `${baseURL}/${action}`;
                return this.request({ url, ...config });
            }
        };
    }

    /**
     * Upload file
     */
    async upload(url, file, options = {}) {
        const formData = new FormData();
        formData.append(options.fieldName || 'file', file);
        
        // Add additional fields
        if (options.data) {
            Object.entries(options.data).forEach(([key, value]) => {
                formData.append(key, value);
            });
        }
        
        return this.request({
            method: 'POST',
            url,
            data: formData,
            headers: {
                ...this.headers,
                'Content-Type': undefined // Let browser set it
            },
            onUploadProgress: options.onProgress || this.onUploadProgress,
            ...options
        });
    }

    /**
     * Download file
     */
    async download(url, options = {}) {
        const response = await this.request({
            method: 'GET',
            url,
            responseType: 'blob',
            onDownloadProgress: options.onProgress || this.onDownloadProgress,
            ...options
        });
        
        // Create download link
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = options.filename || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        
        return response;
    }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiClient;
} else {
    window.ApiClient = ApiClient;
}
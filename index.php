<?php
/**
 * FUSE React Sidebar - PHP SPA Demo
 * 
 * Este archivo es el punto de entrada principal para la demostración de SPA.
 * Carga el sidebar y proporciona el contenedor para las páginas dinámicas.
 * 
 * <AUTHOR> with Claude Code
 * @version 2.0
 */

// Start session
session_start();

// Enable error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Include navigation manager
require_once __DIR__ . '/core/NavigationManager.php';

// Include components
require_once __DIR__ . '/components/sidebar/FuseSidebar.class.php';
require_once __DIR__ . '/components/FuseHeader.php';

use Core\NavigationManager;

// Get page from request
$page = $_GET['page'] ?? $_SESSION['current_page'] ?? 'project';

// Initialize navigation manager
$navigation = new NavigationManager($page);

// Handle AJAX requests
if ($navigation->isAjax) {
    // Set JSON header
    header('Content-Type: application/json');
    
    // Get navigation response
    $response = $navigation->navigate($page);
    
    // Send JSON response
    echo json_encode($response);
    exit;
}

// For regular page loads, create sidebar
$sidebar = new FuseSidebar($navigation->getCurrentPage());
$sidebarHtml = $sidebar->render();

// Get page configuration
$pageConfig = $navigation->getCurrentPageConfig();
$pageTitle = $navigation->getPageTitle();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - FUSE React PHP SPA</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/main.css">
    <!-- Cargar scripts externos -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>
    <!-- Cargar el archivo sidebar.js desde el principio -->
    <script src="assets/js/main.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="flex flex-col h-screen">
        <!-- Header -->
        <?php 
        $header = new FuseHeader();
        echo $header->render();
        ?>
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar - Solo incluido en el contenedor principal, no dentro de páginas individuales -->
            <div id="sidebar-container">
                <?php echo $sidebarHtml; ?>
            </div>
            
            <!-- Contenido principal -->
            <main class="flex-1 overflow-y-auto" id="main-content">
                <div class="main-content-inner">
                    <?php echo $navigation->renderPage(); ?>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Pasar configuración a la inicialización del sistema
        window.appConfig = {
            initialPage: '<?php echo $navigation->getCurrentPage(); ?>',
            baseUrl: '<?php echo dirname($_SERVER['REQUEST_URI']); ?>',
            apiEndpoint: 'public/api/navigation.php',
            debug: <?php echo (isset($_GET['debug']) || isset($_SESSION['debug'])) ? 'true' : 'false'; ?>,
            preloadPages: <?php echo json_encode($navigation->getPreloadPages()); ?>,
            validPages: <?php echo json_encode($navigation->getValidPages()); ?>
        };
        
        // Navigation state
        window.navigationState = <?php echo json_encode($navigation->getClientState()); ?>;
        
        // La inicialización se maneja en main.js
    </script>
    
    <?php if (isset($_GET['debug']) || isset($_SESSION['debug'])): ?>
    <!-- Debug utilities for development -->
    <script src="assets/components/header/header-debug.js"></script>
    <script>
        // Ejecutar debug automático en modo desarrollo
        setTimeout(() => {
            console.log('🔧 Debug mode enabled');
            window.headerDebug.debug();
        }, 2000);
    </script>
    <?php endif; ?>
</body>
</html>
<?php
/**
 * Modern Router Class for SPA Backend
 * 
 * Provides RESTful routing with middleware support, parameter binding,
 * and advanced routing features for the ProyectDash SPA.
 * 
 * <AUTHOR> with Claude Code
 * @version 2.0
 */

namespace Core;

class Router
{
    /**
     * @var array Routes storage
     */
    private array $routes = [
        'GET' => [],
        'POST' => [],
        'PUT' => [],
        'DELETE' => [],
        'PATCH' => [],
        'OPTIONS' => []
    ];
    
    /**
     * @var array Named routes
     */
    private array $namedRoutes = [];
    
    /**
     * @var array Global middleware
     */
    private array $globalMiddleware = [];
    
    /**
     * @var array Route groups
     */
    private array $groups = [];
    
    /**
     * @var string Current group prefix
     */
    private string $currentGroupPrefix = '';
    
    /**
     * @var array Current group middleware
     */
    private array $currentGroupMiddleware = [];
    
    /**
     * Register a GET route
     * 
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function get(string $path, $handler, array $options = []): self
    {
        return $this->addRoute('GET', $path, $handler, $options);
    }
    
    /**
     * Register a POST route
     * 
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function post(string $path, $handler, array $options = []): self
    {
        return $this->addRoute('POST', $path, $handler, $options);
    }
    
    /**
     * Register a PUT route
     * 
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function put(string $path, $handler, array $options = []): self
    {
        return $this->addRoute('PUT', $path, $handler, $options);
    }
    
    /**
     * Register a DELETE route
     * 
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function delete(string $path, $handler, array $options = []): self
    {
        return $this->addRoute('DELETE', $path, $handler, $options);
    }
    
    /**
     * Register a PATCH route
     * 
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function patch(string $path, $handler, array $options = []): self
    {
        return $this->addRoute('PATCH', $path, $handler, $options);
    }
    
    /**
     * Register routes that respond to multiple HTTP verbs
     * 
     * @param array $methods
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function match(array $methods, string $path, $handler, array $options = []): self
    {
        foreach ($methods as $method) {
            $this->addRoute(strtoupper($method), $path, $handler, $options);
        }
        return $this;
    }
    
    /**
     * Register a route that responds to all HTTP verbs
     * 
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    public function any(string $path, $handler, array $options = []): self
    {
        $methods = array_keys($this->routes);
        return $this->match($methods, $path, $handler, $options);
    }
    
    /**
     * Create a route group
     * 
     * @param array $attributes
     * @param callable $callback
     * @return void
     */
    public function group(array $attributes, callable $callback): void
    {
        $previousGroupPrefix = $this->currentGroupPrefix;
        $previousGroupMiddleware = $this->currentGroupMiddleware;
        
        // Set group prefix
        if (isset($attributes['prefix'])) {
            $this->currentGroupPrefix = $previousGroupPrefix . '/' . trim($attributes['prefix'], '/');
        }
        
        // Set group middleware
        if (isset($attributes['middleware'])) {
            $middleware = is_array($attributes['middleware']) ? $attributes['middleware'] : [$attributes['middleware']];
            $this->currentGroupMiddleware = array_merge($previousGroupMiddleware, $middleware);
        }
        
        // Execute the group callback
        $callback($this);
        
        // Restore previous group settings
        $this->currentGroupPrefix = $previousGroupPrefix;
        $this->currentGroupMiddleware = $previousGroupMiddleware;
    }
    
    /**
     * Add global middleware
     * 
     * @param string|array $middleware
     * @return self
     */
    public function middleware($middleware): self
    {
        if (is_array($middleware)) {
            $this->globalMiddleware = array_merge($this->globalMiddleware, $middleware);
        } else {
            $this->globalMiddleware[] = $middleware;
        }
        return $this;
    }
    
    /**
     * Add a route to the router
     * 
     * @param string $method
     * @param string $path
     * @param mixed $handler
     * @param array $options
     * @return self
     */
    private function addRoute(string $method, string $path, $handler, array $options = []): self
    {
        // Apply group prefix
        if ($this->currentGroupPrefix) {
            $path = $this->currentGroupPrefix . '/' . trim($path, '/');
        }
        
        // Normalize path
        $path = '/' . trim($path, '/');
        
        // Convert path to regex pattern
        $pattern = $this->convertPathToRegex($path);
        
        // Extract parameter names
        $parameters = $this->extractParameters($path);
        
        // Prepare route data
        $route = [
            'path' => $path,
            'pattern' => $pattern,
            'handler' => $handler,
            'parameters' => $parameters,
            'middleware' => array_merge(
                $this->currentGroupMiddleware,
                $options['middleware'] ?? []
            ),
            'name' => $options['name'] ?? null
        ];
        
        // Store route
        $this->routes[$method][] = $route;
        
        // Store named route
        if (!empty($options['name'])) {
            $this->namedRoutes[$options['name']] = [
                'method' => $method,
                'path' => $path
            ];
        }
        
        return $this;
    }
    
    /**
     * Convert path to regex pattern
     * 
     * @param string $path
     * @return string
     */
    private function convertPathToRegex(string $path): string
    {
        // Escape forward slashes
        $pattern = preg_quote($path, '#');
        
        // Convert :param to named regex groups
        $pattern = preg_replace('/\\\:(\w+)/', '(?P<$1>[^/]+)', $pattern);
        
        // Convert {param} to named regex groups
        $pattern = preg_replace('/\\\{(\w+)\\\}/', '(?P<$1>[^/]+)', $pattern);
        
        // Convert {param?} to optional named regex groups
        $pattern = preg_replace('/\\\{(\w+)\?\\\}/', '(?P<$1>[^/]*)', $pattern);
        
        // Add start and end delimiters
        return '#^' . $pattern . '$#';
    }
    
    /**
     * Extract parameter names from path
     * 
     * @param string $path
     * @return array
     */
    private function extractParameters(string $path): array
    {
        $parameters = [];
        
        // Extract :param style parameters
        if (preg_match_all('/:(\w+)/', $path, $matches)) {
            $parameters = array_merge($parameters, $matches[1]);
        }
        
        // Extract {param} style parameters
        if (preg_match_all('/{(\w+)\??}/', $path, $matches)) {
            $parameters = array_merge($parameters, $matches[1]);
        }
        
        return array_unique($parameters);
    }
    
    /**
     * Dispatch the request to the appropriate route
     * 
     * @param string $method
     * @param string $uri
     * @return mixed
     * @throws \Exception
     */
    public function dispatch(string $method, string $uri)
    {
        $method = strtoupper($method);
        $uri = '/' . trim($uri, '/');
        
        // Handle OPTIONS requests for CORS
        if ($method === 'OPTIONS') {
            return $this->handleOptionsRequest();
        }
        
        // Find matching route
        $route = $this->findMatchingRoute($method, $uri);
        
        if (!$route) {
            throw new \Exception("Route not found: {$method} {$uri}", 404);
        }
        
        // Extract parameters from URI
        $parameters = $this->extractParametersFromUri($uri, $route['pattern']);
        
        // Execute middleware pipeline
        return $this->executeMiddlewarePipeline(
            array_merge($this->globalMiddleware, $route['middleware']),
            function() use ($route, $parameters) {
                return $this->executeHandler($route['handler'], $parameters);
            }
        );
    }
    
    /**
     * Find matching route for the request
     * 
     * @param string $method
     * @param string $uri
     * @return array|null
     */
    private function findMatchingRoute(string $method, string $uri): ?array
    {
        if (!isset($this->routes[$method])) {
            return null;
        }
        
        foreach ($this->routes[$method] as $route) {
            if (preg_match($route['pattern'], $uri)) {
                return $route;
            }
        }
        
        return null;
    }
    
    /**
     * Extract parameters from URI using route pattern
     * 
     * @param string $uri
     * @param string $pattern
     * @return array
     */
    private function extractParametersFromUri(string $uri, string $pattern): array
    {
        $parameters = [];
        
        if (preg_match($pattern, $uri, $matches)) {
            foreach ($matches as $key => $value) {
                if (is_string($key)) {
                    $parameters[$key] = $value;
                }
            }
        }
        
        return $parameters;
    }
    
    /**
     * Execute middleware pipeline
     * 
     * @param array $middleware
     * @param callable $destination
     * @return mixed
     */
    private function executeMiddlewarePipeline(array $middleware, callable $destination)
    {
        $pipeline = array_reduce(
            array_reverse($middleware),
            function ($next, $middleware) {
                return function ($request) use ($next, $middleware) {
                    // Resolve middleware instance
                    $instance = $this->resolveMiddleware($middleware);
                    
                    // Execute middleware
                    return $instance->handle($request, $next);
                };
            },
            $destination
        );
        
        // Execute the pipeline with the request
        return $pipeline($_REQUEST);
    }
    
    /**
     * Resolve middleware instance
     * 
     * @param string $middleware
     * @return object
     * @throws \Exception
     */
    private function resolveMiddleware(string $middleware): object
    {
        $middlewareClass = "App\\Middleware\\{$middleware}";
        
        if (!class_exists($middlewareClass)) {
            throw new \Exception("Middleware not found: {$middleware}");
        }
        
        return new $middlewareClass();
    }
    
    /**
     * Execute route handler
     * 
     * @param mixed $handler
     * @param array $parameters
     * @return mixed
     * @throws \Exception
     */
    private function executeHandler($handler, array $parameters)
    {
        // Closure handler
        if ($handler instanceof \Closure) {
            return $handler(...array_values($parameters));
        }
        
        // Controller@method handler
        if (is_string($handler) && strpos($handler, '@') !== false) {
            [$controller, $method] = explode('@', $handler);
            return $this->executeControllerMethod($controller, $method, $parameters);
        }
        
        // Array handler [Controller::class, 'method']
        if (is_array($handler) && count($handler) === 2) {
            [$controller, $method] = $handler;
            return $this->executeControllerMethod($controller, $method, $parameters);
        }
        
        throw new \Exception("Invalid route handler");
    }
    
    /**
     * Execute controller method
     * 
     * @param string $controller
     * @param string $method
     * @param array $parameters
     * @return mixed
     * @throws \Exception
     */
    private function executeControllerMethod(string $controller, string $method, array $parameters)
    {
        // Resolve controller class
        if (!str_contains($controller, '\\')) {
            $controller = "App\\Controllers\\{$controller}";
        }
        
        if (!class_exists($controller)) {
            throw new \Exception("Controller not found: {$controller}");
        }
        
        $instance = new $controller();
        
        if (!method_exists($instance, $method)) {
            throw new \Exception("Method not found: {$controller}@{$method}");
        }
        
        return $instance->$method(...array_values($parameters));
    }
    
    /**
     * Handle OPTIONS request for CORS
     * 
     * @return array
     */
    private function handleOptionsRequest(): array
    {
        return [
            'status' => 200,
            'headers' => [
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With',
                'Access-Control-Max-Age' => '86400'
            ],
            'body' => ''
        ];
    }
    
    /**
     * Get route URL by name
     * 
     * @param string $name
     * @param array $parameters
     * @return string
     * @throws \Exception
     */
    public function route(string $name, array $parameters = []): string
    {
        if (!isset($this->namedRoutes[$name])) {
            throw new \Exception("Named route not found: {$name}");
        }
        
        $path = $this->namedRoutes[$name]['path'];
        
        // Replace parameters in path
        foreach ($parameters as $key => $value) {
            $path = str_replace([':' . $key, '{' . $key . '}'], $value, $path);
        }
        
        return $path;
    }
    
    /**
     * Get all registered routes
     * 
     * @return array
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
    
    /**
     * Generate resource routes
     * 
     * @param string $resource
     * @param string $controller
     * @param array $options
     * @return void
     */
    public function resource(string $resource, string $controller, array $options = []): void
    {
        $only = $options['only'] ?? ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy'];
        $except = $options['except'] ?? [];
        
        $actions = array_diff($only, $except);
        
        $resourceRoutes = [
            'index' => ['GET', "/{$resource}", 'index'],
            'create' => ['GET', "/{$resource}/create", 'create'],
            'store' => ['POST', "/{$resource}", 'store'],
            'show' => ['GET', "/{$resource}/{id}", 'show'],
            'edit' => ['GET', "/{$resource}/{id}/edit", 'edit'],
            'update' => ['PUT', "/{$resource}/{id}", 'update'],
            'destroy' => ['DELETE', "/{$resource}/{id}", 'destroy']
        ];
        
        foreach ($resourceRoutes as $action => [$method, $path, $controllerMethod]) {
            if (in_array($action, $actions)) {
                $this->addRoute($method, $path, "{$controller}@{$controllerMethod}", [
                    'name' => "{$resource}.{$action}",
                    'middleware' => $options['middleware'] ?? []
                ]);
            }
        }
    }
    
    /**
     * Generate API resource routes (without create/edit forms)
     * 
     * @param string $resource
     * @param string $controller
     * @param array $options
     * @return void
     */
    public function apiResource(string $resource, string $controller, array $options = []): void
    {
        $options['except'] = array_merge($options['except'] ?? [], ['create', 'edit']);
        $this->resource($resource, $controller, $options);
    }
}
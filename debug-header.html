<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Header - ProyectDash</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }
        .debug-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #1f2937;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            max-width: 400px;
            z-index: 9999;
            font-size: 12px;
        }
        .debug-item {
            margin: 4px 0;
            padding: 2px;
        }
        .debug-found { color: #10b981; }
        .debug-missing { color: #ef4444; }
        .debug-warning { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-100">
    
    <!-- Simulamos el header completo -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                
                <!-- Logo y navegación izquierda -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">ProyectDash</h1>
                    </div>
                </div>

                <!-- Navegación derecha -->
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6 space-x-4">
                        
                        <!-- Dropdown de idioma -->
                        <div class="relative language-dropdown">
                            <button type="button" class="flex items-center text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 px-3 py-2 rounded-md text-sm font-medium">
                                🇪🇸 ES
                            </button>
                            <div class="language-dropdown-content hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="#" class="language-item block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-lang-code="ES">
                                    <span>🇪🇸</span> Español
                                </a>
                                <a href="#" class="language-item block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-lang-code="EN">
                                    <span>🇺🇸</span> English
                                </a>
                            </div>
                        </div>

                        <!-- Botón de pantalla completa -->
                        <button type="button" class="fullscreen-toggle bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white" data-header-tooltip="Pantalla completa">
                            <span class="sr-only">Ver en pantalla completa</span>
                            <svg class="w-4 h-4 fullscreen-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5M3.75 20.25v-4.5m0 4.5h4.5M20.25 3.75h-4.5m4.5 0v4.5M20.25 20.25h-4.5m4.5 0v-4.5" />
                            </svg>
                            <svg class="w-4 h-4 fullscreen-exit-icon hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 9V4.5M9 9H4.5M9 9L3.75 3.75M15 9h4.5M15 9V4.5M15 9l5.25-5.25M9 15v4.5M9 15H4.5M9 15l-5.25 5.25M15 15h4.5M15 15v4.5m0-4.5l5.25 5.25" />
                            </svg>
                        </button>

                        <!-- Botón de notificaciones -->
                        <button type="button" class="notification-toggle bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white relative" data-header-tooltip="Notificaciones">
                            <span class="sr-only">Ver notificaciones</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <!-- Badge de notificación -->
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white rounded-full text-xs flex items-center justify-center">3</span>
                        </button>

                        <!-- Botón de menú lateral -->
                        <button type="button" class="canvas-lateral-toggle bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white" data-header-tooltip="Menú lateral">
                            <span class="sr-only">Abrir menú lateral</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Canvas de notificaciones -->
    <div id="notificationsOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
    <div id="notificationsCanvas" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 border-l border-gray-200">
        <!-- Header del canvas -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center">
                <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <h2 class="text-lg font-semibold text-gray-900">Notificaciones</h2>
            </div>
            <div class="flex items-center space-x-2">
                <button class="clear-all-notifications text-gray-400 hover:text-gray-600 text-sm" title="Limpiar todas">
                    Limpiar
                </button>
                <button class="close-notifications text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Contenido del canvas -->
        <div class="overflow-y-auto h-full pb-16">
            <div class="p-4 space-y-4">
                <!-- Notificación 1 -->
                <div class="notification-item bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                    <div class="flex items-start">
                        <div class="text-xl mr-3">📊</div>
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-blue-900">Nuevo reporte disponible</h3>
                            <p class="text-sm text-blue-700 mt-1">El reporte mensual de ventas está listo para revisión.</p>
                            <div class="mt-2 flex space-x-2">
                                <button class="mark-as-read text-xs text-blue-600 hover:text-blue-800" data-notification-id="1">Marcar como leído</button>
                                <button class="delete-notification text-xs text-red-600 hover:text-red-800" data-notification-id="1">Eliminar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notificación 2 -->
                <div class="notification-item bg-green-50 border-l-4 border-green-500 p-4 rounded-r-lg">
                    <div class="flex items-start">
                        <div class="text-xl mr-3">✅</div>
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-green-900">Tarea completada</h3>
                            <p class="text-sm text-green-700 mt-1">La migración de datos se completó exitosamente.</p>
                            <div class="mt-2 flex space-x-2">
                                <button class="mark-as-read text-xs text-blue-600 hover:text-blue-800" data-notification-id="2">Marcar como leído</button>
                                <button class="delete-notification text-xs text-red-600 hover:text-red-800" data-notification-id="2">Eliminar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notificación 3 -->
                <div class="notification-item bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded-r-lg">
                    <div class="flex items-start">
                        <div class="text-xl mr-3">⚠️</div>
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-yellow-900">Advertencia del sistema</h3>
                            <p class="text-sm text-yellow-700 mt-1">El uso de memoria está al 85%. Considera optimizar.</p>
                            <div class="mt-2 flex space-x-2">
                                <button class="mark-as-read text-xs text-blue-600 hover:text-blue-800" data-notification-id="3">Marcar como leído</button>
                                <button class="delete-notification text-xs text-red-600 hover:text-red-800" data-notification-id="3">Eliminar</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer del canvas -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white">
            <button class="w-full bg-sky-500 text-white py-2 px-4 rounded-lg hover:bg-sky-600 transition-colors">
                Ver todas las notificaciones
            </button>
        </div>
    </div>

    <!-- Canvas lateral -->
    <div id="canvasLateralOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
    <div id="canvasLateral" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 border-l border-gray-200">
        <!-- Header del canvas -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center">
                <svg class="w-6 h-6 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
                <h2 class="text-lg font-semibold text-gray-900">Menú Lateral</h2>
            </div>
            <button class="close-canvas-lateral text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Contenido del canvas -->
        <div class="overflow-y-auto h-full pb-16">
            <div class="p-4 space-y-4">
                <!-- Información del proyecto -->
                <div class="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                    <h3 class="text-sm font-medium text-blue-900">Estado del Proyecto</h3>
                    <p class="text-sm text-blue-700 mt-1">Progreso: 75% completado</p>
                </div>

                <!-- Tareas pendientes -->
                <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded-r-lg">
                    <h3 class="text-sm font-medium text-yellow-900">Tareas Pendientes</h3>
                    <p class="text-sm text-yellow-700 mt-1">5 tareas requieren atención</p>
                </div>

                <!-- Recursos del sistema -->
                <div class="bg-green-50 border-l-4 border-green-500 p-4 rounded-r-lg">
                    <h3 class="text-sm font-medium text-green-900">Recursos del Sistema</h3>
                    <p class="text-sm text-green-700 mt-1">CPU: 45% | RAM: 60% | Disco: 30%</p>
                </div>
            </div>
        </div>

        <!-- Footer del canvas -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white">
            <button class="w-full bg-sky-500 text-white py-2 px-4 rounded-lg hover:bg-sky-600 transition-colors">
                Configuración avanzada
            </button>
        </div>
    </div>

    <!-- Panel de debug -->
    <div class="debug-panel">
        <h3 style="margin-bottom: 8px; font-weight: bold;">🔧 Debug Header</h3>
        <div id="debug-content">Cargando...</div>
        <button onclick="runDebug()" style="background: #3b82f6; color: white; padding: 4px 8px; border: none; border-radius: 4px; margin-top: 8px; cursor: pointer;">
            Actualizar Debug
        </button>
    </div>

    <!-- Contenido principal -->
    <main class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Debug del Header</h2>
            <div class="bg-white shadow rounded-lg p-6">
                <p class="text-gray-600 mb-4">Esta página permite probar la funcionalidad de los botones del header.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-blue-900">🔍 Pantalla Completa</h3>
                        <p class="text-blue-700 text-sm mt-2">Haz clic en el icono ⛶ del header para activar pantalla completa</p>
                    </div>
                    
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-green-900">🔔 Notificaciones</h3>
                        <p class="text-green-700 text-sm mt-2">Haz clic en el icono de campana para ver las notificaciones</p>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-yellow-900">≡ Menú Lateral</h3>
                        <p class="text-yellow-700 text-sm mt-2">Haz clic en el icono de menú para abrir el panel lateral</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Cargar CSS y JS -->
    <link rel="stylesheet" href="assets/components/header/header-enhanced.css">
    <script src="assets/components/header/header-enhanced.js"></script>
    
    <script>
        // Debug function
        function runDebug() {
            const debugContent = document.getElementById('debug-content');
            const results = [];
            
            // Check elements
            const elements = {
                'Fullscreen Toggle': document.querySelector('.fullscreen-toggle'),
                'Fullscreen Icon': document.querySelector('.fullscreen-icon'),
                'Fullscreen Exit Icon': document.querySelector('.fullscreen-exit-icon'),
                'Notification Toggle': document.querySelector('.notification-toggle'),
                'Notifications Canvas': document.getElementById('notificationsCanvas'),
                'Notifications Overlay': document.getElementById('notificationsOverlay'),
                'Canvas Lateral Toggle': document.querySelector('.canvas-lateral-toggle'),
                'Canvas Lateral': document.getElementById('canvasLateral'),
                'Canvas Lateral Overlay': document.getElementById('canvasLateralOverlay'),
                'Close Notifications': document.querySelector('.close-notifications'),
                'Close Canvas Lateral': document.querySelector('.close-canvas-lateral'),
                'Clear All Notifications': document.querySelector('.clear-all-notifications')
            };
            
            results.push('<div class="debug-item"><strong>🔍 Elementos DOM:</strong></div>');
            for (const [name, element] of Object.entries(elements)) {
                const status = element ? 'debug-found' : 'debug-missing';
                const icon = element ? '✅' : '❌';
                results.push(`<div class="debug-item ${status}">${icon} ${name}</div>`);
                
                // Check event listeners if element exists
                if (element) {
                    const hasClick = element.onclick || element.addEventListener;
                    const listenerStatus = hasClick ? 'debug-warning' : 'debug-missing';
                    const listenerIcon = hasClick ? '⚠️' : '❌';
                    results.push(`<div class="debug-item ${listenerStatus}">  ${listenerIcon} Event listeners attached</div>`);
                }
            }
            
            // Check header instance
            results.push('<div class="debug-item"><strong>🔧 JavaScript:</strong></div>');
            const headerInstance = window.headerEnhancedInstance || window.getHeaderInstance?.();
            const instanceStatus = headerInstance ? 'debug-found' : 'debug-missing';
            const instanceIcon = headerInstance ? '✅' : '❌';
            results.push(`<div class="debug-item ${instanceStatus}">${instanceIcon} HeaderEnhanced Instance</div>`);
            
            if (headerInstance) {
                const isInitialized = headerInstance.isInitialized;
                const initStatus = isInitialized ? 'debug-found' : 'debug-warning';
                const initIcon = isInitialized ? '✅' : '⚠️';
                results.push(`<div class="debug-item ${initStatus}">  ${initIcon} Is Initialized: ${isInitialized}</div>`);
            }
            
            // Check functions
            const functions = {
                'initHeader': window.initHeader,
                'getHeaderInstance': window.getHeaderInstance,
                'destroyHeader': window.destroyHeader
            };
            
            for (const [name, func] of Object.entries(functions)) {
                const status = typeof func === 'function' ? 'debug-found' : 'debug-missing';
                const icon = typeof func === 'function' ? '✅' : '❌';
                results.push(`<div class="debug-item ${status}">${icon} ${name}()</div>`);
            }
            
            // Check CSS
            results.push('<div class="debug-item"><strong>🎨 CSS:</strong></div>');
            const testElement = document.querySelector('.fullscreen-toggle');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                const hasTransition = styles.transition !== 'all 0s ease 0s';
                const transitionStatus = hasTransition ? 'debug-found' : 'debug-warning';
                const transitionIcon = hasTransition ? '✅' : '⚠️';
                results.push(`<div class="debug-item ${transitionStatus}">${transitionIcon} CSS transitions loaded</div>`);
            }
            
            debugContent.innerHTML = results.join('');
        }
        
        // Run initial debug
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runDebug, 500);
        });
        
        // Manual test buttons
        function testFullscreen() {
            console.log('Testing fullscreen...');
            const btn = document.querySelector('.fullscreen-toggle');
            if (btn) {
                btn.click();
                console.log('Fullscreen button clicked');
            } else {
                console.error('Fullscreen button not found');
            }
        }
        
        function testNotifications() {
            console.log('Testing notifications...');
            const btn = document.querySelector('.notification-toggle');
            if (btn) {
                btn.click();
                console.log('Notifications button clicked');
            } else {
                console.error('Notifications button not found');
            }
        }
        
        function testCanvasLateral() {
            console.log('Testing canvas lateral...');
            const btn = document.querySelector('.canvas-lateral-toggle');
            if (btn) {
                btn.click();
                console.log('Canvas lateral button clicked');
            } else {
                console.error('Canvas lateral button not found');
            }
        }
        
        // Add test buttons to console
        console.log('%c🔧 Header Debug Functions:', 'color: blue; font-weight: bold;');
        console.log('testFullscreen() - Test fullscreen button');
        console.log('testNotifications() - Test notifications button');
        console.log('testCanvasLateral() - Test canvas lateral button');
        console.log('runDebug() - Run debug check');
        
        // Make functions global for console access
        window.testFullscreen = testFullscreen;
        window.testNotifications = testNotifications;
        window.testCanvasLateral = testCanvasLateral;
        window.runDebug = runDebug;
    </script>
</body>
</html>
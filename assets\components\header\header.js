/**
 * FuseHeader JavaScript
 * 
 * Implementa la funcionalidad interactiva para el componente FuseHeader
 */

document.addEventListener('DOMContentLoaded', function() {
    initHeader();
});

/**
 * Inicializa todas las funcionalidades del header
 */
function initHeader() {
    initLanguageDropdown();
    initFullscreenToggle();
    initThemeToggle();
    initNotificationPanel();
    initCanvasLateral();
    initPanelsDefaults();
}

/**
 * Inicializa el menú desplegable de idiomas
 */
function initLanguageDropdown() {
    // Selector del botón de idioma
    const languageButton = document.querySelector('.language-dropdown button');
    // Selector del menú desplegable
    const dropdown = document.querySelector('.language-dropdown-content');
    // Selectores de todos los elementos del menú
    const languageItems = document.querySelectorAll('.language-item');
    
    if (!languageButton || !dropdown) return;

    // Mostrar/ocultar el menú al hacer clic en el botón
    languageButton.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdown.classList.toggle('hidden');
    });

    // Cerrar el menú al hacer clic fuera de él
    document.addEventListener('click', function(e) {
        // Verificar que e.target sea un nodo DOM válido
        if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE) {
            return;
        }
        
        if (!dropdown.contains(e.target) && !languageButton.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });

    // Manejar la selección de un idioma
    languageItems.forEach(item => {
        item.addEventListener('click', function() {
            const langCode = this.dataset.langCode;
            const langFlag = this.querySelector('span').textContent.trim();
            
            // Actualizar el botón con el idioma seleccionado
            languageButton.innerHTML = `${langFlag} ${langCode}`;
            
            // Cerrar el menú desplegable
            dropdown.classList.add('hidden');

            // Aquí se podría enviar una solicitud AJAX para cambiar el idioma en el servidor
            console.log(`Idioma cambiado a: ${langCode}`);
        });
    });
}

/**
 * Inicializa el botón de pantalla completa
 */
function initFullscreenToggle() {
    const fullscreenButton = document.querySelector('.fullscreen-toggle');
    const fullscreenIcon = document.querySelector('.fullscreen-icon');
    const fullscreenExitIcon = document.querySelector('.fullscreen-exit-icon');

    if (!fullscreenButton) {
        console.warn('Botón de pantalla completa no encontrado');
        return;
    }

    // Función para alternar pantalla completa
    function toggleFullscreen() {
        if (!document.fullscreenElement &&
            !document.webkitFullscreenElement &&
            !document.mozFullScreenElement &&
            !document.msFullscreenElement) {

            // Entrar en pantalla completa
            const docEl = document.documentElement;
            if (docEl.requestFullscreen) {
                docEl.requestFullscreen();
            } else if (docEl.webkitRequestFullscreen) {
                docEl.webkitRequestFullscreen();
            } else if (docEl.mozRequestFullScreen) {
                docEl.mozRequestFullScreen();
            } else if (docEl.msRequestFullscreen) {
                docEl.msRequestFullscreen();
            }
        } else {
            // Salir de pantalla completa
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    // Función para actualizar iconos
    function updateFullscreenIcons() {
        const isFullscreen = !!(document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement);

        if (fullscreenIcon && fullscreenExitIcon) {
            if (isFullscreen) {
                fullscreenIcon.classList.add('hidden');
                fullscreenExitIcon.classList.remove('hidden');
                fullscreenButton.classList.add('active');
            } else {
                fullscreenIcon.classList.remove('hidden');
                fullscreenExitIcon.classList.add('hidden');
                fullscreenButton.classList.remove('active');
            }
        }
    }

    // Event listener para el botón
    fullscreenButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleFullscreen();
    });

    // Event listeners para cambios de pantalla completa
    document.addEventListener('fullscreenchange', updateFullscreenIcons);
    document.addEventListener('webkitfullscreenchange', updateFullscreenIcons);
    document.addEventListener('mozfullscreenchange', updateFullscreenIcons);
    document.addEventListener('MSFullscreenChange', updateFullscreenIcons);

    // Inicializar estado de iconos
    updateFullscreenIcons();

    console.log('✅ Fullscreen toggle inicializado correctamente');
}

/**
 * Inicializa el botón de cambio de tema (claro/oscuro)
 */
function initThemeToggle() {
    const themeButton = document.querySelector('button svg[d*="M12 3v2.25m6.364.386"]').parentElement;
    let darkMode = localStorage.getItem('darkMode') === 'true';
    
    if (!themeButton) return;

    // Aplicar tema guardado al cargar la página
    if (darkMode) {
        document.documentElement.classList.add('dark-theme');
    }

    themeButton.addEventListener('click', function() {
        darkMode = !darkMode;
        
        if (darkMode) {
            document.documentElement.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
        }
        
        // Guardar preferencia
        localStorage.setItem('darkMode', darkMode);
    });
}

/**
 * Inicializa el panel de notificaciones
 */
function initNotificationPanel() {
    const notificationButton = document.querySelector('.notification-toggle');
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const overlay = document.querySelector('#notificationsOverlay');
    const closeButton = document.querySelector('.close-notifications');

    if (!notificationButton) {
        console.warn('Botón de notificaciones no encontrado');
        return;
    }

    if (!notificationsCanvas) {
        console.warn('Panel de notificaciones no encontrado');
        return;
    }

    // Función para mostrar panel de notificaciones
    function showNotifications() {
        // Mostrar overlay si existe
        if (overlay) {
            overlay.classList.remove('hidden');
            overlay.style.display = 'block';
        }

        // Mostrar y animar panel
        notificationsCanvas.style.display = 'block';
        notificationsCanvas.classList.remove('translate-x-full');
        notificationsCanvas.classList.add('translate-x-0');

        // Activar botón
        notificationButton.classList.add('active');

        // Cerrar canvas lateral si está abierto
        hideCanvasLateral();

        console.log('✅ Panel de notificaciones mostrado');
    }

    // Función para ocultar panel de notificaciones
    function hideNotifications() {
        // Ocultar overlay
        if (overlay) {
            overlay.classList.add('hidden');
            overlay.style.display = 'none';
        }

        // Animar y ocultar panel
        notificationsCanvas.classList.remove('translate-x-0');
        notificationsCanvas.classList.add('translate-x-full');

        // Desactivar botón
        notificationButton.classList.remove('active');

        // Ocultar panel después de la animación
        setTimeout(() => {
            notificationsCanvas.style.display = 'none';
        }, 300);

        console.log('✅ Panel de notificaciones ocultado');
    }

    // Función para alternar panel
    function toggleNotifications() {
        const isVisible = !notificationsCanvas.classList.contains('translate-x-full') &&
                         notificationsCanvas.style.display !== 'none';

        if (isVisible) {
            hideNotifications();
        } else {
            showNotifications();
        }
    }

    // Event listener para abrir/cerrar panel
    notificationButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleNotifications();
    });

    // Event listener para cerrar con botón X
    if (closeButton) {
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            hideNotifications();
        });
    }

    // Event listener para cerrar con overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            hideNotifications();
        });
    }

    // Exponer funciones globalmente
    window.showNotifications = showNotifications;
    window.hideNotifications = hideNotifications;
    window.toggleNotifications = toggleNotifications;

    console.log('✅ Panel de notificaciones inicializado correctamente');
}

/**
 * Inicializa el canvas lateral
 */
function initCanvasLateral() {
    const canvasButton = document.querySelector('.canvas-lateral-toggle');
    const canvasLateral = document.querySelector('#canvasLateral');
    const overlay = document.querySelector('#canvasLateralOverlay');
    const closeButton = document.querySelector('.close-canvas-lateral');

    if (!canvasButton) {
        console.warn('Botón de canvas lateral no encontrado');
        return;
    }

    if (!canvasLateral) {
        console.warn('Canvas lateral no encontrado');
        return;
    }

    // Función para mostrar canvas lateral
    function showCanvasLateral() {
        // Mostrar overlay si existe
        if (overlay) {
            overlay.classList.remove('hidden');
            overlay.style.display = 'block';
        }

        // Mostrar y animar panel
        canvasLateral.style.display = 'block';
        canvasLateral.classList.remove('translate-x-full');
        canvasLateral.classList.add('translate-x-0');

        // Activar botón
        canvasButton.classList.add('active');

        // Cerrar notificaciones si están abiertas
        if (window.hideNotifications) {
            window.hideNotifications();
        }

        console.log('✅ Canvas lateral mostrado');
    }

    // Función para ocultar canvas lateral
    function hideCanvasLateral() {
        // Ocultar overlay
        if (overlay) {
            overlay.classList.add('hidden');
            overlay.style.display = 'none';
        }

        // Animar y ocultar panel
        canvasLateral.classList.remove('translate-x-0');
        canvasLateral.classList.add('translate-x-full');

        // Desactivar botón
        canvasButton.classList.remove('active');

        // Ocultar panel después de la animación
        setTimeout(() => {
            canvasLateral.style.display = 'none';
        }, 300);

        console.log('✅ Canvas lateral ocultado');
    }

    // Función para alternar canvas lateral
    function toggleCanvasLateral() {
        const isVisible = !canvasLateral.classList.contains('translate-x-full') &&
                         canvasLateral.style.display !== 'none';

        if (isVisible) {
            hideCanvasLateral();
        } else {
            showCanvasLateral();
        }
    }

    // Event listener para abrir/cerrar panel
    canvasButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleCanvasLateral();
    });

    // Event listener para cerrar con botón X
    if (closeButton) {
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            hideCanvasLateral();
        });
    }

    // Event listener para cerrar con overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            hideCanvasLateral();
        });
    }

    // Exponer funciones globalmente
    window.showCanvasLateral = showCanvasLateral;
    window.hideCanvasLateral = hideCanvasLateral;
    window.toggleCanvasLateral = toggleCanvasLateral;

    console.log('✅ Canvas lateral inicializado correctamente');
}

/**
 * Inicializa los paneles con estado oculto por defecto
 */
function initPanelsDefaults() {
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const canvasLateral = document.querySelector('#canvasLateral');
    const notificationsOverlay = document.querySelector('#notificationsOverlay');
    const canvasLateralOverlay = document.querySelector('#canvasLateralOverlay');
    
    // Ocultar paneles por defecto
    if (notificationsCanvas) {
        notificationsCanvas.style.transform = 'translateX(100%)';
        notificationsCanvas.style.display = 'none';
    }
    
    if (canvasLateral) {
        canvasLateral.style.transform = 'translateX(100%)';
        canvasLateral.style.display = 'none';
    }
    
    // Ocultar overlays
    if (notificationsOverlay) {
        notificationsOverlay.classList.add('hidden');
    }
    
    if (canvasLateralOverlay) {
        canvasLateralOverlay.classList.add('hidden');
    }
}





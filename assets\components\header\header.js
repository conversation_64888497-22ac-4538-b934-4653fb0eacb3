/**
 * FuseHeader JavaScript
 * 
 * Implementa la funcionalidad interactiva para el componente FuseHeader
 */

document.addEventListener('DOMContentLoaded', function() {
    initHeader();
});

/**
 * Inicializa todas las funcionalidades del header
 */
function initHeader() {
    initLanguageDropdown();
    initFullscreenToggle();
    initThemeToggle();
    initNotificationPanel();
    initCanvasLateral();
    initPanelsDefaults();
}

/**
 * Inicializa el menú desplegable de idiomas
 */
function initLanguageDropdown() {
    // Selector del botón de idioma
    const languageButton = document.querySelector('.language-dropdown button');
    // Selector del menú desplegable
    const dropdown = document.querySelector('.language-dropdown-content');
    // Selectores de todos los elementos del menú
    const languageItems = document.querySelectorAll('.language-item');
    
    if (!languageButton || !dropdown) return;

    // Mostrar/ocultar el menú al hacer clic en el botón
    languageButton.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdown.classList.toggle('hidden');
    });

    // Cerrar el menú al hacer clic fuera de él
    document.addEventListener('click', function(e) {
        // Verificar que e.target sea un nodo DOM válido
        if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE) {
            return;
        }
        
        if (!dropdown.contains(e.target) && !languageButton.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });

    // Manejar la selección de un idioma
    languageItems.forEach(item => {
        item.addEventListener('click', function() {
            const langCode = this.dataset.langCode;
            const langFlag = this.querySelector('span').textContent.trim();
            
            // Actualizar el botón con el idioma seleccionado
            languageButton.innerHTML = `${langFlag} ${langCode}`;
            
            // Cerrar el menú desplegable
            dropdown.classList.add('hidden');

            // Aquí se podría enviar una solicitud AJAX para cambiar el idioma en el servidor
            console.log(`Idioma cambiado a: ${langCode}`);
        });
    });
}

/**
 * Inicializa el botón de pantalla completa
 */
function initFullscreenToggle() {
    const fullscreenButton = document.querySelector('.fullscreen-toggle');
    
    if (!fullscreenButton) {
        console.warn('Botón de pantalla completa no encontrado');
        return;
    }

    fullscreenButton.addEventListener('click', function() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.error(`Error al intentar entrar en modo pantalla completa: ${err.message}`);
            });
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
    });
}

/**
 * Inicializa el botón de cambio de tema (claro/oscuro)
 */
function initThemeToggle() {
    const themeButton = document.querySelector('button svg[d*="M12 3v2.25m6.364.386"]').parentElement;
    let darkMode = localStorage.getItem('darkMode') === 'true';
    
    if (!themeButton) return;

    // Aplicar tema guardado al cargar la página
    if (darkMode) {
        document.documentElement.classList.add('dark-theme');
    }

    themeButton.addEventListener('click', function() {
        darkMode = !darkMode;
        
        if (darkMode) {
            document.documentElement.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
        }
        
        // Guardar preferencia
        localStorage.setItem('darkMode', darkMode);
    });
}

/**
 * Inicializa el panel de notificaciones
 */
function initNotificationPanel() {
    const notificationButton = document.querySelector('.notification-toggle');
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const overlay = document.querySelector('#notificationsOverlay');
    const closeButton = document.querySelector('.close-notifications');
    
    if (!notificationButton || !notificationsCanvas) {
        console.warn('Elementos del panel de notificaciones no encontrados');
        return;
    }

    // Event listener para abrir/cerrar panel
    notificationButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleNotifications();
    });

    // Event listener para cerrar con botón X
    if (closeButton) {
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            hideNotifications();
        });
    }

    // Event listener para cerrar con overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            hideNotifications();
        });
    }
}

/**
 * Inicializa el canvas lateral
 */
function initCanvasLateral() {
    const canvasButton = document.querySelector('.canvas-lateral-toggle');
    const canvasLateral = document.querySelector('#canvasLateral');
    const overlay = document.querySelector('#canvasLateralOverlay');
    const closeButton = document.querySelector('.close-canvas-lateral');
    
    if (!canvasButton || !canvasLateral) {
        console.warn('Elementos del canvas lateral no encontrados');
        return;
    }

    // Event listener para abrir/cerrar panel
    canvasButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleCanvasLateral();
    });

    // Event listener para cerrar con botón X
    if (closeButton) {
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            hideCanvasLateral();
        });
    }

    // Event listener para cerrar con overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            hideCanvasLateral();
        });
    }
}

/**
 * Inicializa los paneles con estado oculto por defecto
 */
function initPanelsDefaults() {
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const canvasLateral = document.querySelector('#canvasLateral');
    const notificationsOverlay = document.querySelector('#notificationsOverlay');
    const canvasLateralOverlay = document.querySelector('#canvasLateralOverlay');
    
    // Ocultar paneles por defecto
    if (notificationsCanvas) {
        notificationsCanvas.style.transform = 'translateX(100%)';
        notificationsCanvas.style.display = 'none';
    }
    
    if (canvasLateral) {
        canvasLateral.style.transform = 'translateX(100%)';
        canvasLateral.style.display = 'none';
    }
    
    // Ocultar overlays
    if (notificationsOverlay) {
        notificationsOverlay.classList.add('hidden');
    }
    
    if (canvasLateralOverlay) {
        canvasLateralOverlay.classList.add('hidden');
    }
}

/**
 * Alterna la visibilidad del panel de notificaciones
 */
function toggleNotifications() {
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const overlay = document.querySelector('#notificationsOverlay');
    const button = document.querySelector('.notification-toggle');
    
    if (!notificationsCanvas) return;
    
    const isVisible = notificationsCanvas.style.transform === 'translateX(0px)' || 
                     notificationsCanvas.style.transform === 'translateX(0%)';
    
    if (!isVisible) {
        showNotifications();
    } else {
        hideNotifications();
    }
}

/**
 * Muestra el panel de notificaciones
 */
function showNotifications() {
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const overlay = document.querySelector('#notificationsOverlay');
    const button = document.querySelector('.notification-toggle');
    
    // Ocultar canvas lateral si está abierto
    hideCanvasLateral();
    
    if (notificationsCanvas) {
        notificationsCanvas.style.display = 'block';
        setTimeout(() => {
            notificationsCanvas.style.transform = 'translateX(0%)';
        }, 10);
    }
    
    if (overlay) {
        overlay.classList.remove('hidden');
    }
    
    if (button) {
        button.classList.add('active');
    }
    
    // Prevenir scroll del body
    document.body.style.overflow = 'hidden';
}

/**
 * Oculta el panel de notificaciones
 */
function hideNotifications() {
    const notificationsCanvas = document.querySelector('#notificationsCanvas');
    const overlay = document.querySelector('#notificationsOverlay');
    const button = document.querySelector('.notification-toggle');
    
    if (notificationsCanvas) {
        notificationsCanvas.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notificationsCanvas.style.display = 'none';
        }, 300);
    }
    
    if (overlay) {
        overlay.classList.add('hidden');
    }
    
    if (button) {
        button.classList.remove('active');
    }
    
    // Restaurar scroll del body
    document.body.style.overflow = '';
}

/**
 * Alterna la visibilidad del canvas lateral
 */
function toggleCanvasLateral() {
    const canvasLateral = document.querySelector('#canvasLateral');
    
    if (!canvasLateral) return;
    
    const isVisible = canvasLateral.style.transform === 'translateX(0px)' || 
                     canvasLateral.style.transform === 'translateX(0%)';
    
    if (!isVisible) {
        showCanvasLateral();
    } else {
        hideCanvasLateral();
    }
}

/**
 * Muestra el canvas lateral
 */
function showCanvasLateral() {
    const canvasLateral = document.querySelector('#canvasLateral');
    const overlay = document.querySelector('#canvasLateralOverlay');
    const button = document.querySelector('.canvas-lateral-toggle');
    
    // Ocultar panel de notificaciones si está abierto
    hideNotifications();
    
    if (canvasLateral) {
        canvasLateral.style.display = 'block';
        setTimeout(() => {
            canvasLateral.style.transform = 'translateX(0%)';
        }, 10);
    }
    
    if (overlay) {
        overlay.classList.remove('hidden');
    }
    
    if (button) {
        button.classList.add('active');
    }
    
    // Prevenir scroll del body
    document.body.style.overflow = 'hidden';
}

/**
 * Oculta el canvas lateral
 */
function hideCanvasLateral() {
    const canvasLateral = document.querySelector('#canvasLateral');
    const overlay = document.querySelector('#canvasLateralOverlay');
    const button = document.querySelector('.canvas-lateral-toggle');
    
    if (canvasLateral) {
        canvasLateral.style.transform = 'translateX(100%)';
        setTimeout(() => {
            canvasLateral.style.display = 'none';
        }, 300);
    }
    
    if (overlay) {
        overlay.classList.add('hidden');
    }
    
    if (button) {
        button.classList.remove('active');
    }
    
    // Restaurar scroll del body
    document.body.style.overflow = '';
}

// Exponer funciones globalmente para compatibilidad
window.toggleNotifications = toggleNotifications;
window.toggleCanvasLateral = toggleCanvasLateral;
window.toggleFullscreen = function() {
    const fullscreenButton = document.querySelector('.fullscreen-toggle');
    if (fullscreenButton) {
        fullscreenButton.click();
    }
};
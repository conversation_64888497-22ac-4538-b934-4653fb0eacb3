<!-- Contacts Page -->
<div class="page-content">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h1 class="page-title">Contacts</h1>
        <div class="page-actions">
            <div class="relative mr-2">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="text-gray-400">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <input type="text" class="pl-10 pr-4 py-2 border border-gray-300 rounded-md" placeholder="Search contacts...">
            </div>
            <button class="btn btn-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Contact
            </button>
        </div>
    </div>

    <!-- Contact Filters -->
    <div class="flex flex-wrap items-center justify-between mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div class="flex space-x-2 mb-2 sm:mb-0">
            <button class="btn btn-secondary btn-sm active">All</button>
            <button class="btn btn-secondary btn-sm">Frequent</button>
            <button class="btn btn-secondary btn-sm">Starred</button>
        </div>
        <div class="flex flex-wrap gap-2">
            <select class="border border-gray-300 rounded-md text-sm py-1.5 px-3">
                <option>All Departments</option>
                <option>Engineering</option>
                <option>Design</option>
                <option>Marketing</option>
                <option>Sales</option>
                <option>Support</option>
            </select>
            <select class="border border-gray-300 rounded-md text-sm py-1.5 px-3">
                <option>Sort by: Name</option>
                <option>Sort by: Email</option>
                <option>Sort by: Phone</option>
                <option>Sort by: Department</option>
                <option>Sort by: Location</option>
            </select>
            <div class="flex border border-gray-300 rounded-md overflow-hidden">
                <button class="px-3 py-1.5 bg-white hover:bg-gray-100 border-r border-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                </button>
                <button class="px-3 py-1.5 bg-blue-50 text-blue-600 hover:bg-blue-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Contacts Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <!-- Contact Card 1 -->
        <div class="card hover:shadow-md transition-shadow">
            <div class="p-4">
                <div class="flex items-center">
                    <img class="h-16 w-16 rounded-full object-cover" src="https://ui-avatars.com/api/?name=John+Smith&background=0D8ABC&color=fff" alt="John Smith">
                    <div class="ml-4 flex-1 min-w-0">
                        <h3 class="text-lg font-medium text-gray-900 truncate">John Smith</h3>
                        <p class="text-sm text-gray-500">Engineering Manager</p>
                    </div>
                    <button class="text-gray-400 hover:text-yellow-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4 border-t border-gray-100 pt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">Email</p>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Phone</p>
                            <p class="text-sm text-gray-900">+****************</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Department</p>
                            <p class="text-sm text-gray-900">Engineering</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Location</p>
                            <p class="text-sm text-gray-900">San Francisco, CA</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Email
                    </button>
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                    </button>
                </div>
            </div>
        </div>

        <!-- Contact Card 2 -->
        <div class="card hover:shadow-md transition-shadow">
            <div class="p-4">
                <div class="flex items-center">
                    <img class="h-16 w-16 rounded-full object-cover" src="https://ui-avatars.com/api/?name=Sarah+Johnson&background=2563EB&color=fff" alt="Sarah Johnson">
                    <div class="ml-4 flex-1 min-w-0">
                        <h3 class="text-lg font-medium text-gray-900 truncate">Sarah Johnson</h3>
                        <p class="text-sm text-gray-500">Product Manager</p>
                    </div>
                    <button class="text-yellow-500 hover:text-yellow-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4 border-t border-gray-100 pt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">Email</p>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Phone</p>
                            <p class="text-sm text-gray-900">+****************</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Department</p>
                            <p class="text-sm text-gray-900">Product</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Location</p>
                            <p class="text-sm text-gray-900">New York, NY</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Email
                    </button>
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                    </button>
                </div>
            </div>
        </div>

        <!-- Contact Card 3 -->
        <div class="card hover:shadow-md transition-shadow">
            <div class="p-4">
                <div class="flex items-center">
                    <img class="h-16 w-16 rounded-full object-cover" src="https://ui-avatars.com/api/?name=Michael+Brown&background=059669&color=fff" alt="Michael Brown">
                    <div class="ml-4 flex-1 min-w-0">
                        <h3 class="text-lg font-medium text-gray-900 truncate">Michael Brown</h3>
                        <p class="text-sm text-gray-500">UX Designer</p>
                    </div>
                    <button class="text-gray-400 hover:text-yellow-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4 border-t border-gray-100 pt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">Email</p>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Phone</p>
                            <p class="text-sm text-gray-900">+****************</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Department</p>
                            <p class="text-sm text-gray-900">Design</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Location</p>
                            <p class="text-sm text-gray-900">Austin, TX</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Email
                    </button>
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                    </button>
                </div>
            </div>
        </div>

        <!-- Contact Card 4 -->
        <div class="card hover:shadow-md transition-shadow">
            <div class="p-4">
                <div class="flex items-center">
                    <img class="h-16 w-16 rounded-full object-cover" src="https://ui-avatars.com/api/?name=Emily+Davis&background=DC2626&color=fff" alt="Emily Davis">
                    <div class="ml-4 flex-1 min-w-0">
                        <h3 class="text-lg font-medium text-gray-900 truncate">Emily Davis</h3>
                        <p class="text-sm text-gray-500">Marketing Director</p>
                    </div>
                    <button class="text-yellow-500 hover:text-yellow-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4 border-t border-gray-100 pt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">Email</p>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Phone</p>
                            <p class="text-sm text-gray-900">+****************</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Department</p>
                            <p class="text-sm text-gray-900">Marketing</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Location</p>
                            <p class="text-sm text-gray-900">Chicago, IL</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Email
                    </button>
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                    </button>
                </div>
            </div>
        </div>

        <!-- Contact Card 5 -->
        <div class="card hover:shadow-md transition-shadow">
            <div class="p-4">
                <div class="flex items-center">
                    <img class="h-16 w-16 rounded-full object-cover" src="https://ui-avatars.com/api/?name=David+Wilson&background=7C3AED&color=fff" alt="David Wilson">
                    <div class="ml-4 flex-1 min-w-0">
                        <h3 class="text-lg font-medium text-gray-900 truncate">David Wilson</h3>
                        <p class="text-sm text-gray-500">Sales Manager</p>
                    </div>
                    <button class="text-gray-400 hover:text-yellow-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4 border-t border-gray-100 pt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">Email</p>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Phone</p>
                            <p class="text-sm text-gray-900">+****************</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Department</p>
                            <p class="text-sm text-gray-900">Sales</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Location</p>
                            <p class="text-sm text-gray-900">Seattle, WA</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Email
                    </button>
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                    </button>
                </div>
            </div>
        </div>

        <!-- Contact Card 6 -->
        <div class="card hover:shadow-md transition-shadow">
            <div class="p-4">
                <div class="flex items-center">
                    <img class="h-16 w-16 rounded-full object-cover" src="https://ui-avatars.com/api/?name=Olivia+Martinez&background=F59E0B&color=fff" alt="Olivia Martinez">
                    <div class="ml-4 flex-1 min-w-0">
                        <h3 class="text-lg font-medium text-gray-900 truncate">Olivia Martinez</h3>
                        <p class="text-sm text-gray-500">Customer Support</p>
                    </div>
                    <button class="text-yellow-500 hover:text-yellow-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4 border-t border-gray-100 pt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">Email</p>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Phone</p>
                            <p class="text-sm text-gray-900">+****************</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Department</p>
                            <p class="text-sm text-gray-900">Support</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Location</p>
                            <p class="text-sm text-gray-900">Miami, FL</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Email
                    </button>
                    <button class="btn btn-sm btn-secondary flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4 mr-1">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="flex items-center justify-between mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div class="text-sm text-gray-500">
            Showing <span class="font-medium">1</span> to <span class="font-medium">6</span> of <span class="font-medium">24</span> contacts
        </div>
        <div class="flex space-x-1">
            <button class="btn btn-sm btn-secondary" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
            <button class="btn btn-sm btn-primary">1</button>
            <button class="btn btn-sm btn-secondary">2</button>
            <button class="btn btn-sm btn-secondary">3</button>
            <button class="btn btn-sm btn-secondary">4</button>
            <button class="btn btn-sm btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>
    </div>

    <!-- Recent Contacts -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Recent Activity</h2>
            <button class="btn btn-sm btn-secondary">View All</button>
        </div>
        <div class="card-body p-0">
            <ul class="divide-y divide-gray-200">
                <li class="p-4 flex">
                    <div class="flex-shrink-0 mr-3">
                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Sarah+Johnson&background=2563EB&color=fff" alt="Sarah Johnson">
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            You added <span class="font-medium">Sarah Johnson</span> to your contacts
                        </p>
                        <p class="text-xs text-gray-500 mt-1">2 hours ago</p>
                    </div>
                </li>
                <li class="p-4 flex">
                    <div class="flex-shrink-0 mr-3">
                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=John+Smith&background=0D8ABC&color=fff" alt="John Smith">
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            You called <span class="font-medium">John Smith</span>
                        </p>
                        <p class="text-xs text-gray-500 mt-1">Yesterday at 3:45 PM</p>
                    </div>
                </li>
                <li class="p-4 flex">
                    <div class="flex-shrink-0 mr-3">
                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Emily+Davis&background=DC2626&color=fff" alt="Emily Davis">
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            You sent an email to <span class="font-medium">Emily Davis</span>
                        </p>
                        <p class="text-xs text-gray-500 mt-1">July 14, 2023</p>
                    </div>
                </li>
                <li class="p-4 flex">
                    <div class="flex-shrink-0 mr-3">
                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Michael+Brown&background=059669&color=fff" alt="Michael Brown">
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            You updated <span class="font-medium">Michael Brown</span>'s information
                        </p>
                        <p class="text-xs text-gray-500 mt-1">July 12, 2023</p>
                    </div>
                </li>
                <li class="p-4 flex">
                    <div class="flex-shrink-0 mr-3">
                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Olivia+Martinez&background=F59E0B&color=fff" alt="Olivia Martinez">
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            <span class="font-medium">Olivia Martinez</span> added you as a contact
                        </p>
                        <p class="text-xs text-gray-500 mt-1">July 10, 2023</p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
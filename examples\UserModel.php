<?php
/**
 * Ejemplo de Modelo: UserModel
 * 
 * Demostración de cómo crear un modelo usando BaseModel
 * con validaciones, relaciones y funcionalidades avanzadas
 * 
 * <AUTHOR> with <PERSON>
 * @version 1.0
 */

require_once __DIR__ . '/../core/BaseModel.php';

class UserModel extends BaseModel {
    /**
     * Nombre de la tabla
     * @var string
     */
    protected $table = 'users';
    
    /**
     * Campos asignables masivamente
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'status',
        'role'
    ];
    
    /**
     * Campos protegidos
     * @var array
     */
    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
        'email_verified_at'
    ];
    
    /**
     * Activar timestamps automáticos
     * @var bool
     */
    protected $timestamps = true;
    
    /**
     * Activar soft deletes
     * @var bool
     */
    protected $softDeletes = true;
    
    /**
     * Reglas de validación
     * @var array
     */
    protected $rules = [
        'name' => 'required|min:2|max:100',
        'email' => 'required|email',
        'password' => 'required|min:8',
        'phone' => 'min:10|max:15',
        'status' => 'required',
        'role' => 'required'
    ];
    
    /**
     * Mensajes de validación personalizados
     * @var array
     */
    protected $messages = [
        'name.required' => 'El nombre es obligatorio',
        'name.min' => 'El nombre debe tener al menos 2 caracteres',
        'email.required' => 'El email es obligatorio',
        'email.email' => 'El email debe tener un formato válido',
        'password.required' => 'La contraseña es obligatoria',
        'password.min' => 'La contraseña debe tener al menos 8 caracteres'
    ];
    
    /**
     * Encriptar password antes de guardar
     * 
     * @param string $password
     * @return $this
     */
    public function setPassword(string $password): self {
        $this->setAttribute('password', password_hash($password, PASSWORD_BCRYPT));
        return $this;
    }
    
    /**
     * Verificar password
     * 
     * @param string $password
     * @return bool
     */
    public function verifyPassword(string $password): bool {
        return password_verify($password, $this->getAttribute('password'));
    }
    
    /**
     * Scope para usuarios activos
     * 
     * @param QueryBuilder $query
     * @return QueryBuilder
     */
    public function scopeActive(QueryBuilder $query): QueryBuilder {
        return $query->where('status', 'active');
    }
    
    /**
     * Scope para usuarios por rol
     * 
     * @param QueryBuilder $query
     * @param string $role
     * @return QueryBuilder
     */
    public function scopeByRole(QueryBuilder $query, string $role): QueryBuilder {
        return $query->where('role', $role);
    }
    
    /**
     * Scope para buscar usuarios
     * 
     * @param QueryBuilder $query
     * @param string $search
     * @return QueryBuilder
     */
    public function scopeSearch(QueryBuilder $query, string $search): QueryBuilder {
        return $query->where(function($q) use ($search) {
            $q->where('name', 'like', "%$search%")
              ->orWhere('email', 'like', "%$search%");
        });
    }
    
    /**
     * Obtener nombre completo formateado
     * 
     * @return string
     */
    public function getFullName(): string {
        return trim($this->getAttribute('name'));
    }
    
    /**
     * Verificar si el usuario es administrador
     * 
     * @return bool
     */
    public function isAdmin(): bool {
        return $this->getAttribute('role') === 'admin';
    }
    
    /**
     * Verificar si el usuario está activo
     * 
     * @return bool
     */
    public function isActive(): bool {
        return $this->getAttribute('status') === 'active';
    }
    
    /**
     * Obtener proyectos del usuario (relación)
     * 
     * @return array
     * @throws Exception
     */
    public function getProjects(): array {
        return static::query()
            ->select('p.*')
            ->from('projects as p')
            ->join('user_projects as up', 'p.id', '=', 'up.project_id')
            ->where('up.user_id', $this->getAttribute('id'))
            ->where('p.status', 'active')
            ->get();
    }
    
    /**
     * Convertir a array (sobrescribir para ocultar campos sensibles)
     * 
     * @return array
     */
    public function toArray(): array {
        $attributes = parent::toArray();
        
        // Ocultar campos sensibles
        unset($attributes['password']);
        
        // Añadir campos calculados
        $attributes['full_name'] = $this->getFullName();
        $attributes['is_admin'] = $this->isAdmin();
        $attributes['is_active'] = $this->isActive();
        
        return $attributes;
    }
}
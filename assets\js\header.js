/**
 * FuseHeader JavaScript
 * 
 * Implementa la funcionalidad interactiva para el componente FuseHeader
 */

document.addEventListener('DOMContentLoaded', function() {
    initHeader();
});

/**
 * Inicializa todas las funcionalidades del header
 */
function initHeader() {
    initLanguageDropdown();
    initFullscreenToggle();
    initThemeToggle();
    initNotificationsToggle();
    initCanvasLateralToggle();
    initPanelsDefaults();
}

/**
 * Configura los paneles para estar ocultos por defecto
 */
function initPanelsDefaults() {
    const notificationsCanvas = document.getElementById('notificationsCanvas');
    const canvasLateral = document.getElementById('canvasLateral');
    
    // Ocultar paneles por defecto
    if (notificationsCanvas) {
        notificationsCanvas.style.display = 'none';
    }
    if (canvasLateral) {
        canvasLateral.style.display = 'none';
    }
}

/**
 * Inicializa el menú desplegable de idiomas
 */
function initLanguageDropdown() {
    // Selector del botón de idioma
    const languageButton = document.querySelector('.language-dropdown button');
    // Selector del menú desplegable
    const dropdown = document.querySelector('.language-dropdown-content');
    // Selectores de todos los elementos del menú
    const languageItems = document.querySelectorAll('.language-item');
    
    if (!languageButton || !dropdown) return;

    // Mostrar/ocultar el menú al hacer clic en el botón
    languageButton.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdown.classList.toggle('hidden');
    });

    // Cerrar el menú al hacer clic fuera de él
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target) && !languageButton.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });

    // Manejar la selección de un idioma
    languageItems.forEach(item => {
        item.addEventListener('click', function() {
            const langCode = this.dataset.langCode;
            const langFlag = this.querySelector('span').textContent.trim();
            
            // Actualizar el botón con el idioma seleccionado
            languageButton.innerHTML = `${langFlag} ${langCode}`;
            
            // Cerrar el menú desplegable
            dropdown.classList.add('hidden');

            // Aquí se podría enviar una solicitud AJAX para cambiar el idioma en el servidor
            console.log(`Idioma cambiado a: ${langCode}`);
        });
    });
}

/**
 * Inicializa el botón de pantalla completa
 */
function initFullscreenToggle() {
    const fullscreenButton = document.querySelector('.fullscreen-toggle');
    
    if (!fullscreenButton) return;

    fullscreenButton.addEventListener('click', function() {
        toggleFullscreen();
    });
}

/**
 * Función para alternar pantalla completa
 */
function toggleFullscreen() {
    if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {
        // Entrar en pantalla completa
        const element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen().catch(err => {
                console.error(`Error al intentar entrar en modo pantalla completa: ${err.message}`);
            });
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    } else {
        // Salir de pantalla completa
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
}

/**
 * Inicializa el botón de cambio de tema (claro/oscuro)
 */
function initThemeToggle() {
    const themeButton = document.querySelector('button svg[d*="M12 3v2.25m6.364.386"]').parentElement;
    let darkMode = localStorage.getItem('darkMode') === 'true';
    
    if (!themeButton) return;

    // Aplicar tema guardado al cargar la página
    if (darkMode) {
        document.documentElement.classList.add('dark-theme');
    }

    themeButton.addEventListener('click', function() {
        darkMode = !darkMode;
        
        if (darkMode) {
            document.documentElement.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
        }
        
        // Guardar preferencia
        localStorage.setItem('darkMode', darkMode);
    });
}

/**
 * Inicializa el botón de notificaciones
 */
function initNotificationsToggle() {
    const notificationButton = document.querySelector('.notification-toggle');
    
    if (!notificationButton) return;

    notificationButton.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleNotifications();
    });

    // Cerrar panel al hacer clic fuera
    document.addEventListener('click', function(e) {
        const notificationsCanvas = document.getElementById('notificationsCanvas');
        if (notificationsCanvas && 
            !notificationsCanvas.contains(e.target) && 
            !notificationButton.contains(e.target)) {
            hideNotifications();
        }
    });
}

/**
 * Función para alternar el panel de notificaciones
 */
function toggleNotifications() {
    const notificationsCanvas = document.getElementById('notificationsCanvas');
    const notificationButton = document.querySelector('.notification-toggle');
    
    if (!notificationsCanvas) return;
    
    const isVisible = notificationsCanvas.style.display === 'block';
    
    if (isVisible) {
        hideNotifications();
    } else {
        showNotifications();
    }
}

/**
 * Mostrar panel de notificaciones
 */
function showNotifications() {
    const notificationsCanvas = document.getElementById('notificationsCanvas');
    const notificationButton = document.querySelector('.notification-toggle');
    
    if (notificationsCanvas) {
        notificationsCanvas.style.display = 'block';
        notificationsCanvas.classList.add('active');
    }
    
    if (notificationButton) {
        notificationButton.classList.add('active');
    }
    
    // Cerrar canvas lateral si está abierto
    hideCanvasLateral();
}

/**
 * Ocultar panel de notificaciones
 */
function hideNotifications() {
    const notificationsCanvas = document.getElementById('notificationsCanvas');
    const notificationButton = document.querySelector('.notification-toggle');
    
    if (notificationsCanvas) {
        notificationsCanvas.style.display = 'none';
        notificationsCanvas.classList.remove('active');
    }
    
    if (notificationButton) {
        notificationButton.classList.remove('active');
    }
}

/**
 * Inicializa el botón del canvas lateral
 */
function initCanvasLateralToggle() {
    const canvasButton = document.querySelector('.canvas-lateral-toggle');
    
    if (!canvasButton) return;

    canvasButton.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleCanvasLateral();
    });

    // Cerrar panel al hacer clic fuera
    document.addEventListener('click', function(e) {
        const canvasLateral = document.getElementById('canvasLateral');
        if (canvasLateral && 
            !canvasLateral.contains(e.target) && 
            !canvasButton.contains(e.target)) {
            hideCanvasLateral();
        }
    });
}

/**
 * Función para alternar el canvas lateral
 */
function toggleCanvasLateral() {
    const canvasLateral = document.getElementById('canvasLateral');
    const canvasButton = document.querySelector('.canvas-lateral-toggle');
    
    if (!canvasLateral) return;
    
    const isVisible = canvasLateral.style.display === 'block';
    
    if (isVisible) {
        hideCanvasLateral();
    } else {
        showCanvasLateral();
    }
}

/**
 * Mostrar canvas lateral
 */
function showCanvasLateral() {
    const canvasLateral = document.getElementById('canvasLateral');
    const canvasButton = document.querySelector('.canvas-lateral-toggle');
    
    if (canvasLateral) {
        canvasLateral.style.display = 'block';
        canvasLateral.classList.add('active');
    }
    
    if (canvasButton) {
        canvasButton.classList.add('active');
    }
    
    // Cerrar panel de notificaciones si está abierto
    hideNotifications();
}

/**
 * Ocultar canvas lateral
 */
function hideCanvasLateral() {
    const canvasLateral = document.getElementById('canvasLateral');
    const canvasButton = document.querySelector('.canvas-lateral-toggle');
    
    if (canvasLateral) {
        canvasLateral.style.display = 'none';
        canvasLateral.classList.remove('active');
    }
    
    if (canvasButton) {
        canvasButton.classList.remove('active');
    }
}

// Hacer las funciones disponibles globalmente para compatibilidad
window.toggleFullscreen = toggleFullscreen;
window.toggleNotifications = toggleNotifications;
window.toggleCanvasLateral = toggleCanvasLateral;
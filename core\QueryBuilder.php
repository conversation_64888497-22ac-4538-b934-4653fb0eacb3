<?php
/**
 * QueryBuilder - Constructor de Consultas SQL
 * 
 * Clase para construir consultas SQL de forma fluida y segura:
 * - Interfaz fluida para construcción de queries
 * - Prepared statements automáticos
 * - Soporte para JOINs, WHEREs complejos, agrupaciones
 * - Paginación integrada
 * - Cache de queries
 * - Logging y profiling
 * 
 * <AUTHOR> with Claude <PERSON>
 * @version 1.0
 */

class QueryBuilder {
    /**
     * DatabaseManager instance
     * @var DatabaseManager
     */
    private $db;
    
    /**
     * Tabla principal
     * @var string
     */
    private $table;
    
    /**
     * Campos SELECT
     * @var array
     */
    private $select = ['*'];
    
    /**
     * Cláusulas WHERE
     * @var array
     */
    private $wheres = [];
    
    /**
     * Cláusulas JOIN
     * @var array
     */
    private $joins = [];
    
    /**
     * Cláusulas ORDER BY
     * @var array
     */
    private $orders = [];
    
    /**
     * Cláusulas GROUP BY
     * @var array
     */
    private $groups = [];
    
    /**
     * Cláusulas HAVING
     * @var array
     */
    private $havings = [];
    
    /**
     * LIMIT
     * @var int|null
     */
    private $limit;
    
    /**
     * OFFSET
     * @var int|null
     */
    private $offset;
    
    /**
     * Parámetros para prepared statements
     * @var array
     */
    private $bindings = [];
    
    /**
     * Operadores WHERE válidos
     * @var array
     */
    private $operators = [
        '=', '<', '>', '<=', '>=', '<>', '!=', '<=>',
        'like', 'like binary', 'not like', 'ilike',
        '&', '|', '^', '<<', '>>',
        'rlike', 'regexp', 'not regexp',
        '~', '~*', '!~', '!~*', 'similar to',
        'not similar to', 'not ilike', '~~*', '!~~*',
    ];
    
    /**
     * Constructor
     * 
     * @param DatabaseManager $db
     */
    public function __construct(DatabaseManager $db) {
        $this->db = $db;
    }
    
    /**
     * Establecer tabla
     * 
     * @param string $table
     * @return $this
     */
    public function table(string $table): self {
        $this->table = $table;
        return $this;
    }
    
    /**
     * Establecer campos SELECT
     * 
     * @param mixed ...$columns
     * @return $this
     */
    public function select(...$columns): self {
        if (empty($columns)) {
            $this->select = ['*'];
        } else {
            $this->select = is_array($columns[0]) ? $columns[0] : $columns;
        }
        
        return $this;
    }
    
    /**
     * Añadir campo SELECT
     * 
     * @param mixed ...$columns
     * @return $this
     */
    public function addSelect(...$columns): self {
        $columns = is_array($columns[0]) ? $columns[0] : $columns;
        
        foreach ($columns as $column) {
            if (!in_array($column, $this->select)) {
                $this->select[] = $column;
            }
        }
        
        return $this;
    }
    
    /**
     * Añadir cláusula WHERE
     * 
     * @param string $column
     * @param mixed $operator
     * @param mixed $value
     * @param string $boolean
     * @return $this
     */
    public function where(string $column, $operator = null, $value = null, string $boolean = 'and'): self {
        // Si solo se pasan 2 parámetros, asumir operador '='
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        if (!in_array(strtolower($operator), $this->operators)) {
            throw new InvalidArgumentException("Operador no válido: $operator");
        }
        
        $this->wheres[] = [
            'type' => 'basic',
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * Añadir cláusula WHERE con OR
     * 
     * @param string $column
     * @param mixed $operator
     * @param mixed $value
     * @return $this
     */
    public function orWhere(string $column, $operator = null, $value = null): self {
        return $this->where($column, $operator, $value, 'or');
    }
    
    /**
     * WHERE IN
     * 
     * @param string $column
     * @param array $values
     * @param string $boolean
     * @return $this
     */
    public function whereIn(string $column, array $values, string $boolean = 'and'): self {
        $this->wheres[] = [
            'type' => 'in',
            'column' => $column,
            'values' => $values,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * WHERE NOT IN
     * 
     * @param string $column
     * @param array $values
     * @param string $boolean
     * @return $this
     */
    public function whereNotIn(string $column, array $values, string $boolean = 'and'): self {
        $this->wheres[] = [
            'type' => 'not_in',
            'column' => $column,
            'values' => $values,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * WHERE NULL
     * 
     * @param string $column
     * @param string $boolean
     * @return $this
     */
    public function whereNull(string $column, string $boolean = 'and'): self {
        $this->wheres[] = [
            'type' => 'null',
            'column' => $column,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * WHERE NOT NULL
     * 
     * @param string $column
     * @param string $boolean
     * @return $this
     */
    public function whereNotNull(string $column, string $boolean = 'and'): self {
        $this->wheres[] = [
            'type' => 'not_null',
            'column' => $column,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * WHERE BETWEEN
     * 
     * @param string $column
     * @param mixed $from
     * @param mixed $to
     * @param string $boolean
     * @return $this
     */
    public function whereBetween(string $column, $from, $to, string $boolean = 'and'): self {
        $this->wheres[] = [
            'type' => 'between',
            'column' => $column,
            'from' => $from,
            'to' => $to,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * INNER JOIN
     * 
     * @param string $table
     * @param string $first
     * @param string $operator
     * @param string $second
     * @return $this
     */
    public function join(string $table, string $first, string $operator = '=', string $second = null): self {
        return $this->addJoin('inner', $table, $first, $operator, $second);
    }
    
    /**
     * LEFT JOIN
     * 
     * @param string $table
     * @param string $first
     * @param string $operator
     * @param string $second
     * @return $this
     */
    public function leftJoin(string $table, string $first, string $operator = '=', string $second = null): self {
        return $this->addJoin('left', $table, $first, $operator, $second);
    }
    
    /**
     * RIGHT JOIN
     * 
     * @param string $table
     * @param string $first
     * @param string $operator
     * @param string $second
     * @return $this
     */
    public function rightJoin(string $table, string $first, string $operator = '=', string $second = null): self {
        return $this->addJoin('right', $table, $first, $operator, $second);
    }
    
    /**
     * Añadir JOIN
     * 
     * @param string $type
     * @param string $table
     * @param string $first
     * @param string $operator
     * @param string $second
     * @return $this
     */
    private function addJoin(string $type, string $table, string $first, string $operator, ?string $second): self {
        $this->joins[] = [
            'type' => $type,
            'table' => $table,
            'first' => $first,
            'operator' => $operator,
            'second' => $second
        ];
        
        return $this;
    }
    
    /**
     * ORDER BY
     * 
     * @param string $column
     * @param string $direction
     * @return $this
     */
    public function orderBy(string $column, string $direction = 'asc'): self {
        $direction = strtolower($direction);
        
        if (!in_array($direction, ['asc', 'desc'])) {
            throw new InvalidArgumentException("Dirección de orden no válida: $direction");
        }
        
        $this->orders[] = [
            'column' => $column,
            'direction' => $direction
        ];
        
        return $this;
    }
    
    /**
     * ORDER BY DESC
     * 
     * @param string $column
     * @return $this
     */
    public function orderByDesc(string $column): self {
        return $this->orderBy($column, 'desc');
    }
    
    /**
     * GROUP BY
     * 
     * @param mixed ...$columns
     * @return $this
     */
    public function groupBy(...$columns): self {
        $columns = is_array($columns[0]) ? $columns[0] : $columns;
        $this->groups = array_merge($this->groups, $columns);
        return $this;
    }
    
    /**
     * HAVING
     * 
     * @param string $column
     * @param mixed $operator
     * @param mixed $value
     * @param string $boolean
     * @return $this
     */
    public function having(string $column, $operator = null, $value = null, string $boolean = 'and'): self {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->havings[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'boolean' => $boolean
        ];
        
        return $this;
    }
    
    /**
     * LIMIT
     * 
     * @param int $limit
     * @return $this
     */
    public function limit(int $limit): self {
        $this->limit = max(0, $limit);
        return $this;
    }
    
    /**
     * OFFSET
     * 
     * @param int $offset
     * @return $this
     */
    public function offset(int $offset): self {
        $this->offset = max(0, $offset);
        return $this;
    }
    
    /**
     * TAKE (alias para limit)
     * 
     * @param int $limit
     * @return $this
     */
    public function take(int $limit): self {
        return $this->limit($limit);
    }
    
    /**
     * SKIP (alias para offset)
     * 
     * @param int $offset
     * @return $this
     */
    public function skip(int $offset): self {
        return $this->offset($offset);
    }
    
    /**
     * Ejecutar SELECT y obtener todos los resultados
     * 
     * @return array
     * @throws Exception
     */
    public function get(): array {
        $sql = $this->buildSelectSql();
        $stmt = $this->db->query($sql, $this->bindings);
        return $stmt->fetchAll();
    }
    
    /**
     * Ejecutar SELECT y obtener primer resultado
     * 
     * @return array|null
     * @throws Exception
     */
    public function first(): ?array {
        $this->limit(1);
        $results = $this->get();
        return $results[0] ?? null;
    }
    
    /**
     * Contar registros
     * 
     * @return int
     * @throws Exception
     */
    public function count(): int {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        
        $result = $this->first();
        $this->select = $originalSelect;
        
        return (int) ($result['count'] ?? 0);
    }
    
    /**
     * Verificar si existen registros
     * 
     * @return bool
     * @throws Exception
     */
    public function exists(): bool {
        return $this->count() > 0;
    }
    
    /**
     * Insertar registro
     * 
     * @param array $data
     * @return bool
     * @throws Exception
     */
    public function insert(array $data): bool {
        if (empty($data)) {
            return false;
        }
        
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->db->query($sql, array_values($data));
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Actualizar registros
     * 
     * @param array $data
     * @return bool
     * @throws Exception
     */
    public function update(array $data): bool {
        if (empty($data)) {
            return false;
        }
        
        $sets = [];
        $bindings = [];
        
        foreach ($data as $column => $value) {
            $sets[] = "$column = ?";
            $bindings[] = $value;
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $sets);
        $sql .= $this->buildWhereSql($bindings);
        
        $stmt = $this->db->query($sql, $bindings);
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Eliminar registros
     * 
     * @return bool
     * @throws Exception
     */
    public function delete(): bool {
        $sql = "DELETE FROM {$this->table}";
        $sql .= $this->buildWhereSql($this->bindings);
        
        $stmt = $this->db->query($sql, $this->bindings);
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Paginación
     * 
     * @param int $page
     * @param int $perPage
     * @return array
     * @throws Exception
     */
    public function paginate(int $page = 1, int $perPage = 15): array {
        $page = max(1, $page);
        $offset = ($page - 1) * $perPage;
        
        // Obtener total de registros
        $total = $this->count();
        
        // Obtener registros de la página actual
        $this->limit($perPage)->offset($offset);
        $data = $this->get();
        
        return [
            'data' => $data,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => (int) ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
    
    /**
     * Construir SQL SELECT
     * 
     * @return string
     */
    private function buildSelectSql(): string {
        $sql = 'SELECT ' . implode(', ', $this->select);
        $sql .= " FROM {$this->table}";
        
        // JOINs
        if (!empty($this->joins)) {
            foreach ($this->joins as $join) {
                $sql .= " " . strtoupper($join['type']) . " JOIN {$join['table']} ON {$join['first']} {$join['operator']} {$join['second']}";
            }
        }
        
        // WHERE
        $sql .= $this->buildWhereSql($this->bindings);
        
        // GROUP BY
        if (!empty($this->groups)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groups);
        }
        
        // HAVING
        if (!empty($this->havings)) {
            $havingClauses = [];
            foreach ($this->havings as $i => $having) {
                $boolean = $i === 0 ? '' : ' ' . strtoupper($having['boolean']);
                $havingClauses[] = $boolean . " {$having['column']} {$having['operator']} ?";
                $this->bindings[] = $having['value'];
            }
            $sql .= ' HAVING' . implode('', $havingClauses);
        }
        
        // ORDER BY
        if (!empty($this->orders)) {
            $orderClauses = array_map(function($order) {
                return "{$order['column']} " . strtoupper($order['direction']);
            }, $this->orders);
            $sql .= ' ORDER BY ' . implode(', ', $orderClauses);
        }
        
        // LIMIT
        if ($this->limit !== null) {
            $sql .= " LIMIT {$this->limit}";
        }
        
        // OFFSET
        if ($this->offset !== null) {
            $sql .= " OFFSET {$this->offset}";
        }
        
        return $sql;
    }
    
    /**
     * Construir cláusulas WHERE
     * 
     * @param array &$bindings
     * @return string
     */
    private function buildWhereSql(array &$bindings): string {
        if (empty($this->wheres)) {
            return '';
        }
        
        $sql = ' WHERE';
        $whereClauses = [];
        
        foreach ($this->wheres as $i => $where) {
            $boolean = $i === 0 ? '' : ' ' . strtoupper($where['boolean']);
            
            switch ($where['type']) {
                case 'basic':
                    $whereClauses[] = $boolean . " {$where['column']} {$where['operator']} ?";
                    $bindings[] = $where['value'];
                    break;
                
                case 'in':
                    $placeholders = str_repeat('?,', count($where['values']) - 1) . '?';
                    $whereClauses[] = $boolean . " {$where['column']} IN ($placeholders)";
                    $bindings = array_merge($bindings, $where['values']);
                    break;
                
                case 'not_in':
                    $placeholders = str_repeat('?,', count($where['values']) - 1) . '?';
                    $whereClauses[] = $boolean . " {$where['column']} NOT IN ($placeholders)";
                    $bindings = array_merge($bindings, $where['values']);
                    break;
                
                case 'null':
                    $whereClauses[] = $boolean . " {$where['column']} IS NULL";
                    break;
                
                case 'not_null':
                    $whereClauses[] = $boolean . " {$where['column']} IS NOT NULL";
                    break;
                
                case 'between':
                    $whereClauses[] = $boolean . " {$where['column']} BETWEEN ? AND ?";
                    $bindings[] = $where['from'];
                    $bindings[] = $where['to'];
                    break;
            }
        }
        
        return $sql . implode('', $whereClauses);
    }
    
    /**
     * Clonar query builder para nuevas consultas
     * 
     * @return static
     */
    public function clone(): self {
        return clone $this;
    }
    
    /**
     * Reset query builder
     * 
     * @return $this
     */
    public function reset(): self {
        $this->select = ['*'];
        $this->wheres = [];
        $this->joins = [];
        $this->orders = [];
        $this->groups = [];
        $this->havings = [];
        $this->limit = null;
        $this->offset = null;
        $this->bindings = [];
        
        return $this;
    }
    
    /**
     * Obtener SQL generado (para debug)
     * 
     * @return string
     */
    public function toSql(): string {
        $sql = $this->buildSelectSql();
        
        // Reemplazar placeholders con valores reales para debug
        foreach ($this->bindings as $binding) {
            $value = is_string($binding) ? "'$binding'" : $binding;
            $sql = preg_replace('/\?/', $value, $sql, 1);
        }
        
        return $sql;
    }
}
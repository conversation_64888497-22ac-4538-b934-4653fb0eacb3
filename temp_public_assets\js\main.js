/**
 * Archivo principal de JavaScript
 * 
 * Inicializa todos los componentes y funcionalidades del sistema
 */

// Cargar módulos
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar sistema principal
    initApp();
});

/**
 * Inicializa la aplicación
 */
function initApp() {
    console.log('Inicializando aplicación...');
    
    // Obtener configuración inicial
    const config = window.appConfig || {
        initialPage: 'project',
        baseUrl: '/',
        debug: false
    };
    
    if (config.debug) {
        console.log('Configuración:', config);
    }
    
    // Cargar componentes
    loadComponents(config);
    
    // Inicializar eventos globales
    setupGlobalEvents();
    
    console.log('Aplicación inicializada correctamente');
}

/**
 * Carga los componentes de la aplicación
 * 
 * @param {Object} config Configuración de la aplicación
 */
function loadComponents(config) {
    // Cargar componentes JS
    loadScript('assets/components/header/header.js', function() {
        // Inicializar header cuando el script esté cargado
        if (typeof initHeader === 'function') {
            initHeader();
        }
    });
    
    loadScript('assets/components/sidebar/sidebar.js', function() {
        // Inicializar sidebar cuando el script esté cargado
        if (typeof initFuseSidebar === 'function') {
            fuseSidebarInstance = initFuseSidebar({
                activeItem: config.initialPage || getCurrentPage(),
                navigationMode: 'ajax',
                baseUrl: config.baseUrl || window.location.pathname,
                onItemClick: handleNavigation,
                debug: config.debug || false
            });
        }
    });
    
    loadScript('assets/js/core/navigation.js', function() {
        // Inicializar navegación cuando el script esté cargado
        if (typeof initNavigation === 'function') {
            initNavigation({
                initialPage: config.initialPage || getCurrentPage(),
                baseUrl: config.baseUrl || window.location.pathname
            });
        }
    });
    
    // Cargar utilidades
    loadScript('assets/js/utils/ajax.js');
    
    // Inicializar otros componentes específicos de la página actual
    setTimeout(initPageComponents, 500);
}

/**
 * Configura eventos globales
 */
function setupGlobalEvents() {
    // Manejar clics en enlaces internos para navegación SPA
    document.addEventListener('click', function(event) {
        const link = event.target.closest('a[data-spa-link]');
        if (link) {
            event.preventDefault();
            const page = link.dataset.page || link.getAttribute('href').replace(/^\/|\/$/g, '');
            handleNavigation(page);
        }
    });
    
    // Manejar envío de formularios AJAX
    document.addEventListener('submit', function(event) {
        const form = event.target.closest('form[data-ajax-form]');
        if (form) {
            event.preventDefault();
            handleAjaxForm(form);
        }
    });
    
    // Detectar cambios de tema
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (e.matches) {
            document.documentElement.classList.add('dark-theme');
            localStorage.setItem('darkMode', 'true');
        } else {
            document.documentElement.classList.remove('dark-theme');
            localStorage.setItem('darkMode', 'false');
        }
    });
    
    // Aplicar tema guardado o predeterminado
    if (localStorage.getItem('darkMode') === 'true') {
        document.documentElement.classList.add('dark-theme');
    }
}

/**
 * Obtiene la página actual desde la URL
 * 
 * @returns {string} Nombre de la página actual
 */
function getCurrentPage() {
    const path = window.location.pathname;
    const page = path.split('/').filter(Boolean).pop() || 'project';
    return page;
}

/**
 * Inicializa componentes específicos de la página actual
 */
function initPageComponents() {
    // Inicializar gráficos si la página los contiene
    initCharts();
    
    // Inicializar tablas de datos si la página las contiene
    initDataTables();
    
    // Inicializar calendario si la página lo contiene
    initCalendar();
}

/**
 * Inicializa gráficos de Chart.js si existen en la página
 */
function initCharts() {
    const chartContainers = document.querySelectorAll('.chart-container[data-chart]');
    if (chartContainers.length === 0) return;
    
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js no está cargado');
        return;
    }
    
    chartContainers.forEach(container => {
        try {
            const chartType = container.dataset.chartType || 'line';
            const chartData = JSON.parse(container.dataset.chartData || '{}');
            const chartOptions = JSON.parse(container.dataset.chartOptions || '{}');
            
            const canvas = container.querySelector('canvas');
            if (!canvas) return;
            
            new Chart(canvas, {
                type: chartType,
                data: chartData,
                options: chartOptions
            });
        } catch (error) {
            console.error('Error al inicializar gráfico:', error);
        }
    });
}

/**
 * Inicializa tablas de datos si existen en la página
 */
function initDataTables() {
    const tables = document.querySelectorAll('.data-table[data-sortable]');
    if (tables.length === 0) return;
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        
        headers.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const direction = this.dataset.sortDirection === 'asc' ? 'desc' : 'asc';
                
                // Reiniciar todos los headers
                headers.forEach(h => {
                    h.dataset.sortDirection = '';
                    h.classList.remove('sorting-asc', 'sorting-desc');
                });
                
                // Establecer dirección en el header actual
                this.dataset.sortDirection = direction;
                this.classList.add(direction === 'asc' ? 'sorting-asc' : 'sorting-desc');
                
                // Ordenar la tabla
                sortTable(table, column, direction);
            });
        });
    });
}

/**
 * Ordena una tabla por una columna
 * 
 * @param {HTMLTableElement} table Tabla a ordenar
 * @param {string} column Nombre de la columna a ordenar
 * @param {string} direction Dirección ('asc' o 'desc')
 */
function sortTable(table, column, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const sortedRows = rows.sort((a, b) => {
        const aValue = a.querySelector(`td[data-column="${column}"]`).textContent.trim();
        const bValue = b.querySelector(`td[data-column="${column}"]`).textContent.trim();
        
        // Determinar tipo de datos
        if (!isNaN(parseFloat(aValue)) && !isNaN(parseFloat(bValue))) {
            // Numérico
            return direction === 'asc' 
                ? parseFloat(aValue) - parseFloat(bValue)
                : parseFloat(bValue) - parseFloat(aValue);
        } else {
            // Texto
            return direction === 'asc'
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        }
    });
    
    // Limpiar tabla
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }
    
    // Añadir filas ordenadas
    sortedRows.forEach(row => {
        tbody.appendChild(row);
    });
}

/**
 * Inicializa el calendario si existe en la página
 */
function initCalendar() {
    const calendarContainer = document.querySelector('.calendar-container');
    if (!calendarContainer) return;
    
    if (typeof luxon === 'undefined') {
        console.warn('Luxon no está cargado');
        return;
    }
    
    // Implementar calendario
    console.log('Inicializando calendario...');
}

/**
 * Maneja el envío de formularios AJAX
 * 
 * @param {HTMLFormElement} form Formulario a enviar
 */
function handleAjaxForm(form) {
    const url = form.action;
    const method = form.method.toUpperCase();
    const formData = new FormData(form);
    
    // Mostrar indicador de carga
    const submitButton = form.querySelector('[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<div class="loader"></div> Procesando...';
    
    // Realizar petición
    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Manejar respuesta exitosa
        if (data.redirect) {
            // Redireccionar
            window.location.href = data.redirect;
        } else if (data.message) {
            // Mostrar mensaje
            showNotification(data.message, 'success');
        }
        
        // Si hay un callback especificado, ejecutarlo
        if (form.dataset.callback && window[form.dataset.callback]) {
            window[form.dataset.callback](data);
        }
        
        // Resetear formulario si se especificó
        if (form.dataset.reset === 'true') {
            form.reset();
        }
    })
    .catch(error => {
        // Manejar error
        console.error('Error en envío de formulario:', error);
        showNotification(error.message || 'Error al procesar el formulario', 'error');
    })
    .finally(() => {
        // Restaurar botón
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    });
}

/**
 * Muestra una notificación
 * 
 * @param {string} message Mensaje a mostrar
 * @param {string} type Tipo de notificación ('success', 'error', 'warning', 'info')
 * @param {number} duration Duración en ms (por defecto 5000)
 */
function showNotification(message, type = 'info', duration = 5000) {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon"></div>
            <div class="notification-message">${message}</div>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Añadir al contenedor de notificaciones (crearlo si no existe)
    let notificationsContainer = document.querySelector('.notifications-container');
    if (!notificationsContainer) {
        notificationsContainer = document.createElement('div');
        notificationsContainer.className = 'notifications-container';
        document.body.appendChild(notificationsContainer);
    }
    
    notificationsContainer.appendChild(notification);
    
    // Mostrar con animación
    setTimeout(() => {
        notification.classList.add('notification-show');
    }, 10);
    
    // Configurar cierre
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        closeNotification(notification);
    });
    
    // Auto cerrar después de duration
    setTimeout(() => {
        closeNotification(notification);
    }, duration);
}

/**
 * Cierra una notificación con animación
 * 
 * @param {HTMLElement} notification Elemento de notificación
 */
function closeNotification(notification) {
    notification.classList.remove('notification-show');
    notification.classList.add('notification-hide');
    
    // Eliminar después de la animación
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Carga un script JavaScript de forma dinámica
 * 
 * @param {string} url URL del script a cargar
 * @param {Function} callback Función a ejecutar cuando el script esté cargado
 */
function loadScript(url, callback) {
    // Verificar si el script ya está cargado
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
        if (callback) callback();
        return;
    }
    
    // Crear elemento script
    const script = document.createElement('script');
    script.src = url;
    script.async = true;
    
    // Configurar callback
    if (callback) {
        script.onload = callback;
    }
    
    // Añadir al documento
    document.head.appendChild(script);
}

// Definición temporal de handleNavigation para evitar errores hasta que se cargue navigation.js
if (typeof handleNavigation === 'undefined') {
    window.handleNavigation = function(page) {
        console.log('Navigation handler will be loaded soon...');
    };
}
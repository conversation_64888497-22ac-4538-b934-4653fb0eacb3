<?php
/**
 * DatabaseManager - Gestor de Base de Datos con Patr<PERSON> Singleton
 * 
 * Clase principal para gestionar conexiones MySQL con:
 * - Patrón Singleton para control de instancias
 * - Pool de conexiones para optimización
 * - Manejo robusto de errores y reconexión automática
 * - Soporte para transacciones y logging
 * - Configuración por ambiente
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

class DatabaseManager {
    /**
     * Instancia única del DatabaseManager
     * @var DatabaseManager|null
     */
    private static $instance = null;
    
    /**
     * Pool de conexiones PDO
     * @var array
     */
    private $connectionPool = [];
    
    /**
     * Configuración de la base de datos
     * @var array
     */
    private $config = [];
    
    /**
     * Conexión principal
     * @var PDO|null
     */
    private $primaryConnection = null;
    
    /**
     * Contador de reconexiones
     * @var int
     */
    private $reconnectAttempts = 0;
    
    /**
     * Máximo número de intentos de reconexión
     * @var int
     */
    private $maxReconnectAttempts = 3;
    
    /**
     * Estado de logging
     * @var bool
     */
    private $loggingEnabled = false;
    
    /**
     * Log de queries ejecutadas
     * @var array
     */
    private $queryLog = [];
    
    /**
     * Transacción activa
     * @var bool
     */
    private $inTransaction = false;
    
    /**
     * Constructor privado para implementar Singleton
     */
    private function __construct() {
        $this->loadConfiguration();
        $this->initializePrimaryConnection();
    }
    
    /**
     * Prevenir clonación
     */
    private function __clone() {}
    
    /**
     * Prevenir deserialización
     */
    public function __wakeup() {
        throw new Exception("No se puede deserializar el DatabaseManager");
    }
    
    /**
     * Obtener instancia única del DatabaseManager
     * 
     * @return DatabaseManager
     */
    public static function getInstance(): DatabaseManager {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Cargar configuración de base de datos
     */
    private function loadConfiguration(): void {
        $configFile = __DIR__ . '/../config/database.php';
        
        if (!file_exists($configFile)) {
            throw new Exception("Archivo de configuración de base de datos no encontrado: $configFile");
        }
        
        $this->config = require $configFile;
        
        // Validar configuración requerida
        $requiredKeys = ['host', 'username', 'password', 'database', 'charset'];
        foreach ($requiredKeys as $key) {
            if (!isset($this->config[$key])) {
                throw new Exception("Configuración requerida faltante: $key");
            }
        }
        
        // Configurar logging según el ambiente
        $this->loggingEnabled = $this->config['logging'] ?? false;
    }
    
    /**
     * Inicializar conexión principal
     */
    private function initializePrimaryConnection(): void {
        try {
            $this->primaryConnection = $this->createConnection();
            $this->reconnectAttempts = 0;
            
            if ($this->loggingEnabled) {
                $this->log("Conexión principal establecida exitosamente");
            }
            
        } catch (Exception $e) {
            $this->handleConnectionError($e);
        }
    }
    
    /**
     * Crear nueva conexión PDO
     * 
     * @return PDO
     * @throws Exception
     */
    private function createConnection(): PDO {
        $dsn = sprintf(
            "mysql:host=%s;port=%d;dbname=%s;charset=%s",
            $this->config['host'],
            $this->config['port'] ?? 3306,
            $this->config['database'],
            $this->config['charset']
        );
        
        $options = array_merge([
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_PERSISTENT => $this->config['persistent'] ?? false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']} COLLATE {$this->config['collation']}",
            PDO::ATTR_TIMEOUT => $this->config['timeout'] ?? 30,
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::MYSQL_ATTR_LOCAL_INFILE => false, // Seguridad
        ], $this->config['options'] ?? []);
        
        return new PDO($dsn, $this->config['username'], $this->config['password'], $options);
    }
    
    /**
     * Obtener conexión activa con retry automático
     * 
     * @return PDO
     * @throws Exception
     */
    public function getConnection(): PDO {
        if (!$this->isConnectionAlive($this->primaryConnection)) {
            $this->reconnect();
        }
        
        return $this->primaryConnection;
    }
    
    /**
     * Verificar si la conexión está activa
     * 
     * @param PDO|null $connection
     * @return bool
     */
    private function isConnectionAlive(?PDO $connection): bool {
        if ($connection === null) {
            return false;
        }
        
        try {
            $connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            if ($this->loggingEnabled) {
                $this->log("Conexión perdida: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Reconectar a la base de datos
     * 
     * @throws Exception
     */
    private function reconnect(): void {
        if ($this->reconnectAttempts >= $this->maxReconnectAttempts) {
            throw new Exception("Máximo número de intentos de reconexión alcanzado");
        }
        
        $this->reconnectAttempts++;
        
        if ($this->loggingEnabled) {
            $this->log("Intento de reconexión #{$this->reconnectAttempts}");
        }
        
        // Espera exponencial entre intentos
        $waitTime = pow(2, $this->reconnectAttempts - 1);
        sleep($waitTime);
        
        try {
            $this->primaryConnection = $this->createConnection();
            $this->reconnectAttempts = 0;
            
            if ($this->loggingEnabled) {
                $this->log("Reconexión exitosa");
            }
            
        } catch (Exception $e) {
            if ($this->reconnectAttempts >= $this->maxReconnectAttempts) {
                throw new Exception("Error crítico de conexión: " . $e->getMessage());
            }
            $this->reconnect(); // Reintentar recursivamente
        }
    }
    
    /**
     * Manejar errores de conexión
     * 
     * @param Exception $e
     * @throws Exception
     */
    private function handleConnectionError(Exception $e): void {
        if ($this->loggingEnabled) {
            $this->log("Error de conexión: " . $e->getMessage());
        }
        
        if ($this->reconnectAttempts < $this->maxReconnectAttempts) {
            $this->reconnect();
        } else {
            throw new Exception("Error crítico de base de datos: " . $e->getMessage());
        }
    }
    
    /**
     * Ejecutar query con manejo de errores y logging
     * 
     * @param string $sql
     * @param array $params
     * @return PDOStatement
     * @throws Exception
     */
    public function query(string $sql, array $params = []): PDOStatement {
        $startTime = microtime(true);
        
        try {
            $connection = $this->getConnection();
            $stmt = $connection->prepare($sql);
            $stmt->execute($params);
            
            if ($this->loggingEnabled) {
                $executionTime = round((microtime(true) - $startTime) * 1000, 2);
                $this->logQuery($sql, $params, $executionTime);
            }
            
            return $stmt;
            
        } catch (PDOException $e) {
            if ($this->loggingEnabled) {
                $this->log("Error en query: " . $e->getMessage() . " - SQL: $sql");
            }
            
            // Verificar si es error de conexión y reintentar
            if ($this->isConnectionError($e)) {
                $this->reconnect();
                return $this->query($sql, $params); // Reintentar una vez
            }
            
            throw new Exception("Error en consulta SQL: " . $e->getMessage());
        }
    }
    
    /**
     * Verificar si el error es de conexión
     * 
     * @param PDOException $e
     * @return bool
     */
    private function isConnectionError(PDOException $e): bool {
        $connectionErrors = [
            2006, // MySQL server has gone away
            2013, // Lost connection to MySQL server during query
            2003, // Can't connect to MySQL server
            1053, // Server shutdown in progress
        ];
        
        return in_array($e->errorInfo[1] ?? 0, $connectionErrors);
    }
    
    /**
     * Iniciar transacción
     * 
     * @return bool
     * @throws Exception
     */
    public function beginTransaction(): bool {
        if ($this->inTransaction) {
            throw new Exception("Ya hay una transacción activa");
        }
        
        $connection = $this->getConnection();
        $result = $connection->beginTransaction();
        $this->inTransaction = true;
        
        if ($this->loggingEnabled) {
            $this->log("Transacción iniciada");
        }
        
        return $result;
    }
    
    /**
     * Confirmar transacción
     * 
     * @return bool
     * @throws Exception
     */
    public function commit(): bool {
        if (!$this->inTransaction) {
            throw new Exception("No hay transacción activa para confirmar");
        }
        
        $connection = $this->getConnection();
        $result = $connection->commit();
        $this->inTransaction = false;
        
        if ($this->loggingEnabled) {
            $this->log("Transacción confirmada");
        }
        
        return $result;
    }
    
    /**
     * Revertir transacción
     * 
     * @return bool
     * @throws Exception
     */
    public function rollback(): bool {
        if (!$this->inTransaction) {
            throw new Exception("No hay transacción activa para revertir");
        }
        
        $connection = $this->getConnection();
        $result = $connection->rollBack();
        $this->inTransaction = false;
        
        if ($this->loggingEnabled) {
            $this->log("Transacción revertida");
        }
        
        return $result;
    }
    
    /**
     * Ejecutar operación dentro de transacción
     * 
     * @param callable $callback
     * @return mixed
     * @throws Exception
     */
    public function transaction(callable $callback) {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * Obtener último ID insertado
     * 
     * @return string
     */
    public function lastInsertId(): string {
        return $this->getConnection()->lastInsertId();
    }
    
    /**
     * Logging de queries
     * 
     * @param string $sql
     * @param array $params
     * @param float $executionTime
     */
    private function logQuery(string $sql, array $params, float $executionTime): void {
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime . 'ms',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Limitar log a las últimas 100 queries
        if (count($this->queryLog) > 100) {
            array_shift($this->queryLog);
        }
    }
    
    /**
     * Obtener log de queries
     * 
     * @return array
     */
    public function getQueryLog(): array {
        return $this->queryLog;
    }
    
    /**
     * Limpiar log de queries
     */
    public function clearQueryLog(): void {
        $this->queryLog = [];
    }
    
    /**
     * Log general del sistema
     * 
     * @param string $message
     */
    private function log(string $message): void {
        if (!$this->loggingEnabled) {
            return;
        }
        
        $logFile = $this->config['log_file'] ?? __DIR__ . '/../logs/database.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Obtener estadísticas de conexión
     * 
     * @return array
     */
    public function getStats(): array {
        return [
            'reconnect_attempts' => $this->reconnectAttempts,
            'max_reconnect_attempts' => $this->maxReconnectAttempts,
            'in_transaction' => $this->inTransaction,
            'logging_enabled' => $this->loggingEnabled,
            'query_count' => count($this->queryLog),
            'connection_alive' => $this->isConnectionAlive($this->primaryConnection)
        ];
    }
    
    /**
     * Cerrar conexiones al destruir el objeto
     */
    public function __destruct() {
        if ($this->inTransaction) {
            try {
                $this->rollback();
            } catch (Exception $e) {
                // Silenciar errores en destructor
            }
        }
        
        $this->primaryConnection = null;
        $this->connectionPool = [];
    }
}
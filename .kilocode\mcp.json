{"mcpServers": {"mysql-tata-repuestos": {"command": "npx", "args": ["@f4ww4z/mcp-mysql-server"], "env": {"DB_HOST": "***************", "DB_PORT": "3306", "DB_USER": "ncornejo", "DB_PASSWORD": "N1c0l7as17", "DB_NAME": "Tata_Repuestos"}}, "mysql-database": {"command": "npx", "args": ["@f4ww4z/mcp-mysql-server"], "env": {"DB_HOST": "**************", "DB_PORT": "3306", "DB_USER": "ncornejo", "DB_PASSWORD": "N1c0l7as17", "DB_NAME": "operaciones_tqw"}}, "mysql-aunclick": {"command": "npx", "args": ["@f4ww4z/mcp-mysql-server"], "env": {"DB_HOST": "**************", "DB_PORT": "3306", "DB_USER": "p<PERSON><PERSON>o", "DB_PASSWORD": "Pcornejo@2025", "DB_NAME": "aunclick_prueba"}}, "sqlserver-database": {"command": "npx", "args": ["@executeautomation/database-server", "--sqlserver", "--server", "*************", "--database", "telqway", "--user", "ncornejo", "--password", "N1c0l7as17", "--port", "1433"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mysql-gestar": {"command": "npx", "args": ["@f4ww4z/mcp-mysql-server"], "env": {"DB_HOST": "*************", "DB_PORT": "3306", "DB_USER": "gestarse_ncornejo7_experian", "DB_PASSWORD": "N1c0l7as17", "DB_NAME": "gestarse_experian"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}}}
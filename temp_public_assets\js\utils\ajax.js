/**
 * Funciones de utilidad para AJAX
 * Este archivo contiene funciones para simplificar el uso de AJAX.
 */

/**
 * Realiza una petición AJAX
 * 
 * @param {Object} options - Opciones de configuración
 * @param {string} options.url - URL a la que hacer la petición
 * @param {string} options.method - Método HTTP (GET, POST, PUT, DELETE)
 * @param {Object} options.data - Datos a enviar
 * @param {Function} options.success - Función a ejecutar en caso de éxito
 * @param {Function} options.error - Función a ejecutar en caso de error
 * @param {boolean} options.isJson - Indica si la respuesta es JSON
 */
function ajax(options) {
    // Valores por defecto
    const settings = {
        url: '',
        method: 'GET',
        data: null,
        success: function() {},
        error: function() {},
        isJson: true,
        ...options
    };
    
    // Crear objeto FormData si es necesario
    let formData = settings.data;
    if (settings.data && !(settings.data instanceof FormData) && typeof settings.data === 'object') {
        formData = new FormData();
        for (const key in settings.data) {
            formData.append(key, settings.data[key]);
        }
    }
    
    // Configurar opciones de fetch
    const fetchOptions = {
        method: settings.method,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    // Añadir body para métodos que lo aceptan
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(settings.method.toUpperCase()) && formData) {
        fetchOptions.body = formData;
    }
    
    // Realizar petición
    fetch(settings.url, fetchOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return settings.isJson ? response.json() : response.text();
        })
        .then(data => {
            settings.success(data);
        })
        .catch(error => {
            console.error('AJAX Error:', error);
            settings.error(error);
        });
}

/**
 * Simplificación de ajax para GET
 * 
 * @param {string} url - URL a la que hacer la petición
 * @param {Function} success - Función a ejecutar en caso de éxito
 * @param {Function} error - Función a ejecutar en caso de error
 * @param {boolean} isJson - Indica si la respuesta es JSON
 */
function ajaxGet(url, success, error, isJson = true) {
    ajax({
        url: url,
        method: 'GET',
        success: success,
        error: error,
        isJson: isJson
    });
}

/**
 * Simplificación de ajax para POST
 * 
 * @param {string} url - URL a la que hacer la petición
 * @param {Object} data - Datos a enviar
 * @param {Function} success - Función a ejecutar en caso de éxito
 * @param {Function} error - Función a ejecutar en caso de error
 * @param {boolean} isJson - Indica si la respuesta es JSON
 */
function ajaxPost(url, data, success, error, isJson = true) {
    ajax({
        url: url,
        method: 'POST',
        data: data,
        success: success,
        error: error,
        isJson: isJson
    });
}

// Exportar para uso global
window.ajax = ajax;
window.ajaxGet = ajaxGet;
window.ajaxPost = ajaxPost;
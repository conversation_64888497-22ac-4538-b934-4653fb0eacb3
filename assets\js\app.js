/**
 * Main Application Bootstrap
 * 
 * Integrates all core components:
 * - SPA Router
 * - State Management (Store)
 * - API Client
 * - Navigation System
 */

// Import core modules
import SPARouter from './core/SPARouter.js';
import Store from './core/Store.js';
import ApiClient from './core/ApiClient.js';

// Import store modules
import appModule from './store/modules/app.js';
import userModule from './store/modules/user.js';
import navigationModule from './store/modules/navigation.js';
import uiModule from './store/modules/ui.js';

// Import route definitions
import routes from './routes/index.js';

// Import components
import Sidebar from './components/Sidebar.js';
import Header from './components/Header.js';

class Application {
    constructor() {
        this.router = null;
        this.store = null;
        this.api = null;
        this.components = {};
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    /**
     * Initialize application
     */
    async init() {
        try {
            console.log('Initializing application...');
            
            // Initialize API client
            this.initApi();
            
            // Initialize store
            this.initStore();
            
            // Initialize router
            this.initRouter();
            
            // Initialize components
            await this.initComponents();
            
            // Load initial data
            await this.loadInitialData();
            
            // Setup global event handlers
            this.setupEventHandlers();
            
            console.log('Application initialized successfully');
            
            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('app:ready', { detail: { app: this } }));
        } catch (error) {
            console.error('Application initialization failed:', error);
            this.handleInitError(error);
        }
    }

    /**
     * Initialize API client
     */
    initApi() {
        this.api = new ApiClient({
            baseURL: '/api/v1',
            timeout: 30000,
            cache: true,
            retry: true
        });

        // Add request interceptor for loading state
        this.api.addRequestInterceptor(async (config) => {
            if (this.store) {
                this.store.commit('app/setLoading', true);
            }
            return config;
        });

        // Add response interceptor for loading state and error handling
        this.api.addResponseInterceptor(async (response) => {
            if (this.store) {
                this.store.commit('app/setLoading', false);
            }
            return response;
        });

        // Add response error interceptor
        this.api.addResponseInterceptor(async (response) => {
            if (!response.ok) {
                // Handle authentication errors
                if (response.status === 401) {
                    this.store.dispatch('user/logout');
                    this.router.push('/login');
                }
                
                // Show error notification
                if (this.store) {
                    this.store.dispatch('app/showNotification', {
                        type: 'error',
                        message: response.data?.message || 'An error occurred'
                    });
                }
            }
            return response;
        });

        // Make API available globally
        window.$api = this.api;
    }

    /**
     * Initialize store
     */
    initStore() {
        this.store = new Store({
            strict: true,
            modules: {
                app: appModule,
                user: userModule,
                navigation: navigationModule,
                ui: uiModule
            },
            persist: {
                key: 'proyectdash-state',
                paths: ['user.token', 'ui.theme', 'ui.sidebarOpen']
            },
            plugins: [
                // Logger plugin for development
                (store) => {
                    if (process.env.NODE_ENV === 'development') {
                        store.subscribeMutation((mutation) => {
                            console.log('[Store] Mutation:', mutation.type, mutation.payload);
                        });
                        
                        store.subscribeAction((action, stage) => {
                            if (stage === 'before') {
                                console.log('[Store] Action:', action.type, action.payload);
                            }
                        });
                    }
                }
            ]
        });

        // Make store available globally
        window.$store = this.store;
    }

    /**
     * Initialize router
     */
    initRouter() {
        this.router = new SPARouter({
            baseUrl: '',
            transitions: {
                default: {
                    enter: 'fade-in',
                    leave: 'fade-out'
                }
            },
            beforeEach: async (to, from) => {
                // Check authentication for protected routes
                if (to.meta.requiresAuth && !this.store.state.user.isAuthenticated) {
                    this.router.push('/login');
                    return false;
                }
                
                // Update active navigation item
                this.store.commit('navigation/setActiveItem', to.path);
                
                return true;
            },
            afterEach: (to, from) => {
                // Update page title
                document.title = to.meta.title || 'ProyectDash';
                
                // Track page view
                this.trackPageView(to);
            }
        });

        // Register routes
        routes.forEach(route => {
            this.router.route(route.path, route);
        });

        // Make router available globally
        window.$router = this.router;
    }

    /**
     * Initialize components
     */
    async initComponents() {
        // Initialize sidebar
        const sidebarContainer = document.getElementById('sidebar-container');
        if (sidebarContainer) {
            this.components.sidebar = new Sidebar({
                container: sidebarContainer,
                store: this.store,
                router: this.router
            });
            await this.components.sidebar.render();
        }

        // Initialize header
        const headerContainer = document.getElementById('header-container');
        if (headerContainer) {
            this.components.header = new Header({
                container: headerContainer,
                store: this.store,
                router: this.router
            });
            await this.components.header.render();
        }
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            // Load user data if authenticated
            const token = this.store.state.user.token;
            if (token) {
                this.api.setToken(token);
                await this.store.dispatch('user/fetchProfile');
            }

            // Load navigation structure
            await this.store.dispatch('navigation/loadNavigation');

            // Load application configuration
            await this.store.dispatch('app/loadConfig');
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }

    /**
     * Setup global event handlers
     */
    setupEventHandlers() {
        // Handle window resize
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.store.commit('ui/setWindowSize', {
                    width: window.innerWidth,
                    height: window.innerHeight
                });
            }, 250);
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + K - Quick search
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                this.store.dispatch('ui/toggleQuickSearch');
            }

            // Ctrl/Cmd + \ - Toggle sidebar
            if ((event.ctrlKey || event.metaKey) && event.key === '\\') {
                event.preventDefault();
                this.store.dispatch('ui/toggleSidebar');
            }

            // Escape - Close modals
            if (event.key === 'Escape') {
                this.store.dispatch('ui/closeAllModals');
            }
        });

        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.store.commit('app/setActive', false);
            } else {
                this.store.commit('app/setActive', true);
                // Refresh data when becoming visible
                this.refreshData();
            }
        });

        // Handle online/offline
        window.addEventListener('online', () => {
            this.store.commit('app/setOnline', true);
            this.store.dispatch('app/showNotification', {
                type: 'success',
                message: 'Connection restored'
            });
        });

        window.addEventListener('offline', () => {
            this.store.commit('app/setOnline', false);
            this.store.dispatch('app/showNotification', {
                type: 'warning',
                message: 'You are offline'
            });
        });

        // Handle router events
        window.addEventListener('router:loading', (event) => {
            this.store.commit('app/setRouteLoading', event.detail);
        });

        window.addEventListener('router:navigation', (event) => {
            this.store.commit('app/addRouteHistory', {
                from: event.detail.from,
                to: event.detail.to,
                timestamp: Date.now()
            });
        });
    }

    /**
     * Refresh application data
     */
    async refreshData() {
        if (this.store.state.user.isAuthenticated) {
            try {
                await Promise.all([
                    this.store.dispatch('user/fetchProfile'),
                    this.store.dispatch('app/fetchNotifications')
                ]);
            } catch (error) {
                console.error('Failed to refresh data:', error);
            }
        }
    }

    /**
     * Track page view
     */
    trackPageView(route) {
        // Analytics tracking
        if (window.gtag) {
            window.gtag('config', 'GA_MEASUREMENT_ID', {
                page_path: route.path
            });
        }

        // Custom tracking
        this.store.dispatch('app/trackPageView', {
            path: route.path,
            title: route.meta.title,
            timestamp: Date.now()
        });
    }

    /**
     * Handle initialization error
     */
    handleInitError(error) {
        // Show error UI
        const container = document.getElementById('app') || document.body;
        container.innerHTML = `
            <div class="init-error">
                <h1>Application Error</h1>
                <p>Failed to initialize the application.</p>
                <p class="error-message">${error.message}</p>
                <button onclick="location.reload()">Reload</button>
            </div>
        `;
    }

    /**
     * Public API
     */
    
    // Navigate to a route
    navigate(path, options) {
        return this.router.navigate(path, options);
    }

    // Get current route
    getCurrentRoute() {
        return this.router.getCurrentRoute();
    }

    // Dispatch store action
    dispatch(action, payload) {
        return this.store.dispatch(action, payload);
    }

    // Commit store mutation
    commit(mutation, payload) {
        return this.store.commit(mutation, payload);
    }

    // Make API request
    request(config) {
        return this.api.request(config);
    }

    // Show notification
    notify(options) {
        return this.store.dispatch('app/showNotification', options);
    }

    // Open modal
    openModal(name, props) {
        return this.store.dispatch('ui/openModal', { name, props });
    }

    // Close modal
    closeModal(name) {
        return this.store.dispatch('ui/closeModal', name);
    }
}

// Create and export application instance
const app = new Application();

// Export for use in other modules
export default app;

// Make app available globally
window.$app = app;
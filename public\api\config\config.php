<?php
/**
 * Configuración de la API - Actualizada con DatabaseManager
 * 
 * Este archivo contiene las configuraciones globales para la API
 * Integrado con el nuevo sistema DatabaseManager para conexiones optimizadas
 * 
 * <AUTHOR> with Claude <PERSON>
 * @version 2.0
 */

// Definir zona horaria
date_default_timezone_set('UTC');

// Detectar ambiente
$environment = $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? 'development';

// Habilitar o deshabilitar mostrado de errores según ambiente
$show_errors = $environment !== 'production';

if ($show_errors) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    error_reporting(0);
}

// Configuración de la API
define('API_VERSION', 'v1');
define('API_BASE_PATH', '/api/' . API_VERSION);
define('API_ENVIRONMENT', $environment);

// Cargar DatabaseManager y clases core
require_once __DIR__ . '/../../../core/DatabaseManager.php';
require_once __DIR__ . '/../../../core/QueryBuilder.php';
require_once __DIR__ . '/../../../core/BaseModel.php';

// Autoload mejorado para clases
spl_autoload_register(function($class_name) {
    // Convertir el nombre de la clase a un formato de ruta
    $class_file = str_replace('\\', '/', $class_name) . '.php';
    
    // Buscar en las posibles ubicaciones de la clase (orden de prioridad)
    $possible_locations = [
        __DIR__ . '/../../../core/',                    // Core classes
        __DIR__ . '/../v1/controllers/',                // API Controllers
        __DIR__ . '/../v1/models/',                     // API Models  
        __DIR__ . '/../../services/api/models/',        // Service Models
        __DIR__ . '/../../services/api/endpoints/',     // Service Endpoints
        __DIR__ . '/../../services/',                   // General Services
        __DIR__ . '/../../components/',                 // Components
        __DIR__ . '/../helpers/',                       // API Helpers
    ];
    
    foreach ($possible_locations as $location) {
        if (file_exists($location . $class_file)) {
            require_once $location . $class_file;
            return;
        }
    }
    
    // Log error si no se encuentra la clase (solo en desarrollo)
    if ($show_errors) {
        error_log("Clase no encontrada: $class_name en ubicaciones: " . implode(', ', $possible_locations));
    }
    
    throw new Exception("Clase $class_name no encontrada");
});

/**
 * Obtener instancia del DatabaseManager (reemplaza getDbConnection)
 * 
 * @return DatabaseManager
 * @throws Exception
 */
function getDatabaseManager(): DatabaseManager {
    return DatabaseManager::getInstance();
}

/**
 * Función legacy para compatibilidad con código existente
 * 
 * @return PDO
 * @throws Exception
 * @deprecated Usar getDatabaseManager() en su lugar
 */
function getDbConnection(): PDO {
    if (API_ENVIRONMENT === 'development') {
        error_log("DEPRECATED: getDbConnection() está obsoleto. Usar getDatabaseManager() en su lugar.");
    }
    
    return getDatabaseManager()->getConnection();
}

// Función helper para respuestas JSON
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Cargar API Client
require_once __DIR__ . '/../../../services/api/ApiClient.php';
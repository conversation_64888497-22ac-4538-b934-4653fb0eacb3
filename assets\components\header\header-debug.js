/**
 * Header Debug Utilities
 * 
 * Utilidades para debugging y verificación del componente header
 */

/**
 * Verificar estado del header
 */
function debugHeader() {
    console.group('🔍 Header Debug Information');
    
    // Verificar elementos del DOM
    const elements = {
        'Fullscreen Toggle': document.querySelector('.fullscreen-toggle'),
        'Fullscreen Icon': document.querySelector('.fullscreen-icon'),
        'Fullscreen Exit Icon': document.querySelector('.fullscreen-exit-icon'),
        'Notification Toggle': document.querySelector('.notification-toggle'),
        'Notifications Canvas': document.getElementById('notificationsCanvas'),
        'Notifications Overlay': document.getElementById('notificationsOverlay'),
        'Canvas Lateral Toggle': document.querySelector('.canvas-lateral-toggle'),
        'Canvas Lateral': document.getElementById('canvasLateral'),
        'Canvas Lateral Overlay': document.getElementById('canvasLateralOverlay'),
        'Language Dropdown': document.querySelector('.language-dropdown')
    };
    
    console.group('📋 DOM Elements');
    Object.entries(elements).forEach(([name, element]) => {
        console.log(`${element ? '✅' : '❌'} ${name}:`, element || 'Not found');
    });
    console.groupEnd();
    
    // Verificar instancia del header
    console.group('🔧 Header Instance');
    const instance = window.getHeaderInstance();
    console.log('Instance:', instance);
    console.log('Initialized:', instance?.isInitialized || false);
    console.groupEnd();
    
    // Verificar API de pantalla completa
    console.group('📺 Fullscreen API Support');
    console.log('requestFullscreen:', !!document.documentElement.requestFullscreen);
    console.log('webkitRequestFullscreen:', !!document.documentElement.webkitRequestFullscreen);
    console.log('mozRequestFullScreen:', !!document.documentElement.mozRequestFullScreen);
    console.log('msRequestFullscreen:', !!document.documentElement.msRequestFullscreen);
    console.log('Currently fullscreen:', !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
    ));
    console.groupEnd();
    
    // Verificar estilos cargados
    console.group('🎨 Stylesheets');
    const stylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    const headerStyles = stylesheets.filter(link => 
        link.href.includes('header') || 
        link.href.includes('components')
    );
    console.log('Header stylesheets:', headerStyles.map(link => link.href));
    console.groupEnd();
    
    console.groupEnd();
    
    return {
        elements,
        instance,
        fullscreenSupport: {
            standard: !!document.documentElement.requestFullscreen,
            webkit: !!document.documentElement.webkitRequestFullscreen,
            moz: !!document.documentElement.mozRequestFullScreen,
            ms: !!document.documentElement.msRequestFullscreen
        }
    };
}

/**
 * Probar funcionalidad de pantalla completa
 */
function testFullscreen() {
    console.group('🧪 Testing Fullscreen Functionality');
    
    const instance = window.getHeaderInstance();
    if (!instance) {
        console.error('❌ Header instance not found');
        console.groupEnd();
        return;
    }
    
    try {
        console.log('🔄 Toggling fullscreen...');
        instance.toggleFullscreen();
        console.log('✅ Fullscreen toggle executed');
    } catch (error) {
        console.error('❌ Fullscreen toggle failed:', error);
    }
    
    console.groupEnd();
}

/**
 * Probar funcionalidad de notificaciones
 */
function testNotifications() {
    console.group('🧪 Testing Notifications Functionality');
    
    const instance = window.getHeaderInstance();
    if (!instance) {
        console.error('❌ Header instance not found');
        console.groupEnd();
        return;
    }
    
    try {
        console.log('🔄 Toggling notifications panel...');
        instance.toggleNotifications();
        console.log('✅ Notifications toggle executed');
        
        // Cerrar después de 3 segundos
        setTimeout(() => {
            console.log('🔄 Closing notifications panel...');
            instance.closeNotificationsPanel();
            console.log('✅ Notifications panel closed');
        }, 3000);
    } catch (error) {
        console.error('❌ Notifications toggle failed:', error);
    }
    
    console.groupEnd();
}

/**
 * Probar funcionalidad del canvas lateral
 */
function testCanvasLateral() {
    console.group('🧪 Testing Canvas Lateral Functionality');
    
    const instance = window.getHeaderInstance();
    if (!instance) {
        console.error('❌ Header instance not found');
        console.groupEnd();
        return;
    }
    
    try {
        console.log('🔄 Toggling canvas lateral...');
        instance.toggleCanvasLateral();
        console.log('✅ Canvas lateral toggle executed');
        
        // Cerrar después de 3 segundos
        setTimeout(() => {
            console.log('🔄 Closing canvas lateral...');
            instance.closeCanvasLateralPanel();
            console.log('✅ Canvas lateral closed');
        }, 3000);
    } catch (error) {
        console.error('❌ Canvas lateral toggle failed:', error);
    }
    
    console.groupEnd();
}

/**
 * Ejecutar todas las pruebas
 */
function runAllTests() {
    console.group('🚀 Running All Header Tests');
    
    console.log('📊 Debug information:');
    debugHeader();
    
    console.log('\n🧪 Running functionality tests...');
    
    // Esperar un poco entre pruebas
    setTimeout(() => testFullscreen(), 1000);
    setTimeout(() => testNotifications(), 3000);
    setTimeout(() => testCanvasLateral(), 7000);
    
    console.groupEnd();
}

/**
 * Agregar utilidades de debug al objeto window para acceso global
 */
window.headerDebug = {
    debug: debugHeader,
    testFullscreen,
    testNotifications,
    testCanvasLateral,
    runAllTests
};

console.log('🔧 Header debug utilities loaded. Use window.headerDebug for testing.');
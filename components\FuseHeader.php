<?php
/**
 * FuseHeader Component
 * 
 * Implementación PHP del componente de header de FUSE React
 */

class FuseHeader {
    private $language = 'EN';
    private $notifications = 3;
    private $languages = [
        ['code' => 'EN', 'name' => 'English', 'flag' => '🇺🇸'],
        ['code' => 'ES', 'name' => 'Español', 'flag' => '🇪🇸'],
        ['code' => 'FR', 'name' => 'Français', 'flag' => '🇫🇷'],
        ['code' => 'DE', 'name' => 'Deutsch', 'flag' => '🇩🇪'],
        ['code' => 'IT', 'name' => 'Italiano', 'flag' => '🇮🇹']
    ];

    public function __construct($language = 'EN') {
        $this->language = $language;
    }

    public function render() {
        // Obtenemos los datos del lenguaje actual
        $currentLanguage = array_values(array_filter($this->languages, function($lang) {
            return $lang['code'] === $this->language;
        }))[0];

        $languageDropdown = '';
        foreach ($this->languages as $lang) {
            $languageDropdown .= '
            <div class="language-item py-1.5 px-3 text-xs hover:bg-gray-100 cursor-pointer" data-lang-code="' . $lang['code'] . '">
                <span class="mr-2">' . $lang['flag'] . '</span>
                ' . $lang['name'] . '
            </div>';
        }

        return <<<HTML
        <header class="h-16 bg-white border-b border-gray-200 px-6 flex items-center justify-between">
            <!-- Left side - Logo and Navigation -->
            <div class="flex items-center space-x-6">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">F</span>
                    </div>
                    <span class="text-xl font-semibold text-gray-900">FUSE React</span>
                </div>

                <!-- Vitejs Button -->
                <button class="h-8 px-3 text-xs border border-gray-200 rounded-md hover:bg-gray-50">
                    Vitejs
                </button>

                <!-- Navigation Icons -->
                <div class="flex items-center space-x-1">
                    <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                        </svg>
                    </button>
                    
                    <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                        </svg>
                    </button>
                    
                    <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                        </svg>
                    </button>
                    
                    <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 15.75l-2.489-2.489m0 0a6.001 6.001 0 10-8.485-8.485 6.001 6.001 0 008.485 8.485z" />
                        </svg>
                    </button>
                    
                    <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Right side - Settings and User -->
            <div class="flex items-center space-x-2">
                <!-- Language Selector -->
                <div class="relative language-dropdown">
                    <button class="h-8 px-2 text-xs rounded-md flex items-center hover:bg-gray-100">
                        {$currentLanguage['flag']} {$this->language}
                    </button>
                    <div class="language-dropdown-content hidden absolute right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 w-32">
                        {$languageDropdown}
                    </div>
                </div>

                <!-- Text Size -->
                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                    <span class="text-xs font-medium">Aa</span>
                </button>

                <!-- Fullscreen -->
                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100 fullscreen-toggle" 
                        data-header-tooltip="Pantalla completa" 
                        aria-label="Alternar pantalla completa">
                    <svg class="w-4 h-4 fullscreen-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5M3.75 20.25v-4.5m0 4.5h4.5M20.25 3.75h-4.5m4.5 0v4.5M20.25 20.25h-4.5m4.5 0v-4.5" />
                    </svg>
                    <svg class="w-4 h-4 fullscreen-exit-icon hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M3.75 8.25h1.5m9 1.5v-1.5m1.5-1.5h-1.5m-9 7.5h1.5m-1.5 3v-1.5m9 1.5h-1.5m0-9V3.75m1.5 1.5h-1.5" />
                    </svg>
                </button>

                <!-- Theme -->
                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
                    </svg>
                </button>

                <!-- Search -->
                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                    </svg>
                </button>

                <!-- Notifications -->
                <div class="relative">
                    <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100 relative notification-toggle"
                            data-header-tooltip="Notificaciones"
                            aria-label="Abrir panel de notificaciones">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                        </svg>
                        {$this->renderNotificationBadge()}
                    </button>
                </div>

                <!-- Canvas Lateral -->
                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100 relative canvas-lateral-toggle"
                        data-header-tooltip="Panel lateral"
                        aria-label="Abrir panel lateral">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                    </svg>
                </button>

                <!-- Notifications Canvas Lateral -->
                <div id="notificationsCanvas" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 border-l border-gray-200">
                    <div class="h-full flex flex-col">
                        <!-- Canvas Header -->
                        <div class="flex items-center justify-between p-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Notificaciones</h3>
                            <div class="flex items-center space-x-2">
                                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100 clear-all-notifications">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                    </svg>
                                </button>
                                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100 close-notifications">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Canvas Content -->
                        <div class="flex-1 overflow-y-auto p-4">
                            <div class="space-y-4">
                                {$this->renderNotificationItems()}
                            </div>
                        </div>
                        
                        <!-- Canvas Footer -->
                        <div class="p-4 border-t border-gray-200">
                            <button class="w-full py-2 px-4 bg-sky-500 text-white rounded-md hover:bg-sky-600 transition-colors">
                                Ver todas las notificaciones
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Overlay para cerrar canvas -->
                <div id="notificationsOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

                <!-- Canvas Lateral -->
                <div id="canvasLateral" class="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 border-l border-gray-200">
                    <div class="h-full flex flex-col">
                        <!-- Canvas Header -->
                        <div class="flex items-center justify-between p-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Panel Lateral</h3>
                            <div class="flex items-center space-x-2">
                                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100 close-canvas-lateral">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Canvas Content -->
                        <div class="flex-1 overflow-y-auto p-4">
                            <div class="space-y-4">
                                <div class="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                                    <h4 class="font-medium text-blue-900">Bienvenido al Panel Lateral</h4>
                                    <p class="text-blue-700 mt-1">Este es un panel deslizante que puedes personalizar con tu contenido.</p>
                                </div>
                                
                                <div class="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                                    <h4 class="font-medium text-green-900">Características</h4>
                                    <ul class="text-green-700 mt-1 list-disc list-inside">
                                        <li>Diseño responsive</li>
                                        <li>Animaciones suaves</li>
                                        <li>Personalizable</li>
                                        <li>Fácil de usar</li>
                                    </ul>
                                </div>
                                
                                <div class="p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                                    <h4 class="font-medium text-yellow-900">Contenido Dinámico</h4>
                                    <p class="text-yellow-700 mt-1">Puedes agregar cualquier contenido aquí mediante JavaScript o PHP.</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Canvas Footer -->
                        <div class="p-4 border-t border-gray-200">
                            <button class="w-full py-2 px-4 bg-sky-500 text-white rounded-md hover:bg-sky-600 transition-colors">
                                Guardar Cambios
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Overlay para cerrar canvas lateral -->
                <div id="canvasLateralOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

                <!-- Chat -->
                <button class="h-8 w-8 p-0 rounded-md flex items-center justify-center hover:bg-gray-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.083 1.5 1.994v9.018c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v9.018c0 1.136.845 2.1 1.976 2.192 1.31.1 2.65.155 4.02.155 1.34 0 2.67-.055 4.02-.155 1.13-.092 1.976-1.057 1.976-2.192v-9.018c0-.844-.48-1.59-1.185-1.974z" />
                    </svg>
                </button>

                <!-- User Avatar -->
                <div class="w-8 h-8 bg-gradient-to-br from-sky-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-medium cursor-pointer hover:ring-2 hover:ring-sky-500 hover:ring-offset-2 transition-all">
                    G
                </div>
            </div>
        </header>
HTML;
    }

    private function renderNotificationBadge() {
        if ($this->notifications > 0) {
            return '<span class="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-xs min-w-4 bg-red-500 text-white rounded-full">' . $this->notifications . '</span>';
        }
        return '';
    }

    private function renderNotificationItems() {
        $notifications = [
            [
                'id' => 1,
                'title' => 'Nueva actualización disponible',
                'message' => 'Se ha lanzado una nueva versión del sistema con mejoras de rendimiento.',
                'time' => 'Hace 5 minutos',
                'read' => false,
                'icon' => '🔔'
            ],
            [
                'id' => 2,
                'title' => 'Recordatorio de reunión',
                'message' => 'Reunión de equipo programada para mañana a las 10:00 AM.',
                'time' => 'Hace 1 hora',
                'read' => false,
                'icon' => '📅'
            ],
            [
                'id' => 3,
                'title' => 'Tarea completada',
                'message' => 'El proyecto "Dashboard Analytics" ha sido completado exitosamente.',
                'time' => 'Hace 3 horas',
                'read' => true,
                'icon' => '✅'
            ],
            [
                'id' => 4,
                'title' => 'Nuevo mensaje',
                'message' => 'Has recibido un nuevo mensaje de soporte técnico.',
                'time' => 'Hace 6 horas',
                'read' => true,
                'icon' => '💬'
            ]
        ];

        $items = '';
        foreach ($notifications as $notification) {
            $readClass = $notification['read'] ? 'bg-gray-50' : 'bg-blue-50 border-l-4 border-blue-500';
            $iconClass = $notification['read'] ? 'text-gray-400' : 'text-blue-500';
            
            $items .= '
            <div class="notification-item p-4 rounded-lg border border-gray-200 ' . $readClass . ' hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <span class="text-xl ' . $iconClass . '">' . $notification['icon'] . '</span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900 truncate">' . $notification['title'] . '</h4>
                            <span class="text-xs text-gray-500">' . $notification['time'] . '</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">' . $notification['message'] . '</p>
                        <div class="mt-2 flex items-center space-x-2">
                            <button class="text-xs text-blue-600 hover:text-blue-800 mark-as-read" data-notification-id="' . $notification['id'] . '">
                                Marcar como leído
                            </button>
                            <button class="text-xs text-gray-500 hover:text-gray-700 delete-notification" data-notification-id="' . $notification['id'] . '">
                                Eliminar
                            </button>
                        </div>
                    </div>
                </div>
            </div>';
        }

        return $items;
    }
}
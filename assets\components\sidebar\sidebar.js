/**
 * FUSE React Sidebar - JavaScript
 * 
 * Este archivo contiene toda la lógica JavaScript para el sidebar de FUSE React.
 * Es independiente del framework PHP y puede ser usado con cualquier backend.
 * 
 * <AUTHOR> with Claude Code
 * @version 1.1
 */

class FuseSidebarJS {
    constructor(options = {}) {
        this.options = {
            container: options.container || '#fuse-sidebar', // The sidebar container element
            activeItem: options.activeItem || 'project',
            navigationMode: options.navigationMode || 'spa', // 'spa', 'full', 'ajax'
            baseUrl: options.baseUrl || '/',
            onItemClick: options.onItemClick || null,
            onItemHover: options.onItemHover || null,
            contentTarget: options.contentTarget || '#main-content', // Default content area
            debug: options.debug || false
        };

        this.container = document.querySelector(this.options.container);
        if (!this.container) {
            console.error(`[FUSE Sidebar] Container element '${this.options.container}' not found.`);
            return;
        }
        
        this.activeItem = this.options.activeItem;
        this.hoveredItem = null;
        
        this.bindEvents();
        this.setInitialState();
    }
    
    bindEvents() {
        this.unbindEvents();
        
        const clickHandler = (e) => {
            // Verificar que e.target sea un nodo DOM válido
            if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE) {
                return;
            }
            
            const item = e.target.closest('.fuse-sidebar-item');
            if (item) {
                const itemId = item.dataset.itemId;
                if (itemId) {
                    this.handleItemClick(itemId, item, e);
                }
            }
        };
        
        const mouseEnterHandler = (e) => {
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE) {
                const item = e.target.closest('.fuse-sidebar-item');
                if (item) {
                    const itemId = item.dataset.itemId;
                    if (itemId) {
                        this.handleItemHover(itemId, item);
                    }
                }
            }
        };
        
        const mouseLeaveHandler = () => {
            this.handleItemLeave();
        };
        
        const keydownHandler = (e) => {
            // Verificar que e.target sea un nodo DOM válido
            if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE) {
                return;
            }
            
            if (e.target.closest('.fuse-sidebar-item')) {
                this.handleKeydown(e);
            }
        };
        
        this.eventHandlers = { clickHandler, mouseEnterHandler, mouseLeaveHandler, keydownHandler };
        
        this.container.addEventListener('click', this.eventHandlers.clickHandler);
        this.container.addEventListener('mouseover', this.eventHandlers.mouseEnterHandler);
        this.container.addEventListener('mouseout', this.eventHandlers.mouseLeaveHandler);
        this.container.addEventListener('keydown', this.eventHandlers.keydownHandler);
    }
    
    setInitialState() {
        this.setActiveItem(this.options.activeItem, false);
        this.log('Initial state set with active item:', this.options.activeItem);
    }

    handleItemClick(itemId, itemElement, event) {
        this.log('Item clicked:', itemId);
        if (this.options.navigationMode !== 'none') {
            event.preventDefault();
            event.stopPropagation();
        }

        if (itemId === this.activeItem && this.options.navigationMode !== 'full') {
            this.log('Item is already active:', itemId);
            return;
        }

        this.setActiveItem(itemId);
        this.handleNavigation(itemId);
    }

    handleItemHover(itemId, itemElement) {
        this.hoveredItem = itemId;
        const hoverEffect = itemElement.querySelector('.fuse-sidebar-hover');
        if (hoverEffect) hoverEffect.classList.add('opacity-100');
        if (typeof this.options.onItemHover === 'function') this.options.onItemHover(itemId, itemElement);
    }

    handleItemLeave() {
        if (this.hoveredItem) {
            const itemElement = document.querySelector(`[data-item-id="${this.hoveredItem}"]`);
            if (itemElement) {
                const hoverEffect = itemElement.querySelector('.fuse-sidebar-hover');
                if (hoverEffect) hoverEffect.classList.remove('opacity-100');
            }
            this.hoveredItem = null;
        }
    }

    handleKeydown(e) {
        const item = e.target.closest('.fuse-sidebar-item');
        if (!item) return;
        if (e.key === 'Enter' || e.key === ' ') this.handleItemClick(item.dataset.itemId, item, e);
    }

    handleNavigation(itemId) {
        this.log('Handling navigation for:', itemId);
        if (this.options.navigationMode === 'spa' && typeof this.options.onItemClick === 'function') {
            this.options.onItemClick(itemId);
        } else if (this.options.navigationMode === 'ajax') {
            this.loadContentAjax(itemId);
        } else { // 'full' navigation
            const pageUrl = itemId === 'project' ? this.options.baseUrl : `${this.options.baseUrl.replace(/\/$/, '')}/${itemId}`;
            window.location.href = pageUrl;
        }
    }

    async loadContentAjax(itemId) {
        const url = `${this.options.baseUrl.replace(/\/$/, '')}/index.php?page=${itemId}`;
        this.log(`Loading content from: ${url}`);
        try {
            const response = await fetch(url, {
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            });
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const content = await response.text();
            const targetElement = document.querySelector(this.options.contentTarget);
            if (targetElement) targetElement.innerHTML = content;
            else console.error(`AJAX content target '${this.options.contentTarget}' not found.`);
        } catch (error) {
            console.error('Error loading content via AJAX:', error);
        }
    }

    setActiveItem(itemId) {
        document.querySelectorAll('.fuse-sidebar-item').forEach(item => {
            const isSelected = item.dataset.itemId === itemId;
            item.classList.toggle('bg-blue-50', isSelected);
            item.classList.toggle('text-blue-700', isSelected);
            item.classList.toggle('shadow-sm', isSelected);
            item.classList.toggle('text-gray-700', !isSelected);
            item.classList.toggle('hover:bg-gray-100', !isSelected);
        });
        this.activeItem = itemId;
        this.log('Active item set to:', itemId);
        if (this.options.navigationMode === 'spa') {
            const url = itemId === 'project' ? this.options.baseUrl : `${this.options.baseUrl}${itemId}`;
            window.history.pushState({ itemId }, '', url);
        }
    }

    destroy() {
        this.unbindEvents();
        this.log('FUSE Sidebar destroyed');
    }

    unbindEvents() {
        if (this.eventHandlers && this.container) {
            this.container.removeEventListener('click', this.eventHandlers.clickHandler);
            this.container.removeEventListener('mouseover', this.eventHandlers.mouseEnterHandler);
            this.container.removeEventListener('mouseout', this.eventHandlers.mouseLeaveHandler);
            this.container.removeEventListener('keydown', this.eventHandlers.keydownHandler);
            this.eventHandlers = null;
        }
    }

    log(...args) {
        if (this.options.debug) console.log('[FUSE Sidebar]', ...args);
    }
}

let fuseSidebarInstance = null;
function initFuseSidebar(options = {}) {
    if (fuseSidebarInstance) fuseSidebarInstance.destroy();
    fuseSidebarInstance = new FuseSidebarJS(options);
    return fuseSidebarInstance;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = FuseSidebarJS;
} else if (typeof window !== 'undefined') {
    window.FuseSidebarJS = FuseSidebarJS;
    window.initFuseSidebar = initFuseSidebar;
}
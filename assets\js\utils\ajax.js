/**
 * Utilidades para peticiones AJAX
 */

/**
 * Realiza una petición AJAX GET
 * 
 * @param {string} url - URL a la que hacer la petición
 * @param {Object} options - Opciones de configuración
 * @returns {Promise} - Promesa con la respuesta
 */
function ajaxGet(url, options = {}) {
    const defaultOptions = {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    const mergedOptions = {...defaultOptions, ...options};
    
    return fetch(url, mergedOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
            }
            return response.text();
        });
}

/**
 * Realiza una petición AJAX POST
 * 
 * @param {string} url - URL a la que hacer la petición
 * @param {Object|FormData} data - Datos a enviar
 * @param {Object} options - Opciones de configuración
 * @returns {Promise} - Promesa con la respuesta
 */
function ajaxPost(url, data, options = {}) {
    const isFormData = data instanceof FormData;
    
    const defaultOptions = {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: isFormData ? data : JSON.stringify(data)
    };
    
    // Si no es FormData, añadir Content-Type
    if (!isFormData) {
        defaultOptions.headers['Content-Type'] = 'application/json';
    }
    
    // Fusionar opciones personalizadas
    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...(options.headers || {})
        }
    };
    
    return fetch(url, mergedOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
            }
            return response.text();
        });
}

/**
 * Carga contenido vía AJAX en un contenedor
 * 
 * @param {string} url - URL del contenido a cargar
 * @param {string|Element} container - Contenedor donde cargar el contenido
 * @param {Function} callback - Función a ejecutar después de cargar el contenido
 */
function loadContent(url, container, callback = null) {
    const containerElement = typeof container === 'string' 
        ? document.querySelector(container) 
        : container;
    
    if (!containerElement) {
        console.error('Contenedor no encontrado:', container);
        return;
    }
    
    // Mostrar indicador de carga
    const loader = document.createElement('div');
    loader.className = 'loader';
    loader.style.margin = '2rem auto';
    containerElement.innerHTML = '';
    containerElement.appendChild(loader);
    
    ajaxGet(url)
        .then(html => {
            containerElement.innerHTML = html;
            if (callback && typeof callback === 'function') {
                callback(html);
            }
        })
        .catch(error => {
            containerElement.innerHTML = `
                <div class="error-message p-4 rounded bg-red-100 text-red-800">
                    <p>Error al cargar el contenido:</p>
                    <p>${error.message}</p>
                </div>
            `;
            console.error('Error al cargar contenido:', error);
        });
}

// Exportar funciones
window.ajaxUtils = {
    get: ajaxGet,
    post: ajaxPost,
    loadContent: loadContent
};
<?php
/**
 * Modelo de Proyecto
 * 
 * Representa la estructura de datos de un proyecto y proporciona
 * métodos para trabajar con esta entidad.
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

class Project {
    /**
     * ID único del proyecto
     * @var int
     */
    public $id;
    
    /**
     * Nombre del proyecto
     * @var string
     */
    public $name;
    
    /**
     * Descripción del proyecto
     * @var string
     */
    public $description;
    
    /**
     * Estado actual del proyecto (pending, active, completed, etc.)
     * @var string
     */
    public $status;
    
    /**
     * Porcentaje de progreso (0-100)
     * @var int
     */
    public $progress;
    
    /**
     * Fecha de inicio (formato Y-m-d)
     * @var string
     */
    public $startDate;
    
    /**
     * Fecha de finalización (formato Y-m-d)
     * @var string
     */
    public $dueDate;
    
    /**
     * ID del propietario del proyecto
     * @var int
     */
    public $ownerId;
    
    /**
     * Nombre del propietario del proyecto
     * @var string
     */
    public $ownerName;
    
    /**
     * Array de miembros del equipo del proyecto
     * @var array
     */
    public $team = [];
    
    /**
     * Array de etiquetas del proyecto
     * @var array
     */
    public $tags = [];
    
    /**
     * URL del icono o imagen del proyecto
     * @var string|null
     */
    public $icon;
    
    /**
     * Fecha de creación (formato Y-m-d H:i:s)
     * @var string
     */
    public $createdAt;
    
    /**
     * Fecha de última actualización (formato Y-m-d H:i:s)
     * @var string
     */
    public $updatedAt;
    
    /**
     * Crea una instancia de Project a partir de datos de la API
     * 
     * @param array $data Datos de la API
     * @return Project
     */
    public static function fromApiResponse($data) {
        $project = new self();
        
        // Mapear propiedades básicas
        $project->id = isset($data['id']) ? (int)$data['id'] : null;
        $project->name = isset($data['name']) ? $data['name'] : '';
        $project->description = isset($data['description']) ? $data['description'] : '';
        $project->status = isset($data['status']) ? $data['status'] : 'pending';
        $project->progress = isset($data['progress']) ? (int)$data['progress'] : 0;
        $project->startDate = isset($data['start_date']) ? $data['start_date'] : null;
        $project->dueDate = isset($data['due_date']) ? $data['due_date'] : null;
        $project->ownerId = isset($data['owner_id']) ? (int)$data['owner_id'] : null;
        $project->ownerName = isset($data['owner_name']) ? $data['owner_name'] : '';
        $project->icon = isset($data['icon']) ? $data['icon'] : null;
        $project->createdAt = isset($data['created_at']) ? $data['created_at'] : null;
        $project->updatedAt = isset($data['updated_at']) ? $data['updated_at'] : null;
        
        // Mapear arrays
        if (isset($data['team']) && is_array($data['team'])) {
            $project->team = $data['team'];
        }
        
        if (isset($data['tags']) && is_array($data['tags'])) {
            $project->tags = $data['tags'];
        }
        
        return $project;
    }
    
    /**
     * Convierte el objeto a un array para enviar a la API
     * 
     * @return array
     */
    public function toApiRequest() {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'status' => $this->status,
            'progress' => $this->progress,
            'start_date' => $this->startDate,
            'due_date' => $this->dueDate,
            'owner_id' => $this->ownerId,
            'team' => $this->team,
            'tags' => $this->tags,
            'icon' => $this->icon
        ];
    }
    
    /**
     * Devuelve una representación HTML para mostrar el proyecto en una tabla
     * 
     * @return string HTML
     */
    public function toTableRow() {
        // Definir el color del badge según el estado
        $statusClass = '';
        switch ($this->status) {
            case 'completed':
                $statusClass = 'badge-success';
                break;
            case 'active':
                $statusClass = 'badge-info';
                break;
            case 'on-hold':
                $statusClass = 'badge-warning';
                break;
            case 'cancelled':
                $statusClass = 'badge-danger';
                break;
            default:
                $statusClass = 'badge-secondary';
        }
        
        // Formatear tags
        $tagsHtml = '';
        foreach ($this->tags as $tag) {
            $tagsHtml .= '<span class="badge bg-gray-200 text-gray-800 mr-1">' . htmlspecialchars($tag) . '</span>';
        }
        
        // Formatear miembros del equipo
        $teamHtml = '';
        $maxDisplayMembers = 3;
        $displayMembers = array_slice($this->team, 0, $maxDisplayMembers);
        
        foreach ($displayMembers as $member) {
            $initials = isset($member['name']) ? strtoupper(substr($member['name'], 0, 1)) : '?';
            $teamHtml .= '<div class="w-6 h-6 rounded-full bg-sky-500 text-white text-xs flex items-center justify-center mr-1">' . $initials . '</div>';
        }
        
        // Si hay más miembros de los que mostramos
        $remainingMembers = count($this->team) - $maxDisplayMembers;
        if ($remainingMembers > 0) {
            $teamHtml .= '<div class="w-6 h-6 rounded-full bg-gray-300 text-gray-700 text-xs flex items-center justify-center">+' . $remainingMembers . '</div>';
        }
        
        // Generar HTML de la fila
        return <<<HTML
        <tr>
            <td class="px-4 py-3 border-b">
                <div class="flex items-center">
                    <div class="mr-3 flex-shrink-0">
                        <div class="w-10 h-10 rounded-lg bg-sky-100 flex items-center justify-center text-sky-500">
                            <span class="text-lg font-semibold">{$this->name[0]}</span>
                        </div>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">{$this->name}</div>
                        <div class="text-sm text-gray-500">{$this->description}</div>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3 border-b">
                <span class="badge {$statusClass}">{$this->status}</span>
            </td>
            <td class="px-4 py-3 border-b">
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-sky-500 h-2 rounded-full" style="width: {$this->progress}%"></div>
                </div>
                <div class="text-xs text-right mt-1">{$this->progress}%</div>
            </td>
            <td class="px-4 py-3 border-b">{$this->startDate}</td>
            <td class="px-4 py-3 border-b">{$this->dueDate}</td>
            <td class="px-4 py-3 border-b">
                <div class="flex flex-wrap">{$tagsHtml}</div>
            </td>
            <td class="px-4 py-3 border-b">
                <div class="flex items-center">{$teamHtml}</div>
            </td>
            <td class="px-4 py-3 border-b text-right">
                <button class="text-gray-500 hover:text-sky-500">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </td>
        </tr>
HTML;
    }
    
    /**
     * Devuelve una representación HTML para mostrar el proyecto como tarjeta
     * 
     * @return string HTML
     */
    public function toCard() {
        // Definir el color del badge según el estado
        $statusClass = '';
        switch ($this->status) {
            case 'completed':
                $statusClass = 'badge-success';
                break;
            case 'active':
                $statusClass = 'badge-info';
                break;
            case 'on-hold':
                $statusClass = 'badge-warning';
                break;
            case 'cancelled':
                $statusClass = 'badge-danger';
                break;
            default:
                $statusClass = 'badge-secondary';
        }
        
        // Formatear tags
        $tagsHtml = '';
        foreach ($this->tags as $tag) {
            $tagsHtml .= '<span class="badge bg-gray-200 text-gray-800 mr-1">' . htmlspecialchars($tag) . '</span>';
        }
        
        // Formatear miembros del equipo
        $teamHtml = '';
        $maxDisplayMembers = 3;
        $displayMembers = array_slice($this->team, 0, $maxDisplayMembers);
        
        foreach ($displayMembers as $member) {
            $initials = isset($member['name']) ? strtoupper(substr($member['name'], 0, 1)) : '?';
            $teamHtml .= '<div class="w-8 h-8 rounded-full bg-sky-500 text-white text-xs flex items-center justify-center mr-1">' . $initials . '</div>';
        }
        
        // Si hay más miembros de los que mostramos
        $remainingMembers = count($this->team) - $maxDisplayMembers;
        if ($remainingMembers > 0) {
            $teamHtml .= '<div class="w-8 h-8 rounded-full bg-gray-300 text-gray-700 text-xs flex items-center justify-center">+' . $remainingMembers . '</div>';
        }
        
        // Generar HTML de la tarjeta
        return <<<HTML
        <div class="card hover:shadow-md transition-all">
            <div class="card-header flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-sky-100 flex items-center justify-center text-sky-500 mr-3">
                        <span class="text-lg font-semibold">{$this->name[0]}</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">{$this->name}</h3>
                </div>
                <span class="badge {$statusClass}">{$this->status}</span>
            </div>
            <div class="card-body">
                <p class="text-gray-600 mb-4">{$this->description}</p>
                
                <div class="mb-4">
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700">Progreso</span>
                        <span class="text-sm font-medium text-gray-700">{$this->progress}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-sky-500 h-2 rounded-full" style="width: {$this->progress}%"></div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <span class="text-sm text-gray-500">Fecha inicio</span>
                        <p class="font-medium">{$this->startDate}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">Fecha límite</span>
                        <p class="font-medium">{$this->dueDate}</p>
                    </div>
                </div>
                
                <div class="flex flex-wrap mb-4">
                    {$tagsHtml}
                </div>
            </div>
            <div class="card-footer flex justify-between items-center">
                <div class="flex items-center">
                    {$teamHtml}
                </div>
                <button class="btn btn-sm btn-primary">Ver detalles</button>
            </div>
        </div>
HTML;
    }
}
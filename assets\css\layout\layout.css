/**
 * Estilos de layout y estructura de la aplicación
 */

/* Contenedor principal */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
}

/* Estructura principal (sidebar + contenido) */
.app-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Contenedor del sidebar */
.sidebar-container {
    flex-shrink: 0;
    position: relative;
    z-index: 40;
}

/* Área de contenido principal */
.main-content {
    flex: 1;
    overflow-y: auto;
    background-color: var(--app-background-alt);
}

/* Contenedor interno para el contenido principal */
.main-content-inner {
    padding: 1.5rem;
    width: 100%;
}

/* Cabecera fija */
.header-container {
    flex-shrink: 0;
    position: relative;
    z-index: 50;
}

/* Diseño responsivo */
@media (max-width: 768px) {
    .sidebar-container {
        position: absolute;
        height: 100%;
        z-index: 50;
    }
    
    .main-content-inner {
        padding: 1rem;
    }
}
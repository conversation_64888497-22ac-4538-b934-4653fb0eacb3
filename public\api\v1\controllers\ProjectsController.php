<?php
/**
 * Controlador de Proyectos
 * 
 * Maneja las solicitudes relacionadas con proyectos
 * 
 * <AUTHOR> with <PERSON> Code
 * @version 1.0
 */

require_once __DIR__ . '/../../../../services/api/models/Project.php';

class ProjectsController {
    /**
     * Obtiene todos los proyectos
     * 
     * @param array $params Parámetros de consulta
     * @return array Respuesta con los proyectos
     */
    public function getAll($params = []) {
        try {
            $db = getDbConnection();
            
            // Construir la consulta SQL base
            $sql = "SELECT p.*, u.name as owner_name 
                   FROM projects p 
                   LEFT JOIN users u ON p.owner_id = u.id";
            
            // Preparar los valores para los filtros
            $whereConditions = [];
            $queryParams = [];
            
            // Filtrar por estado si se especifica
            if (!empty($params['status'])) {
                $whereConditions[] = "p.status = :status";
                $queryParams[':status'] = $params['status'];
            }
            
            // Filtrar por etiquetas si se especifican
            if (!empty($params['tag'])) {
                $whereConditions[] = "p.id IN (SELECT project_id FROM project_tags WHERE tag = :tag)";
                $queryParams[':tag'] = $params['tag'];
            }
            
            // Filtrar por propietario si se especifica
            if (!empty($params['owner_id'])) {
                $whereConditions[] = "p.owner_id = :owner_id";
                $queryParams[':owner_id'] = $params['owner_id'];
            }
            
            // Aplicar condiciones WHERE si existen
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            // Ordenar resultados
            $orderBy = !empty($params['sort']) ? $params['sort'] : 'p.created_at';
            $direction = !empty($params['order']) && strtolower($params['order']) === 'asc' ? 'ASC' : 'DESC';
            $sql .= " ORDER BY {$orderBy} {$direction}";
            
            // Paginación
            $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
            $limit = isset($params['limit']) ? max(1, min(100, intval($params['limit']))) : 10;
            $offset = ($page - 1) * $limit;
            
            $sql .= " LIMIT :limit OFFSET :offset";
            $queryParams[':limit'] = $limit;
            $queryParams[':offset'] = $offset;
            
            // Ejecutar la consulta
            $stmt = $db->prepare($sql);
            foreach ($queryParams as $param => $value) {
                $stmt->bindValue($param, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
            }
            $stmt->execute();
            
            $projectsData = $stmt->fetchAll();
            
            // Contar total de resultados para paginación
            $countSql = "SELECT COUNT(*) FROM projects p";
            if (!empty($whereConditions)) {
                $countSql .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            $countStmt = $db->prepare($countSql);
            foreach ($queryParams as $param => $value) {
                if ($param !== ':limit' && $param !== ':offset') {
                    $countStmt->bindValue($param, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
                }
            }
            $countStmt->execute();
            $totalCount = $countStmt->fetchColumn();
            
            // Obtener etiquetas para cada proyecto
            $projects = [];
            foreach ($projectsData as $projectData) {
                $project = Project::fromApiResponse($projectData);
                
                // Obtener etiquetas
                $tagsSql = "SELECT tag FROM project_tags WHERE project_id = :project_id";
                $tagsStmt = $db->prepare($tagsSql);
                $tagsStmt->bindValue(':project_id', $project->id, PDO::PARAM_INT);
                $tagsStmt->execute();
                $project->tags = $tagsStmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Obtener miembros del equipo
                $teamSql = "SELECT u.id, u.name, u.avatar FROM project_members pm 
                           JOIN users u ON pm.user_id = u.id 
                           WHERE pm.project_id = :project_id";
                $teamStmt = $db->prepare($teamSql);
                $teamStmt->bindValue(':project_id', $project->id, PDO::PARAM_INT);
                $teamStmt->execute();
                $project->team = $teamStmt->fetchAll();
                
                $projects[] = $project;
            }
            
            return [
                'data' => $projects,
                'meta' => [
                    'total' => $totalCount,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($totalCount / $limit)
                ]
            ];
            
        } catch (Exception $e) {
            throw new Exception('Error al obtener proyectos: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Obtiene un proyecto específico por su ID
     * 
     * @param int $id ID del proyecto
     * @param array $params Parámetros adicionales
     * @return array Respuesta con el proyecto
     */
    public function get($id, $params = []) {
        try {
            $db = getDbConnection();
            
            // Obtener datos del proyecto
            $sql = "SELECT p.*, u.name as owner_name 
                   FROM projects p 
                   LEFT JOIN users u ON p.owner_id = u.id 
                   WHERE p.id = :id";
            
            $stmt = $db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $projectData = $stmt->fetch();
            
            if (!$projectData) {
                throw new Exception('Proyecto no encontrado', 404);
            }
            
            $project = Project::fromApiResponse($projectData);
            
            // Obtener etiquetas
            $tagsSql = "SELECT tag FROM project_tags WHERE project_id = :project_id";
            $tagsStmt = $db->prepare($tagsSql);
            $tagsStmt->bindValue(':project_id', $project->id, PDO::PARAM_INT);
            $tagsStmt->execute();
            $project->tags = $tagsStmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Obtener miembros del equipo
            $teamSql = "SELECT u.id, u.name, u.avatar FROM project_members pm 
                       JOIN users u ON pm.user_id = u.id 
                       WHERE pm.project_id = :project_id";
            $teamStmt = $db->prepare($teamSql);
            $teamStmt->bindValue(':project_id', $project->id, PDO::PARAM_INT);
            $teamStmt->execute();
            $project->team = $teamStmt->fetchAll();
            
            // Obtener tareas si se solicitan
            if (isset($params['include']) && strpos($params['include'], 'tasks') !== false) {
                $tasksSql = "SELECT * FROM tasks WHERE project_id = :project_id ORDER BY due_date ASC";
                $tasksStmt = $db->prepare($tasksSql);
                $tasksStmt->bindValue(':project_id', $project->id, PDO::PARAM_INT);
                $tasksStmt->execute();
                $project->tasks = $tasksStmt->fetchAll();
            }
            
            return ['data' => $project];
            
        } catch (Exception $e) {
            throw new Exception('Error al obtener proyecto: ' . $e->getMessage(), 
                                $e->getCode() >= 400 && $e->getCode() < 600 ? $e->getCode() : 500);
        }
    }
    
    /**
     * Crea un nuevo proyecto
     * 
     * @param array $data Datos del proyecto a crear
     * @return array Respuesta con el proyecto creado
     */
    public function create($data) {
        try {
            // Validar datos requeridos
            if (empty($data['name'])) {
                throw new Exception('El nombre del proyecto es requerido', 400);
            }
            
            $db = getDbConnection();
            $db->beginTransaction();
            
            // Crear el proyecto
            $sql = "INSERT INTO projects (name, description, status, progress, start_date, due_date, owner_id, icon, created_at, updated_at) 
                   VALUES (:name, :description, :status, :progress, :start_date, :due_date, :owner_id, :icon, NOW(), NOW())";
            
            $stmt = $db->prepare($sql);
            $stmt->bindValue(':name', $data['name'], PDO::PARAM_STR);
            $stmt->bindValue(':description', $data['description'] ?? '', PDO::PARAM_STR);
            $stmt->bindValue(':status', $data['status'] ?? 'pending', PDO::PARAM_STR);
            $stmt->bindValue(':progress', $data['progress'] ?? 0, PDO::PARAM_INT);
            $stmt->bindValue(':start_date', $data['start_date'] ?? null, PDO::PARAM_STR);
            $stmt->bindValue(':due_date', $data['due_date'] ?? null, PDO::PARAM_STR);
            $stmt->bindValue(':owner_id', $data['owner_id'] ?? null, PDO::PARAM_INT);
            $stmt->bindValue(':icon', $data['icon'] ?? null, PDO::PARAM_STR);
            
            $stmt->execute();
            $projectId = $db->lastInsertId();
            
            // Agregar etiquetas si existen
            if (!empty($data['tags']) && is_array($data['tags'])) {
                $tagSql = "INSERT INTO project_tags (project_id, tag) VALUES (:project_id, :tag)";
                $tagStmt = $db->prepare($tagSql);
                
                foreach ($data['tags'] as $tag) {
                    $tagStmt->bindValue(':project_id', $projectId, PDO::PARAM_INT);
                    $tagStmt->bindValue(':tag', $tag, PDO::PARAM_STR);
                    $tagStmt->execute();
                }
            }
            
            // Agregar miembros del equipo si existen
            if (!empty($data['team']) && is_array($data['team'])) {
                $teamSql = "INSERT INTO project_members (project_id, user_id, role) VALUES (:project_id, :user_id, :role)";
                $teamStmt = $db->prepare($teamSql);
                
                foreach ($data['team'] as $member) {
                    if (empty($member['user_id'])) continue;
                    
                    $teamStmt->bindValue(':project_id', $projectId, PDO::PARAM_INT);
                    $teamStmt->bindValue(':user_id', $member['user_id'], PDO::PARAM_INT);
                    $teamStmt->bindValue(':role', $member['role'] ?? 'member', PDO::PARAM_STR);
                    $teamStmt->execute();
                }
            }
            
            $db->commit();
            
            // Obtener el proyecto recién creado
            return $this->get($projectId);
            
        } catch (Exception $e) {
            if (isset($db) && $db->inTransaction()) {
                $db->rollBack();
            }
            throw new Exception('Error al crear proyecto: ' . $e->getMessage(), 
                                $e->getCode() >= 400 && $e->getCode() < 600 ? $e->getCode() : 500);
        }
    }
    
    /**
     * Actualiza un proyecto existente
     * 
     * @param int $id ID del proyecto a actualizar
     * @param array $data Datos actualizados del proyecto
     * @return array Respuesta con el proyecto actualizado
     */
    public function update($id, $data) {
        try {
            $db = getDbConnection();
            
            // Verificar que el proyecto existe
            $checkSql = "SELECT id FROM projects WHERE id = :id";
            $checkStmt = $db->prepare($checkSql);
            $checkStmt->bindValue(':id', $id, PDO::PARAM_INT);
            $checkStmt->execute();
            
            if (!$checkStmt->fetch()) {
                throw new Exception('Proyecto no encontrado', 404);
            }
            
            $db->beginTransaction();
            
            // Construir la consulta SQL de actualización
            $updateFields = [];
            $params = [':id' => $id];
            
            $fields = [
                'name', 'description', 'status', 'progress', 
                'start_date', 'due_date', 'owner_id', 'icon'
            ];
            
            foreach ($fields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            // Siempre actualizar el campo updated_at
            $updateFields[] = "updated_at = NOW()";
            
            if (empty($updateFields)) {
                throw new Exception('No se proporcionaron datos para actualizar', 400);
            }
            
            $sql = "UPDATE projects SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $db->prepare($sql);
            
            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value, 
                                 is_int($value) ? PDO::PARAM_INT : 
                                 (is_null($value) ? PDO::PARAM_NULL : PDO::PARAM_STR));
            }
            
            $stmt->execute();
            
            // Actualizar etiquetas si se proporcionaron
            if (isset($data['tags'])) {
                // Eliminar etiquetas existentes
                $deleteTagsSql = "DELETE FROM project_tags WHERE project_id = :project_id";
                $deleteTagsStmt = $db->prepare($deleteTagsSql);
                $deleteTagsStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
                $deleteTagsStmt->execute();
                
                // Agregar nuevas etiquetas
                if (!empty($data['tags']) && is_array($data['tags'])) {
                    $tagSql = "INSERT INTO project_tags (project_id, tag) VALUES (:project_id, :tag)";
                    $tagStmt = $db->prepare($tagSql);
                    
                    foreach ($data['tags'] as $tag) {
                        $tagStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
                        $tagStmt->bindValue(':tag', $tag, PDO::PARAM_STR);
                        $tagStmt->execute();
                    }
                }
            }
            
            // Actualizar miembros del equipo si se proporcionaron
            if (isset($data['team'])) {
                // Eliminar miembros existentes
                $deleteTeamSql = "DELETE FROM project_members WHERE project_id = :project_id";
                $deleteTeamStmt = $db->prepare($deleteTeamSql);
                $deleteTeamStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
                $deleteTeamStmt->execute();
                
                // Agregar nuevos miembros
                if (!empty($data['team']) && is_array($data['team'])) {
                    $teamSql = "INSERT INTO project_members (project_id, user_id, role) VALUES (:project_id, :user_id, :role)";
                    $teamStmt = $db->prepare($teamSql);
                    
                    foreach ($data['team'] as $member) {
                        if (empty($member['user_id'])) continue;
                        
                        $teamStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
                        $teamStmt->bindValue(':user_id', $member['user_id'], PDO::PARAM_INT);
                        $teamStmt->bindValue(':role', $member['role'] ?? 'member', PDO::PARAM_STR);
                        $teamStmt->execute();
                    }
                }
            }
            
            $db->commit();
            
            // Obtener el proyecto actualizado
            return $this->get($id);
            
        } catch (Exception $e) {
            if (isset($db) && $db->inTransaction()) {
                $db->rollBack();
            }
            throw new Exception('Error al actualizar proyecto: ' . $e->getMessage(), 
                                $e->getCode() >= 400 && $e->getCode() < 600 ? $e->getCode() : 500);
        }
    }
    
    /**
     * Elimina un proyecto
     * 
     * @param int $id ID del proyecto a eliminar
     * @return array Respuesta indicando éxito
     */
    public function delete($id) {
        try {
            $db = getDbConnection();
            
            // Verificar que el proyecto existe
            $checkSql = "SELECT id FROM projects WHERE id = :id";
            $checkStmt = $db->prepare($checkSql);
            $checkStmt->bindValue(':id', $id, PDO::PARAM_INT);
            $checkStmt->execute();
            
            if (!$checkStmt->fetch()) {
                throw new Exception('Proyecto no encontrado', 404);
            }
            
            $db->beginTransaction();
            
            // Eliminar etiquetas del proyecto
            $deleteTagsSql = "DELETE FROM project_tags WHERE project_id = :project_id";
            $deleteTagsStmt = $db->prepare($deleteTagsSql);
            $deleteTagsStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
            $deleteTagsStmt->execute();
            
            // Eliminar miembros del equipo
            $deleteTeamSql = "DELETE FROM project_members WHERE project_id = :project_id";
            $deleteTeamStmt = $db->prepare($deleteTeamSql);
            $deleteTeamStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
            $deleteTeamStmt->execute();
            
            // Eliminar tareas asociadas
            $deleteTasksSql = "DELETE FROM tasks WHERE project_id = :project_id";
            $deleteTasksStmt = $db->prepare($deleteTasksSql);
            $deleteTasksStmt->bindValue(':project_id', $id, PDO::PARAM_INT);
            $deleteTasksStmt->execute();
            
            // Eliminar el proyecto
            $sql = "DELETE FROM projects WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $db->commit();
            
            return [
                'success' => true,
                'message' => 'Proyecto eliminado correctamente'
            ];
            
        } catch (Exception $e) {
            if (isset($db) && $db->inTransaction()) {
                $db->rollBack();
            }
            throw new Exception('Error al eliminar proyecto: ' . $e->getMessage(), 
                                $e->getCode() >= 400 && $e->getCode() < 600 ? $e->getCode() : 500);
        }
    }
}
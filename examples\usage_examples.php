<?php
/**
 * Ejemplos de Uso del Sistema de Base de Datos
 * 
 * Este archivo contiene ejemplos prácticos de cómo usar:
 * - DatabaseManager
 * - QueryBuilder  
 * - BaseModel
 * - Transacciones
 * - Validaciones
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

require_once __DIR__ . '/../public/api/config/config.php';
require_once __DIR__ . '/UserModel.php';

// ============================================================================
// 1. EJEMPLOS BÁSICOS CON DATABASEMANAGER
// ============================================================================

try {
    echo "=== EJEMPLOS CON DATABASEMANAGER ===\n\n";
    
    // Obtener instancia del DatabaseManager
    $db = getDatabaseManager();
    
    // Ejemplo 1: Query simple
    echo "1. Query simple:\n";
    $stmt = $db->query("SELECT COUNT(*) as total FROM users WHERE status = ?", ['active']);
    $result = $stmt->fetch();
    echo "Usuarios activos: " . $result['total'] . "\n\n";
    
    // Ejemplo 2: Inserción
    echo "2. Inserción con prepared statement:\n";
    $userData = [
        'name' => 'Juan Pérez',
        'email' => '<EMAIL>',
        'password' => password_hash('123456789', PASSWORD_BCRYPT),
        'status' => 'active',
        'role' => 'user',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $sql = "INSERT INTO users (name, email, password, status, role, created_at) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $db->query($sql, array_values($userData));
    echo "Usuario insertado con ID: " . $db->lastInsertId() . "\n\n";
    
    // Ejemplo 3: Transacción
    echo "3. Ejemplo de transacción:\n";
    $result = $db->transaction(function($db) {
        // Crear usuario
        $userSql = "INSERT INTO users (name, email, password, status, role, created_at) VALUES (?, ?, ?, ?, ?, ?)";
        $db->query($userSql, ['María García', '<EMAIL>', password_hash('password123', PASSWORD_BCRYPT), 'active', 'user', date('Y-m-d H:i:s')]);
        $userId = $db->lastInsertId();
        
        // Crear perfil asociado
        $profileSql = "INSERT INTO user_profiles (user_id, bio, location, created_at) VALUES (?, ?, ?, ?)";
        $db->query($profileSql, [$userId, 'Desarrolladora Full Stack', 'Madrid, España', date('Y-m-d H:i:s')]);
        
        return $userId;
    });
    echo "Transacción completada. Usuario creado con ID: $result\n\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// ============================================================================
// 2. EJEMPLOS CON QUERYBUILDER
// ============================================================================

try {
    echo "=== EJEMPLOS CON QUERYBUILDER ===\n\n";
    
    $db = getDatabaseManager();
    $qb = new QueryBuilder($db);
    
    // Ejemplo 1: Select simple
    echo "1. Select con WHERE:\n";
    $users = $qb->table('users')
                ->select('id', 'name', 'email')
                ->where('status', 'active')
                ->orderBy('name')
                ->get();
    
    foreach ($users as $user) {
        echo "- {$user['name']} ({$user['email']})\n";
    }
    echo "\n";
    
    // Ejemplo 2: Query compleja con JOINs
    echo "2. Query con JOIN:\n";
    $usersWithProfiles = $qb->table('users')
                           ->select('users.name', 'users.email', 'user_profiles.bio', 'user_profiles.location')
                           ->leftJoin('user_profiles', 'users.id', '=', 'user_profiles.user_id')
                           ->where('users.status', 'active')
                           ->whereNotNull('user_profiles.bio')
                           ->get();
    
    foreach ($usersWithProfiles as $user) {
        echo "- {$user['name']}: {$user['bio']} - {$user['location']}\n";
    }
    echo "\n";
    
    // Ejemplo 3: Agregación y agrupación
    echo "3. Agregación por rol:\n";
    $roleStats = $qb->table('users')
                    ->select('role', 'COUNT(*) as total')
                    ->where('status', 'active')
                    ->groupBy('role')
                    ->orderByDesc('total')
                    ->get();
    
    foreach ($roleStats as $stat) {
        echo "- {$stat['role']}: {$stat['total']} usuarios\n";
    }
    echo "\n";
    
    // Ejemplo 4: Paginación
    echo "4. Paginación:\n";
    $paginatedUsers = $qb->table('users')
                         ->select('id', 'name', 'email', 'role')
                         ->where('status', 'active')
                         ->orderBy('name')
                         ->paginate(1, 5); // Página 1, 5 por página
    
    echo "Página {$paginatedUsers['current_page']} de {$paginatedUsers['last_page']}\n";
    echo "Total de registros: {$paginatedUsers['total']}\n";
    foreach ($paginatedUsers['data'] as $user) {
        echo "- {$user['name']} ({$user['role']})\n";
    }
    echo "\n";
    
    // Ejemplo 5: Búsqueda con LIKE
    echo "5. Búsqueda:\n";
    $searchResults = $qb->table('users')
                        ->select('name', 'email')
                        ->where('name', 'like', '%juan%')
                        ->orWhere('email', 'like', '%juan%')
                        ->limit(10)
                        ->get();
    
    echo "Resultados para 'juan':\n";
    foreach ($searchResults as $result) {
        echo "- {$result['name']} ({$result['email']})\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// ============================================================================
// 3. EJEMPLOS CON BASEMODEL (ORM)
// ============================================================================

try {
    echo "=== EJEMPLOS CON BASEMODEL (ORM) ===\n\n";
    
    // Ejemplo 1: Crear usuario
    echo "1. Crear usuario con modelo:\n";
    $user = new UserModel([
        'name' => 'Ana Silva',
        'email' => '<EMAIL>',
        'phone' => '123456789',
        'status' => 'active',
        'role' => 'editor'
    ]);
    
    $user->setPassword('password123');
    
    if ($user->save()) {
        echo "Usuario creado exitosamente con ID: {$user->id}\n";
    } else {
        echo "Error al crear usuario\n";
    }
    echo "\n";
    
    // Ejemplo 2: Buscar usuario
    echo "2. Buscar usuario por ID:\n";
    $foundUser = UserModel::find(1);
    if ($foundUser) {
        echo "Usuario encontrado: {$foundUser->name} ({$foundUser->email})\n";
        echo "Es admin: " . ($foundUser->isAdmin() ? 'Sí' : 'No') . "\n";
        echo "Está activo: " . ($foundUser->isActive() ? 'Sí' : 'No') . "\n";
    } else {
        echo "Usuario no encontrado\n";
    }
    echo "\n";
    
    // Ejemplo 3: Actualizar usuario
    echo "3. Actualizar usuario:\n";
    if ($foundUser) {
        $foundUser->phone = '987654321';
        $foundUser->status = 'active';
        
        if ($foundUser->save()) {
            echo "Usuario actualizado exitosamente\n";
        } else {
            echo "Error al actualizar usuario\n";
        }
    }
    echo "\n";
    
    // Ejemplo 4: Query con scopes
    echo "4. Usar scopes personalizados:\n";
    
    // Usuarios activos
    $activeUsers = UserModel::query()->active()->get();
    echo "Usuarios activos: " . count($activeUsers) . "\n";
    
    // Usuarios por rol
    $admins = UserModel::query()->byRole('admin')->get();
    echo "Administradores: " . count($admins) . "\n";
    
    // Búsqueda
    $searchResults = UserModel::query()->search('ana')->limit(5)->get();
    echo "Resultados de búsqueda para 'ana': " . count($searchResults) . "\n";
    echo "\n";
    
    // Ejemplo 5: Conversión a array/JSON
    echo "5. Serialización:\n";
    if ($foundUser) {
        echo "Array:\n";
        print_r($foundUser->toArray());
        
        echo "\nJSON:\n";
        echo $foundUser->toJson() . "\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// ============================================================================
// 4. EJEMPLOS AVANZADOS: TRANSACCIONES Y MANEJO DE ERRORES
// ============================================================================

try {
    echo "=== EJEMPLOS AVANZADOS ===\n\n";
    
    $db = getDatabaseManager();
    
    // Ejemplo 1: Transacción compleja
    echo "1. Transacción compleja con rollback:\n";
    
    try {
        $result = $db->transaction(function($db) {
            // Crear usuario
            $user = UserModel::create([
                'name' => 'Carlos Rodríguez',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'status' => 'active',
                'role' => 'user'
            ]);
            
            // Crear proyecto (simulamos error)
            if (rand(1, 2) === 1) {
                throw new Exception("Error simulado en creación de proyecto");
            }
            
            // Si llegamos aquí, todo OK
            return $user->id;
        });
        
        echo "Transacción completada exitosamente. Usuario ID: $result\n";
        
    } catch (Exception $e) {
        echo "Transacción falló y se revirtió: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // Ejemplo 2: Manejo de reconexión
    echo "2. Estadísticas de conexión:\n";
    $stats = $db->getStats();
    print_r($stats);
    echo "\n";
    
    // Ejemplo 3: Log de queries (solo en desarrollo)
    echo "3. Log de queries:\n";
    if (API_ENVIRONMENT === 'development') {
        $queryLog = $db->getQueryLog();
        echo "Queries ejecutadas: " . count($queryLog) . "\n";
        
        if (!empty($queryLog)) {
            echo "Última query:\n";
            $lastQuery = end($queryLog);
            echo "SQL: " . $lastQuery['sql'] . "\n";
            echo "Tiempo: " . $lastQuery['execution_time'] . "\n";
        }
    } else {
        echo "Log de queries deshabilitado en producción\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n\n";
}

// ============================================================================
// 5. EJEMPLO DE API ENDPOINT
// ============================================================================

/**
 * Ejemplo de endpoint de API usando el nuevo sistema
 */
function getUsersApiEndpoint() {
    try {
        // Obtener parámetros
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $role = $_GET['role'] ?? '';
        $status = $_GET['status'] ?? 'active';
        
        // Construir query
        $query = UserModel::query()
                          ->select('id', 'name', 'email', 'role', 'status', 'created_at')
                          ->where('status', $status);
        
        // Aplicar filtros opcionales
        if (!empty($search)) {
            $query->search($search);
        }
        
        if (!empty($role)) {
            $query->byRole($role);
        }
        
        // Obtener resultados paginados
        $users = $query->orderBy('name')->paginate($page, 10);
        
        // Formatear respuesta
        return [
            'success' => true,
            'data' => [
                'users' => array_map(function($user) {
                    return (new UserModel($user))->toArray();
                }, $users['data']),
                'pagination' => [
                    'current_page' => $users['current_page'],
                    'per_page' => $users['per_page'],
                    'total' => $users['total'],
                    'last_page' => $users['last_page']
                ]
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Ejemplo de uso del endpoint
echo "=== EJEMPLO DE API ENDPOINT ===\n\n";
$_GET['search'] = 'juan';
$_GET['page'] = 1;

$apiResponse = getUsersApiEndpoint();
echo "Respuesta API:\n";
echo json_encode($apiResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== EJEMPLOS COMPLETADOS ===\n";
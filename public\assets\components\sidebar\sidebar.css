/**
 * FUSE React Sidebar - CSS Styles
 * 
 * Este archivo contiene los estilos CSS para el sidebar de FUSE React.
 * Complementa a Tailwind CSS y proporciona estilos personalizados.
 * 
 * <AUTHOR> with <PERSON>
 * @version 1.0
 */

/* ===== Variables CSS ===== */
:root {
    --fuse-primary: #0ea5e9;
    --fuse-primary-hover: #0284c7;
    --fuse-primary-light: #e0f2fe;
    --fuse-secondary: #64748b;
    --fuse-secondary-light: #f8fafc;
    --fuse-background: #ffffff;
    --fuse-background-alt: #f9fafb;
    --fuse-border: #f1f5f9;
    --fuse-text-primary: #1e293b;
    --fuse-text-secondary: #64748b;
    --fuse-text-muted: #94a3b8;
    --fuse-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --fuse-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --fuse-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --fuse-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --fuse-radius: 0.5rem;
    --fuse-radius-sm: 0.375rem;
    --fuse-radius-lg: 0.75rem;
    --fuse-transition: all 0.2s ease-in-out;
}

/* ===== Sidebar Container ===== */
.fuse-sidebar {
    width: 16rem;
    background-color: var(--fuse-background);
    border-right: 1px solid var(--fuse-border);
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: var(--fuse-shadow-sm);
    position: relative;
    overflow: hidden;
    z-index: 20;
}

/* ===== Header ===== */
.fuse-sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--fuse-border);
    background-color: var(--fuse-background);
}

.fuse-sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fuse-sidebar-logo-left,
.fuse-sidebar-logo-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.fuse-sidebar-logo-icon {
    width: 2rem;
    height: 2rem;
    background-color: var(--fuse-primary);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--fuse-shadow-sm);
}

.fuse-sidebar-logo-icon span {
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
}

.fuse-sidebar-logo-text {
    font-weight: 600;
    color: var(--fuse-text-primary);
    font-size: 0.875rem;
}

.fuse-sidebar-framework-icon {
    width: 1.5rem;
    height: 1.5rem;
    background-color: var(--fuse-text-primary);
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--fuse-shadow-sm);
}

.fuse-sidebar-framework-icon span {
    color: white;
    font-weight: bold;
    font-size: 0.75rem;
}

.fuse-sidebar-framework-text {
    font-size: 0.75rem;
    color: var(--fuse-text-secondary);
    font-weight: 500;
}

/* ===== Content ===== */
.fuse-sidebar-content {
    flex: 1;
    overflow-y: auto;
    background-color: rgba(249, 250, 251, 0.3);
}

.fuse-sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.fuse-sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.fuse-sidebar-content::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 3px;
}

.fuse-sidebar-content::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.5);
}

/* ===== Menu Sections ===== */
.fuse-sidebar-section {
    padding: 1rem;
}

.fuse-sidebar-section-header {
    margin-bottom: 0.75rem;
}

.fuse-sidebar-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--fuse-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.fuse-sidebar-section-subtitle {
    font-size: 0.75rem;
    color: var(--fuse-text-muted);
    margin-top: 0.25rem;
}

.fuse-sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* ===== Menu Items ===== */
.fuse-sidebar-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    transition: var(--fuse-transition);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: none;
    background: none;
    text-align: left;
    font-family: inherit;
}

.fuse-sidebar-item:hover {
    background-color: var(--fuse-secondary-light);
}

.fuse-sidebar-item:focus {
    outline: 2px solid var(--fuse-primary);
    outline-offset: 2px;
}

.fuse-sidebar-item.active {
    background-color: var(--fuse-primary-light);
    color: var(--fuse-primary);
    box-shadow: var(--fuse-shadow-sm);
}

.fuse-sidebar-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 0.25rem;
    transition: var(--fuse-transition);
    flex-shrink: 0;
}

.fuse-sidebar-item.active .fuse-sidebar-item-icon {
    color: var(--fuse-primary);
}

.fuse-sidebar-item:not(.active) .fuse-sidebar-item-icon {
    color: var(--fuse-text-secondary);
}

.fuse-sidebar-item:hover .fuse-sidebar-item-icon {
    color: var(--fuse-text-primary);
}

.fuse-sidebar-item-content {
    flex: 1;
    text-align: left;
    min-width: 0;
}

.fuse-sidebar-item-label {
    font-weight: 500;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fuse-sidebar-item-subtitle {
    font-size: 0.75rem;
    color: var(--fuse-text-muted);
    margin-top: 0.125rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fuse-sidebar-item-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: var(--fuse-primary-light);
    color: var(--fuse-primary);
    border: 1px solid rgba(59, 130, 246, 0.2);
    flex-shrink: 0;
}

.fuse-sidebar-item-chevron {
    transition: var(--fuse-transition);
    flex-shrink: 0;
}

.fuse-sidebar-item.active .fuse-sidebar-item-chevron {
    opacity: 1;
    transform: translateX(0);
}

.fuse-sidebar-item:not(.active) .fuse-sidebar-item-chevron {
    opacity: 0;
    transform: translateX(-0.5rem);
}

.fuse-sidebar-item-hover {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(219, 234, 254, 0.5), transparent);
    transition: opacity 0.2s ease-in-out;
    opacity: 0;
    pointer-events: none;
}

.fuse-sidebar-item:hover .fuse-sidebar-item-hover {
    opacity: 1;
}

/* ===== Footer ===== */
.fuse-sidebar-footer {
    border-top: 1px solid var(--fuse-border);
    background-color: var(--fuse-background);
    padding: 1rem;
}

.fuse-sidebar-help {
    margin-bottom: 1rem;
}

.fuse-sidebar-help-text {
    font-size: 0.875rem;
    color: var(--fuse-text-secondary);
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.fuse-sidebar-help-link {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    font-size: 0.875rem;
    color: var(--fuse-primary);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: var(--fuse-transition);
    font-weight: normal;
    background: none;
    border: none;
    cursor: pointer;
}

.fuse-sidebar-help-link:hover {
    color: var(--fuse-primary-hover);
    background-color: var(--fuse-primary-light);
}

.fuse-sidebar-divider {
    margin-bottom: 1rem;
    border-top: 1px solid var(--fuse-border);
}

.fuse-sidebar-contact {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.fuse-sidebar-contact-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(to bottom right, var(--fuse-primary), var(--fuse-primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 0 2px rgba(243, 244, 246, 1);
    flex-shrink: 0;
}

.fuse-sidebar-contact-avatar span {
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
}

.fuse-sidebar-contact-info {
    flex: 1;
    min-width: 0;
}

.fuse-sidebar-contact-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--fuse-text-primary);
    line-height: 1.25;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fuse-sidebar-contact-email {
    font-size: 0.75rem;
    color: var(--fuse-text-muted);
    line-height: 1.25;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .fuse-sidebar {
        width: 100%;
        max-width: 18rem;
        position: fixed;
        left: -100%;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease-in-out;
    }
    
    .fuse-sidebar.open {
        left: 0;
    }
    
    .fuse-sidebar-overlay {
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }
    
    .fuse-sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
}

/* ===== Animations ===== */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.fuse-sidebar.animate-in {
    animation: slideIn 0.3s ease-out;
}

.fuse-sidebar-item.animate-in {
    animation: scaleIn 0.2s ease-out;
}

/* ===== Accessibility ===== */
.fuse-sidebar-item:focus-visible {
    outline: 2px solid var(--fuse-primary);
    outline-offset: 2px;
}

.fuse-sidebar-item[aria-disabled="true"] {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===== Print Styles ===== */
@media print {
    .fuse-sidebar {
        display: none !important;
    }
}

/* ===== High Contrast Mode ===== */
@media (prefers-contrast: high) {
    :root {
        --fuse-border: #000000;
        --fuse-text-secondary: #000000;
        --fuse-text-muted: #666666;
    }
    
    .fuse-sidebar {
        border-width: 2px;
    }
    
    .fuse-sidebar-item:focus {
        outline-width: 3px;
    }
}

/* ===== Reduced Motion ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --fuse-background: #1f2937;
        --fuse-background-alt: #111827;
        --fuse-border: #374151;
        --fuse-text-primary: #f9fafb;
        --fuse-text-secondary: #d1d5db;
        --fuse-text-muted: #9ca3af;
        --fuse-secondary-light: #374151;
    }
    
    .fuse-sidebar-item.active {
        background-color: rgba(59, 130, 246, 0.2);
    }
    
    .fuse-sidebar-item-hover {
        background: linear-gradient(to right, rgba(59, 130, 246, 0.2), transparent);
    }
}

/* ===== Utility Classes ===== */
.fuse-sidebar-hidden {
    display: none !important;
}

.fuse-sidebar-visible {
    display: flex !important;
}

.fuse-sidebar-collapsed {
    width: 4rem !important;
}

.fuse-sidebar-collapsed .fuse-sidebar-section-title,
.fuse-sidebar-collapsed .fuse-sidebar-section-subtitle,
.fuse-sidebar-collapsed .fuse-sidebar-item-content,
.fuse-sidebar-collapsed .fuse-sidebar-item-chevron,
.fuse-sidebar-collapsed .fuse-sidebar-help,
.fuse-sidebar-collapsed .fuse-sidebar-contact-info {
    display: none !important;
}

.fuse-sidebar-collapsed .fuse-sidebar-item {
    justify-content: center;
    padding: 0.75rem;
}
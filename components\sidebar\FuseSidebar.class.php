<?php
/**
 * FUSE React Sidebar - PHP Version
 * 
 * Este componente recrea el sidebar de FUSE React para ser usado en proyectos PHP.
 * Mantiene el mismo diseño y funcionalidad que la versión de React.
 * 
 * <AUTHOR> with <PERSON> Code
 * @version 1.1
 */

class FuseSidebar {
    private $activeItem = 'project';
    private $menuItems;
    
    public function __construct($activeItem = 'project') {
        $this->activeItem = $activeItem;
        $this->menuItems = $this->getMenuItems();
    }
    
    private function getMenuItems() {
        return [
            'dashboards' => [
                [
                    'id' => 'project',
                    'label' => 'Project',
                    'icon' => 'layout-dashboard',
                    'title' => 'Project Dashboard'
                ],
                [
                    'id' => 'analytics', 
                    'label' => 'Analytics',
                    'icon' => 'trending-up',
                    'title' => 'Analytics Dashboard'
                ],
                [
                    'id' => 'finance',
                    'label' => 'Finance', 
                    'icon' => 'dollar-sign',
                    'title' => 'Finance Dashboard'
                ],
                [
                    'id' => 'crypto',
                    'label' => 'Crypto',
                    'icon' => 'bitcoin',
                    'title' => 'Crypto Dashboard'
                ]
            ],
            'applications' => [
                [
                    'id' => 'ai-image',
                    'label' => 'AI Image Generator',
                    'icon' => 'image',
                    'title' => 'AI Image Generator',
                    'isNew' => true
                ],
                [
                    'id' => 'academy',
                    'label' => 'Academy',
                    'icon' => 'graduation-cap',
                    'title' => 'Academy'
                ],
                [
                    'id' => 'calendar',
                    'label' => 'Calendar',
                    'icon' => 'calendar',
                    'title' => 'Calendar',
                    'subtitle' => '3 upcoming events'
                ],
                [
                    'id' => 'messenger',
                    'label' => 'Messenger',
                    'icon' => 'message-circle',
                    'title' => 'Messenger'
                ],
                [
                    'id' => 'contacts',
                    'label' => 'Contacts',
                    'icon' => 'users',
                    'title' => 'Contacts'
                ],
                [
                    'id' => 'ecommerce',
                    'label' => 'E-Commerce',
                    'icon' => 'shopping-cart',
                    'title' => 'E-Commerce'
                ],
                [
                    'id' => 'file-manager',
                    'label' => 'File Manager',
                    'icon' => 'folder-open',
                    'title' => 'File Manager'
                ],
                [
                    'id' => 'help-center',
                    'label' => 'Help Center',
                    'icon' => 'help-circle',
                    'title' => 'Help Center'
                ]
            ]
        ];
    }
    
    private function renderIcon($iconName) {
        // Mapeo de iconos a SVG de Lucide
        $icons = [
            'layout-dashboard' => $this->getLayoutDashboardIcon(),
            'trending-up' => $this->getTrendingUpIcon(),
            'dollar-sign' => $this->getDollarSignIcon(),
            'bitcoin' => $this->getBitcoinIcon(),
            'image' => $this->getImageIcon(),
            'graduation-cap' => $this->getGraduationCapIcon(),
            'calendar' => $this->getCalendarIcon(),
            'message-circle' => $this->getMessageCircleIcon(),
            'users' => $this->getUsersIcon(),
            'shopping-cart' => $this->getShoppingCartIcon(),
            'folder-open' => $this->getFolderOpenIcon(),
            'help-circle' => $this->getHelpCircleIcon(),
            'chevron-right' => $this->getChevronRightIcon(),
            'external-link' => $this->getExternalLinkIcon()
        ];
        
        return $icons[$iconName] ?? '';
    }
    
    private function getLayoutDashboardIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <rect x="3" y="3" width="7" height="7"></rect>
            <rect x="14" y="3" width="7" height="7"></rect>
            <rect x="14" y="14" width="7" height="7"></rect>
            <rect x="3" y="14" width="7" height="7"></rect>
        </svg>';
    }
    
    private function getTrendingUpIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
            <polyline points="16 7 22 7 22 13"></polyline>
        </svg>';
    }
    
    private function getDollarSignIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <line x1="12" y1="1" x2="12" y2="23"></line>
            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
        </svg>';
    }
    
    private function getBitcoinIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.52 18.921m6.247.168c-4.924-.869-6.14 6.025-1.216 6.893m1.216-6.893l6.247.168"></path>
            <path d="M13.983 5.081c-4.924-.868-6.14 6.025-1.216 6.894m1.216-6.894l6.247-.168m-6.247.168c4.924.869 6.14-6.025 1.216-6.893m-1.216 6.893l-6.247-.168"></path>
        </svg>';
    }
    
    private function getImageIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <circle cx="8.5" cy="8.5" r="1.5"></circle>
            <polyline points="21 15 16 10 5 21"></polyline>
        </svg>';
    }
    
    private function getGraduationCapIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
            <path d="M6 12v5c3 3 9 3 12 0v-5"></path>
        </svg>';
    }
    
    private function getCalendarIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
        </svg>';
    }
    
    private function getMessageCircleIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
        </svg>';
    }
    
    private function getUsersIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>';
    }
    
    private function getShoppingCartIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>';
    }
    
    private function getFolderOpenIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
        </svg>';
    }
    
    private function getHelpCircleIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>';
    }
    
    private function getChevronRightIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <polyline points="9 18 15 12 9 6"></polyline>
        </svg>';
    }
    
    private function getExternalLinkIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
            <polyline points="15 3 21 3 21 9"></polyline>
            <line x1="10" y1="14" x2="21" y2="3"></line>
        </svg>';
    }
    
    private function renderMenuItem($item, $isActive = false) {
        // Generar un ID único para cada instancia del elemento
        $uniqueId = $item['id'] . '_' . uniqid();
        
        $activeClass = $isActive ? 'bg-sky-50 text-sky-600 shadow-sm' : 'text-gray-600 hover:bg-gray-50';
        $iconClass = $isActive ? 'text-sky-500' : 'text-gray-400 group-hover:text-gray-600';
        $chevronClass = $isActive ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2';
        
        $isNewBadge = '';
        if (isset($item['isNew']) && $item['isNew']) {
            $isNewBadge = '<span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200 flex-shrink-0">NEW</span>';
        }
        
        $subtitle = '';
        if (isset($item['subtitle'])) {
            $subtitle = '<p class="text-xs text-gray-500 mt-0.5 truncate">' . htmlspecialchars($item['subtitle']) . '</p>';
        }
        
        return <<<HTML
        <button
            id="sidebar-item-{$uniqueId}"
            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden {$activeClass}"
            data-item-id="{$item['id']}"
            title="{$item['title']}"
        >
            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors {$iconClass}">
                {$this->renderIcon($item['icon'])}
            </div>
            <div class="flex-1 text-left min-w-0">
                <div class="flex items-center gap-2">
                    <span class="font-medium truncate">{$item['label']}</span>
                    {$isNewBadge}
                </div>
                {$subtitle}
            </div>
            <div class="transition-all duration-200 flex-shrink-0 {$chevronClass}">
                {$this->renderIcon('chevron-right')}
            </div>
            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-sky-50/50 to-transparent transition-opacity duration-200 opacity-0"></div>
        </button>
HTML;
    }
    
    private function renderMenuSection($title, $subtitle, $items, $type) {
        $menuItemsHtml = '';
        foreach ($items as $item) {
            $isActive = $this->activeItem === $item['id'];
            $menuItemsHtml .= $this->renderMenuItem($item, $isActive);
        }
        
        return <<<HTML
        <div class="p-4">
            <div class="mb-3">
                <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    {$title}
                </h3>
                <p class="text-xs text-gray-400 mt-1">
                    {$subtitle}
                </p>
            </div>
            <nav class="space-y-1 fuse-sidebar-{$type}">
                {$menuItemsHtml}
            </nav>
        </div>
HTML;
    }
    
    public function render() {
        $dashboardsSection = $this->renderMenuSection(
            'Dashboards',
            'Unique dashboard designs',
            $this->menuItems['dashboards'],
            'dashboards'
        );
        
        $applicationsSection = $this->renderMenuSection(
            'Applications',
            'Custom made application designs',
            $this->menuItems['applications'],
            'applications'
        );
        
        return <<<HTML
        <div class="fuse-sidebar w-64 bg-white border-r border-gray-100 flex flex-col h-full shadow-sm">
            <!-- Header -->


            <!-- Content -->
            <div class="flex-1 overflow-y-auto bg-gray-50/20">
                {$dashboardsSection}
                
                <div class="mx-4 border-t border-gray-200"></div>
                
                {$applicationsSection}
            </div>

            <!-- Footer -->
            <div class="border-t border-gray-200 bg-white p-4">
                <!-- Help Section -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2 leading-relaxed">
                        Need assistance to get started?
                    </p>
                    <button 
                        class="view-docs-btn w-full flex items-center gap-2 px-2 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors font-normal"
                    >
                        {$this->renderIcon('external-link')}
                        <span>View documentation</span>
                    </button>
                </div>

                <div class="mb-4 border-t border-gray-200"></div>

                <!-- Contact Info -->
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center ring-2 ring-gray-100">
                        <span class="text-white text-xs font-medium">AK</span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate leading-tight">
                            Abbott Keitch
                        </p>
                        <p class="text-xs text-gray-500 truncate leading-tight">
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
        </div>
HTML;
    }
    
    public function renderWithAssets() {
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FUSE Sidebar PHP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="sidebar.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {}
            }
        }
    </script>
    <!-- Cargar el archivo sidebar.js desde el principio -->
    <script src="sidebar.js"></script>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        {$this->render()}
        <main class="flex-1 flex items-center justify-center p-8">
            <div class="text-center max-w-2xl">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    Welcome to FUSE React - PHP Version
                </h1>
                <p class="text-xl text-gray-600 mb-8">
                    This is a recreation of the FUSE React sidebar interface using PHP
                </p>
                <p class="text-gray-500">
                    Select an option from the sidebar to navigate through different sections
                </p>
            </div>
        </main>
    </div>

    <script>
        // Solo configuramos variables iniciales sin definir funciones
        let initialActiveItem = '{$this->activeItem}';

        // Inicializar eventos de manera segura después de que el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            // Primero inicializamos el sidebar con la configuración
            if (typeof initFuseSidebar === 'function') {
                fuseSidebarInstance = initFuseSidebar({
                    activeItem: initialActiveItem,
                    navigationMode: 'spa',
                    baseUrl: '/',
                    debug: false
                });
            }

            // Luego asignamos eventos a los botones
            document.querySelectorAll('.view-docs-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    window.open('#', '_blank');
                });
            });
        });
    </script>
</body>
</html>
HTML;
    }
}
?>